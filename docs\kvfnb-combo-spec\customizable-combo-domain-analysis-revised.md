# Customizable Combo ProductType: Revised Domain Analysis

## Document Information
| Document Type | Domain Analysis |
| ------------- | --------------- |
| Version       | 1.2             |
| Status        | Updated         |
| Author        | Development Team |
| Last Updated  | April 18, 2024  |

## 1. Introduction

### 1.1 Purpose
This document provides a detailed domain analysis for implementing a new customizable combo product type in the KiotViet Product Management System. It explores the business requirements, key concepts, existing product types, and how the new customizable combo product type extends and differs from the current combo product functionality.

### 1.2 Scope
This analysis covers:
- Existing product type implementations in the codebase and database
- Business requirements for customizable combo products
- Key differences between standard combo (Type 1) and the new customizable combo (Type 4)
- Domain entities and their relationships
- Business rules specific to customizable combos
- Integration points with existing order processing

## 2. Current System Analysis

### 2.1 Existing Product Types
Based on the codebase analysis and database schema, the KiotViet system currently classifies products into the following types:

| ProductType | Description | Implementation |
|-------------|-------------|----------------|
| 1 | Combo Products | Products that are bundles of components with fixed structure |
| 2 | Regular Products | Standard inventory items sold as-is |
| 3 | Service Products | Non-physical offerings like consultations or memberships |
| 4 | Customizable Combo | Combos that allow customer selection from component groups |

The product type is represented in the codebase as an Enumeration type:

```csharp
public class ProductTypes : Enumeration
{
    public static readonly ProductTypes Combo = new(1, nameof(Combo).ToLowerInvariant(), "Combo - Đóng gói");
    public static readonly ProductTypes Normal = new(2, nameof(Normal).ToLowerInvariant(), "Hàng hóa");
    public static readonly ProductTypes Service = new(3, nameof(Service).ToLowerInvariant(), "Dịch vụ");
    public static readonly ProductTypes CustomizableCombo = new(4, nameof(CustomizableCombo).ToLowerInvariant(), "Combo tùy chỉnh");
}
```

There's also a ProductGroup classification in the system (Food = 1, Beverage = 2, Other = 3), which is primarily used for menu categorization.

### 2.2 Current Database Schema

#### 2.2.1 Primary Tables
The database schema includes the following key tables:

- **Product**: Core product entity with ProductType field indicating the type
- **ProductFormula**: Defines the components that make up a manufactured or combo product
  - Maps a product (ProductId) to its components (MaterialId) with quantity
  - Used for both manufactured products and current fixed combos

#### 2.2.2 Order Processing Tables
- **Order**: Main order header table
- **OrderDetail**: Line items in an order
  - Contains fields like ProductId, Quantity, Price, Discount
  - Has a field for Extra (NVARCHAR(MAX)) that could be used for customization data
  - Contains ToppingParentId to link topping items to their parent

### 2.3 Current Combo Products Implementation

The current combo product implementation has the following characteristics:

1. **Fixed Structure**: All components of a combo are predefined and fixed
2. **Formula-Based**: Components are defined in the ProductFormula table
3. **All-Inclusive**: When a combo is purchased, all its components are included
4. **Flat Structure**: No notion of component groups or selections
5. **No Customization**: Customers cannot modify component selection

### 2.4 Current Customization Mechanisms

The system does have some customization capabilities, but they're limited:

1. **Toppings**: Products can have toppings (IsTopping flag), which are additional items that can be added
   - Implemented via ToppingParentId in OrderDetail
   - Does not support grouped selections or quantity limits
2. **Extra Field**: OrderDetail has an Extra field (NVARCHAR(MAX)) that could potentially store JSON data for customizations

## 3. Customizable Combo Requirements

### 3.1 Business Need
There is a need for combos that allow customers to:
- Choose components within predefined groups (e.g., select any 1 main dish, any 2 sides)
- Influence final price through selection of premium components
- Have a consistent structure across all instances of the same combo

### 3.2 Key Characteristics

#### 3.2.1 Conceptual Model
- A customizable combo still has a base product (the combo itself)
- The combo is organized into logical groups (e.g., "Main Dish", "Side Options", "Drink Options")
- Each group contains multiple potential component items
- Each group has a maximum selection quantity (e.g., "select up to 2 side dishes")
- Component items can have additional prices (price additions)
- The combo has a base price, and selections with price additions increase the final price

#### 3.2.2 Key Differences from Standard Combo
| Aspect | Standard Combo (Type 1) | Customizable Combo (Type 4) |
|--------|-------------------------|-------------------|
| Component Selection | Fixed, predefined components | Customer selects from options within groups |
| Structure | Flat list of components | Hierarchical (groups containing items) |
| Pricing | Fixed price or simple discount | Base price + additional charges for premium selections |
| Inventory Impact | Reduces inventory for all components | Reduces inventory only for selected components |
| Storage Mechanism | ProductFormula table | CustomizableComboGroup and CustomizableComboGroupItem tables |

## 4. Domain Entities and Relationships

### 4.1 Core Entities

#### 4.1.1 Product (Existing Entity)
```csharp
Product
- Id: bigint (PK)
- Code: nvarchar(50)
- Name: nvarchar(255)
- ProductType: tinyint
- BasePrice: money
- (Other standard product fields)
```

No changes are needed to the Product entity itself. The ProductType value will be 4 for customizable combos.

#### 4.1.2 CustomizableComboGroup (New Entity)
```csharp
public class CustomizableComboGroup : Entity<long>, ICreatedAt, IModifiedAt, ICreatedBy, IModifiedBy
{
    // The ID of the combo product this group belongs to
    public long ComboProductId { get; private set; }
    
    // The name of the combo group (e.g., "Main Dish Options")
    public string Name { get; private set; } = string.Empty;
    
    // Optional description of the combo group
    public string? Description { get; private set; }
    
    // Maximum number of items that can be selected from this group
    public int? MaxQuantity { get; private set; }
    
    // Sort order for display purposes
    public int SortOrder { get; private set; }
    
    // When the group was created
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    // When the group was last modified
    public DateTime? ModifiedAt { get; set; }

    // The user ID that created this group
    public long CreatedBy { get; set; }
    
    // The user ID that last modified this group
    public long? ModifiedBy { get; set; }
    
    // The items in this combo group
    private readonly List<CustomizableComboGroupItem> _items = new List<CustomizableComboGroupItem>();
    public IReadOnlyCollection<CustomizableComboGroupItem> Items => _items.AsReadOnly();
}
```

The database mapping is defined as:

```csharp
public class CustomizableComboGroupEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomizableComboGroup>
{
    public override void Configure(EntityTypeBuilder<CustomizableComboGroup> builder)
    {
        base.Configure(builder);
        builder.ToTable(nameof(CustomizableComboGroup));
        
        builder.HasKey(g => g.Id);
        
        builder.Property(g => g.Id).HasColumnType(SqlServerColumnTypes.BIGINT);
        builder.Property(g => g.ComboProductId).HasColumnType(SqlServerColumnTypes.BIGINT).IsRequired();
        builder.Property(g => g.Name).HasColumnType(SqlServerColumnTypes.NVARCHAR125).IsRequired();
        builder.Property(g => g.Description).HasColumnType(SqlServerColumnTypes.NVARCHAR2000);
        builder.Property(g => g.MaxQuantity).HasColumnType(SqlServerColumnTypes.INT);
        builder.Property(g => g.SortOrder).HasColumnType(SqlServerColumnTypes.INT);
    }
}
```

#### 4.1.3 CustomizableComboGroupItem (New Entity)
```csharp
public class CustomizableComboGroupItem : Entity<long>, ICreatedAt, IModifiedAt, ICreatedBy, IModifiedBy
{
    // The ID of the group this item belongs to
    public long GroupId { get; private set; }
    
    // The ID of the product this item represents
    public long ProductId { get; private set; }
    
    // Additional price for this item
    public decimal AdditionalPrice { get; private set; }
    
    // Sort order for display purposes
    public int SortOrder { get; private set; }
    
    // When the item was created
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    // When the item was last modified
    public DateTime? ModifiedAt { get; set; }
    
    // The user ID that created this item
    public long CreatedBy { get; set; }
    
    // The user ID that last modified this item
    public long? ModifiedBy { get; set; }
}
```

The database mapping is defined as:

```csharp
public class CustomizableComboGroupItemEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomizableComboGroupItem>
{
    public override void Configure(EntityTypeBuilder<CustomizableComboGroupItem> builder)
    {
        base.Configure(builder);
        builder.ToTable(nameof(CustomizableComboGroupItem));
        
        builder.HasKey(i => i.Id);
        
        builder.Property(i => i.Id).HasColumnType(SqlServerColumnTypes.BIGINT);
        builder.Property(i => i.GroupId).HasColumnType(SqlServerColumnTypes.BIGINT);
        builder.Property(i => i.ProductId).HasColumnType(SqlServerColumnTypes.BIGINT);
        builder.Property(i => i.AdditionalPrice).HasColumnType(SqlServerColumnTypes.MONEY);
        builder.Property(i => i.SortOrder).HasColumnType(SqlServerColumnTypes.INT);
        
        // FK relationships are configured here
    }
}
```

### 4.2 Order Entities

To store customer selections, we need to track which components were selected for each combo. Two approaches are possible:

#### 4.2.1 Approach 1: Use Existing OrderDetail Structure
- Store combo as a main OrderDetail record
- Store selected components as separate OrderDetail records
- Link components to parent combo via ToppingParentId (repurposing this field)
- Leverage existing order processing logic

#### 4.2.2 Approach 2: JSON Serialization in Extra Field
- Store combo as a single OrderDetail record
- Store selection information as JSON in the Extra field
- Example JSON structure:
```json
{
  "selections": [
    {
      "group_id": 1,
      "group_name": "Main Dish",
      "items": [{ "product_id": 101, "price": 0 }]
    },
    {
      "group_id": 2,
      "group_name": "Sides",
      "items": [
        { "product_id": 201, "price": 0 },
        { "product_id": 202, "price": 3000 }
      ]
    }
  ]
}
```

The recommended approach is #1 (using existing OrderDetail structure) for the following reasons:
- Better compatibility with existing order processing logic
- Easier to query directly in database
- Consistent representation with existing POS order entry
- Leverages existing inventory management

## 5. Business Rules

### 5.1 Validation Rules
- A customizable combo must have at least one group
- Each group must have at least one item
- A component product can only appear once within a combo structure
- The maximum quantity for a group must be positive if specified
- Price additions cannot be negative
- Total price of the combo = base price + sum of selected component price additions
- The combo product must be active for sales
- Component products must be active and valid

### 5.2 Selection Rules
- Customer selections must not exceed the maximum quantity for each group
- Some groups may require a minimum number of selections (required flag)
- The system must validate customer selections before finalizing a sale

### 5.3 Inventory Impact
- Only selected components reduce inventory
- If a component is out of stock, it should be marked as unavailable for selection
- Inventory reservation applies to selected components only

## 6. Implementation Strategy

### 6.1 Database Changes
Create two new tables:
- CustomizableComboGroup
- CustomizableComboGroupItem

### 6.2 Domain Model Changes
Create new domain models:
- CustomizableComboGroup
- CustomizableComboGroupItem
- Various request/response DTOs for API interaction

### 6.3 API Endpoints
Implement two main API endpoints:
- GET /api/product/{productId}/combo-groups
- PUT /api/product/{productId}/combo-structure

### 6.4 Order Processing Changes
Modify order processing to:
- Recognize customizable combos
- Validate customer selections against group constraints
- Calculate final price correctly including any premium selections
- Track selected components in OrderDetail correctly

## 7. Compatibility Considerations

### 7.1 Backward Compatibility
The implementation maintains backward compatibility:
- Standard Combo (Type 1) and Customizable Combo (Type 4) are distinct product types
- Reports and analytics treat both combo types appropriately
- Order processing handles both standard and customizable combos appropriately

### 7.2 Feature Detection
The system will need to detect whether a combo is customizable or standard:
- If the product has ProductType = 4, it's a customizable combo
- If the product has ProductType = 1, it's a standard combo with fixed components in ProductFormula

## 8. Transaction Handling

When a customizable combo is ordered, the system needs to:

1. Create the main OrderDetail record for the combo
2. Create OrderDetail records for each selected component 
3. Link component records to the parent combo
4. Calculate and record the total price correctly
5. Apply inventory changes only for selected components
6. Handle any modifiers or notes attached to the components

## 9. Integration Points

### 9.1 Product Management
- Product creation and editing screens
- Product formula management

### 9.2 Inventory Management
- Inventory tracking for components
- Stock checking during ordering

### 9.3 Ordering System
- POS interface for combo customization
- Order detail processing and storage

### 9.4 Reporting
- Sales reports
- Inventory usage reports

## 10. Technical Considerations

### 10.1 Query Performance
- Optimize fetching of combo structure for quick rendering in POS
- Consider caching combo structures for frequently ordered items
- Index the CustomizableComboGroup and CustomizableComboGroupItem tables appropriately

### 10.2 Transaction Boundaries
- Ensure all operations related to creating/updating combo structures are wrapped in transactions
- Similarly, ensure order processing for customizable combos is atomic

### 10.3 Versioning
- Consider how to handle updates to combo structures for existing orders
- Track the combo structure version used for each order if necessary

## 11. Conclusion

The customizable combo feature will enhance the KiotViet system by allowing retailers to offer more flexible product combinations to their customers. By leveraging the existing product management and order processing systems with targeted extensions, the implementation maintains backward compatibility while adding significant new capabilities.

The approach outlined in this document provides a foundation for implementing customizable combos in a way that integrates well with the existing system architecture and business processes. The next steps involve detailed database design, domain model implementation, and API development. 