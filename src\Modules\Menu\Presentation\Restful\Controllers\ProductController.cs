using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductDetail;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetListProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateImages;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.DeleteProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductCombo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo;
using Microsoft.Extensions.DependencyInjection;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.SearchProduct;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Menu.Application.UseCases.BranchUseCase.GetBranchList;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProduct;
using System.Text.Json.Serialization;
using System.ComponentModel;
using KvFnB.Shared.Constants;
using System.Text.Json;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductSaleBranch;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ProductController : BaseController
    {
        private readonly ILogger _logger;

        private readonly IPermissionService _permissionService;
        private readonly IS3Service _s3Service;

        public ProductController(
            ILogger logger,
            IHttpContextAccessor httpContextAccessor,
            IPermissionService permissionService,
            IS3Service s3Service) : base(httpContextAccessor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _permissionService = permissionService ?? throw new ArgumentNullException(nameof(permissionService));
            _s3Service = s3Service ?? throw new ArgumentNullException(nameof(s3Service));
        }

        /// <summary>
        /// Gets a list of products with optional filtering and pagination.
        /// </summary>
        /// <param name="request">The request object containing search, sort, and pagination parameters.</param>
        /// <returns>A list of products matching the specified criteria.</returns>
        /// <response code="200">Returns the list of products.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Gets a list of products", Description = "Gets a list of products with optional filtering and pagination.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of products", typeof(IEnumerable<GetListProductResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [RequireAnyPermissions(PermissionConsts.Product_Read)]
        public async Task<IActionResult> GetProducts([FromQuery] GetListProductRequest request)
        {
            try
            {
                // Extract attribute parameters from request context if they weren't bound automatically
                if (request.ListAttributeFilter == null)
                {
                    var attributeFilters = new List<AttributeDto>();
                    var query = HttpContext.Request.Query;
                    
                    for (int i = 0; i < 100; i++) // Set a reasonable limit to avoid infinite loops
                    {
                        var idKey = $"attribute[{i}].id";
                        var nameKey = $"attribute[{i}].name";
                        var valueKey = $"attribute[{i}].value";
                        
                        if (query.ContainsKey(idKey) && query.ContainsKey(nameKey))
                        {
                            attributeFilters.Add(new AttributeDto 
                            {
                                Id = long.Parse(query[idKey].ToString()),
                                AttributeName = query[nameKey].ToString(),
                                AttributeValue = query.ContainsKey(valueKey) ? query[valueKey].ToString() : string.Empty
                            });
                        }
                        else
                        {
                            break; // No more attributes found
                        }
                    }
                    
                    if (attributeFilters.Count != 0)
                    {
                        request = request with { ListAttributeFilter = attributeFilters };
                    }
                }
                
                var getListProductUseCase = HttpContext.RequestServices.GetRequiredService<GetListProductUseCase>();
                var result = await getListProductUseCase.ExecuteAsync(request);
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.Error("Error in GetProducts", ex);
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Searches for products based on a search key with specific filtering rules.
        /// </summary>
        /// <remarks>
        /// Search rules:
        /// - Performs case-insensitive search on product name and code
        /// - Only returns non-deleted products (isDeleted is null or 0)
        /// - Product type must be either:
        ///   * Type 2 products
        ///   * Type 3 products with IsTimeType = false or null
        /// - Results are ordered by Id in descending order
        /// - Default limit is 50 products if not specified
        /// </remarks>
        /// <param name="request">The search request containing:
        /// - searchKey: Text to search in product name or code
        /// - limit: Optional. Maximum number of results to return (default: 50)
        /// </param>
        /// <returns>A list of products matching the search criteria.</returns>
        /// <response code="200">Returns the list of products.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("search")]
        [SwaggerOperation(Summary = "Searches for products based on a search key with specific filtering rules", Description = "Searches for products based on a search key with specific filtering rules.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of products", typeof(SearchProductResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [RequireAnyPermissions(PermissionConsts.Product_Read, PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> SearchProducts([FromQuery] SearchProductRequest request)
        {
            var searchProductUseCase = HttpContext.RequestServices.GetRequiredService<SearchProductUseCase>();
            var result = await searchProductUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result); 
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets the details of a specific product.
        /// </summary>
        /// <param name="id">The ID of the product to get details for.</param>
        /// <returns>The detailed information of the specified product.</returns>
        /// <response code="200">Returns the product details.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpGet("{id}")]
        [SwaggerOperation(Summary = "Gets product details", Description = "Gets detailed information about a specific product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the product details", typeof(GetProductDetailResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Read)]
        public async Task<IActionResult> GetProductDetail(long id)
        {
            var getProductDetailUseCase = HttpContext.RequestServices.GetRequiredService<GetProductDetailUseCase>();
            var request = new GetProductDetailRequest { ProductId = id };
            var result = await getProductDetailUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets the combo details of a product.
        /// </summary>
        /// <param name="id">The ID of the product to get combo details for.</param>
        /// <returns>The combo details of the specified product.</returns>
        /// <response code="200">Returns the product combo details.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found or if it's not a combo product.</response>
        [HttpGet("{id}/combo")]
        [SwaggerOperation(Summary = "Gets product combo details", Description = "Gets combo details for a specific product including customizable combo groups and items.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the product combo details", typeof(GetProductComboResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found or is not a customizable combo product")]
        [RequireAnyPermissions(PermissionConsts.Product_Read)]
        public async Task<IActionResult> GetProductCombo(long id)
        {
            var getProductComboUseCase = HttpContext.RequestServices.GetRequiredService<GetProductComboUseCase>();
            var request = new GetProductComboRequest { ProductId = id };
            var result = await getProductComboUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Creates a new product with image upload support.
        /// </summary>
        /// <param name="productJson">JSON string containing the product information (Form field name: 'product')</param>
        /// <returns>The newly created product.</returns>
        /// <response code="200">Returns the newly created product.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new product with image upload", Description = "Creates a new product with the provided details and uploads any attached images.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the newly created product", typeof(CreateProductResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [RequireAnyPermissions(PermissionConsts.Product_Create)]
        public async Task<IActionResult> CreateProduct()
        {
            try
            {
                // Get the product JSON from form data
                var productJson = HttpContext.Request.Form["product"];
                if (string.IsNullOrEmpty(productJson))
                {
                    return BadRequest("Product data is required");
                }

                // Deserialize the product information with custom JSON naming settings
                var serializerOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    // Remove PropertyNamingPolicy to avoid forcing any naming convention
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };
                
                var request = JsonSerializer.Deserialize<CreateProductRequest>(productJson!, serializerOptions);
                
                if (request == null)
                {
                    return BadRequest("Invalid product data format");
                }

                // Process uploaded images if any
                var images = await ProcessProductImagesAsync(HttpContext.Request.Form.Files);
                if (images.Any())
                {
                    request.Images = images;
                }

                // Add branch permissions if not admin
                if (!_permissionService.IsAdmin)
                {
                    await SetBranchPermissionsAsync(request);
                }

                // Execute the use case
                var createProductUseCase = HttpContext.RequestServices.GetRequiredService<CreateProductUseCase>();
                var result = await createProductUseCase.ExecuteAsync(request);
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.Error("Error creating product", ex);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while creating the product");
            }
        }

        /// <summary>
        /// Processes product images from the uploaded files
        /// </summary>
        /// <param name="files">Collection of uploaded files</param>
        /// <returns>List of image URLs</returns>
        private async Task<List<string>> ProcessProductImagesAsync(IFormFileCollection files)
        {
            if (files == null || files.Count == 0)
            {
                return new List<string>();
            }

            // Limit the number of images
            const int maxImageCount = 5;
            if (files.Count > maxImageCount)
            {
                throw new InvalidOperationException($"Maximum {maxImageCount} images allowed");
            }

            var imageUrls = new List<string>();
            
            foreach (var file in files)
            {
                if (file == null || file.Length <= 0)
                {
                    continue;
                }

                // Validate file type
                var allowedContentTypes = new[] { "image/jpeg", "image/png", "image/gif" };
                if (!allowedContentTypes.Contains(file.ContentType.ToLower()))
                {
                    _logger.Warning($"Invalid file type: {file.ContentType}");
                    continue;
                }

                try
                {
                    // Generate a unique key for the image
                    var now = DateTime.UtcNow;
                    var year = now.Year;
                    var month = now.Month.ToString().PadLeft(2, '0');
                    var key = $"products/{year}/{month}/{Guid.NewGuid():N}";

                    // Upload file directly to S3
                    using var stream = file.OpenReadStream();
                    var result = await _s3Service.UploadFileAsync(stream, key, file.ContentType);
                    
                    if (result.Success)
                    {
                        // Get the CloudFront URL from S3Configuration and combine with the key
                        var imageUrl = string.IsNullOrEmpty(result.PublicUrl) ? $"{_s3Service.GetObjectKey(key)}": result.PublicUrl;
                        imageUrls.Add(imageUrl);
                        _logger.Information($"Image uploaded successfully. Key: {key}");
                    }
                    else
                    {
                        _logger.Error($"Failed to upload image: {result.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"Error processing image file {file.FileName}", ex);
                }
            }

            return imageUrls;
        }

        /// <summary>
        /// Updates the basic information of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated basic information of the product.</param>
        /// <returns>The updated product basic information.</returns>
        /// <response code="200">Returns the updated product basic information.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}/basic-info")]
        [SwaggerOperation(Summary = "Updates product basic information", Description = "Updates the basic information of an existing product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product basic information", typeof(UpdateProductBasicInfoResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]

        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductBasicInfo(long id, [FromBody] UpdateProductBasicInfoRequest request)
        {
            var updateProductBasicInfoUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductBasicInfoUseCase>();
            request.ProductId = id;
            var result = await updateProductBasicInfoUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the combo details of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated combo details of the product.</param>
        /// <returns>The updated product combo details.</returns>
        /// <response code="200">Returns the updated product combo details.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found or is not a customizable combo product.</response>
        [HttpPut("{id}/combo")]
        [SwaggerOperation(Summary = "Updates product combo details", Description = "Updates the combo details of an existing product including customizable combo groups and items.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product combo details", typeof(UpdateProductComboResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found or is not a customizable combo product")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductCombo(long id, [FromBody] UpdateProductComboRequest request)
        {
            var updatedRequest = new UpdateProductComboRequest
            {
                ProductId = id,
                ComboGroups = request.ComboGroups
            };
            
            var updateProductComboUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductComboUseCase>();
            var result = await updateProductComboUseCase.ExecuteAsync(updatedRequest);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the images of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated images of the product.</param>
        /// <returns>The updated product images.</returns>
        /// <response code="200">Returns the updated product images.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}/images")]
        [SwaggerOperation(Summary = "Updates product images", Description = "Updates the images of an existing product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product images", typeof(UpdateProductImagesResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductImages(long id, [FromBody] UpdateProductImagesRequest request)
        {
            var updatedRequest = new UpdateProductImagesRequest
            {
                ProductId = id,
                Images = request.Images
            };
            
            var updateProductImagesUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductImagesUseCase>();
            var result = await updateProductImagesUseCase.ExecuteAsync(updatedRequest);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the on-hand quantity of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated on-hand quantity of the product.</param>
        /// <returns>The updated product on-hand quantity.</returns>
        /// <response code="200">Returns the updated product on-hand quantity.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}/on-hand-quantity")]
        [SwaggerOperation(Summary = "Updates product on-hand quantity", Description = "Updates the on-hand quantity of an existing product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product on-hand quantity", typeof(UpdateProductOnHandQuantityResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductOnHandQuantity(long id, [FromBody] UpdateProductOnHandQuantityRequest request)
        {
            var updatedRequest = new UpdateProductOnHandQuantityRequest
            {
                ProductId = id,
                OnHandQuantity = request.OnHandQuantity,
                MinQuantity = request.MinQuantity,
                MaxQuantity = request.MaxQuantity,
                Cost = request.Cost 
            };
            
            var updateProductOnHandQuantityUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductOnHandQuantityUseCase>();
            var result = await updateProductOnHandQuantityUseCase.ExecuteAsync(updatedRequest);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the price books of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated price books of the product.</param>
        /// <returns>The updated product price books.</returns>
        /// <response code="200">Returns the updated product price books.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}/price-books")]
        [SwaggerOperation(Summary = "Updates product price books", Description = "Updates the price books of an existing product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product price books", typeof(UpdateProductPriceBooksResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductPriceBooks(long id, [FromBody] UpdateProductPriceBooksRequest request)
        {
            var updatedRequest = new UpdateProductPriceBooksRequest
            {
                ProductId = id,
                PriceBookDetails = request.PriceBookDetails
            };
            
            var updateProductPriceBooksUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductPriceBooksUseCase>();
            var result = await updateProductPriceBooksUseCase.ExecuteAsync(updatedRequest);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the toppings of an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <param name="request">The request object containing the updated toppings of the product.</param>
        /// <returns>The updated product toppings.</returns>
        /// <response code="200">Returns the updated product toppings.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}/toppings")]
        [SwaggerOperation(Summary = "Updates product toppings", Description = "Updates the toppings of an existing product.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product toppings", typeof(UpdateProductToppingsResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProductToppings(long id, [FromBody] UpdateProductToppingsRequest request)
        {
            var updatedRequest = new UpdateProductToppingsRequest
            {
                ProductId = id,
                ToppingProductIds = request.ToppingProductIds
            };
            
            var updateProductToppingsUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductToppingsUseCase>();
            var result = await updateProductToppingsUseCase.ExecuteAsync(updatedRequest);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates all aspects of an existing product in a single call.
        /// </summary>
        /// <param name="id">The ID of the product to update.</param>
        /// <returns>The updated product.</returns>
        /// <response code="200">Returns the updated product.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpPut("{id}")]
        [SwaggerOperation(Summary = "Updates all aspects of an existing product with image upload", Description = "Updates all aspects of an existing product in a single call with support for image uploads.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product", typeof(UpdateProductResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Create, PermissionConsts.Product_Update)]
        public async Task<IActionResult> UpdateProduct(long id)
        {
            try
            {
                // Get the product JSON from form data
                var productJson = HttpContext.Request.Form["product"];
                if (string.IsNullOrEmpty(productJson))
                {
                    return BadRequest("Product data is required");
                }

                // Deserialize the product information with custom JSON naming settings
                var serializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true, ReadCommentHandling = JsonCommentHandling.Skip, AllowTrailingCommas = true };
                
                var request = JsonSerializer.Deserialize<ProductUpdateRequest>(productJson!, serializerOptions);
                
                if (request == null)
                {
                    return BadRequest("Invalid product data format");
                }

                // Process uploaded images if any
                var images = await ProcessProductImagesAsync(HttpContext.Request.Form.Files);
                if (images.Any())
                {
                    // Create Images request if it doesn't exist
                    if (request.Images == null)
                    {
                        request.Images = new UpdateProductImagesRequest { Images = images.Select(image => new ProductImageDto { Url = image }).ToList() };
                    }
                    // Add to existing Images
                    else if (request.Images.Images != null)
                    {
                        request.Images.Images.AddRange(images.Select(image => new ProductImageDto { Url = image }));
                    }
                    // Set Images if property exists but is null
                    else
                    {
                        request.Images.Images = [.. images.Select(image => new ProductImageDto { Url = image })];
                    }
                }

                // Map from ProductUpdateRequest to UpdateProductRequest
                var updateProductRequest = new UpdateProductRequest
                {
                    Id = id,
                    BasicInfo = request.BasicInfo,
                    Combo = request.Combo,
                    Images = request.Images,
                    OnHandQuantity = request.OnHandQuantity,
                    PriceBooks = request.PriceBooks,
                    Toppings = request.Toppings,
                    NotAllowSaleBranch = request.NotAllowSaleBranch,
                    AllBranchIds = request.AllBranchIds,
                    BranchesWithCreatePermission = request.BranchesWithCreatePermission
                };
                
                if (!_permissionService.IsAdmin)
                {
                    await SetBranchPermissionsAsync(updateProductRequest);
                }
                
                // Use the combined use case that handles transaction management
                var updateProductUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductUseCase>();
                var result = await updateProductUseCase.ExecuteAsync(updateProductRequest);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error updating product with ID {id}", ex);
                return BadRequest($"Error updating product: {ex.Message}");
            }
        }

        /// <summary>
        /// Deletes an existing product.
        /// </summary>
        /// <param name="id">The ID of the product to delete.</param>
        /// <returns>The deleted product.</returns>
        /// <response code="200">Returns the deleted product.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes an existing product", Description = "Deletes an existing product by ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the deleted product", typeof(DeleteProductResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Delete)]
        public async Task<IActionResult> DeleteProduct(long id)
        {
            var request = new DeleteProductRequest { Id = id };
            var deleteProductUseCase = HttpContext.RequestServices.GetRequiredService<DeleteProductUseCase>();
            var result = await deleteProductUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets the list of branches where a product is not allowed for sale.
        /// </summary>
        /// <param name="id">The ID of the product to get not-for-sale branch information.</param>
        /// <returns>The list of branches where the product is not allowed for sale.</returns>
        /// <response code="200">Returns the branches where the product is not allowed for sale.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the product is not found.</response>
        [HttpGet("{id}/not-sale-branches")]
        [SwaggerOperation(Summary = "Gets branches where product is not allowed for sale", Description = "Gets the list of branches where a specific product is not allowed for sale.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the branches where the product is not allowed for sale", typeof(GetProductSaleBranchResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the product is not found")]
        [RequireAnyPermissions(PermissionConsts.Product_Read)]
        public async Task<IActionResult> GetProductNotSaleBranches(long id)
        {
            var getProductNotSaleBranchUseCase = HttpContext.RequestServices.GetRequiredService<GetProductSaleBranchUseCase>();
            var request = new GetProductSaleBranchRequest { ProductId = id };
            var result = await getProductNotSaleBranchUseCase.ExecuteAsync(request);

            if (!result.IsSuccess || result.Value == null)
            {
                return Failure(result);
            }

            var permissions = await _permissionService.GetUserPermissionsAsync();
            var branchListUseCase = HttpContext.RequestServices.GetRequiredService<GetBranchListUseCase>();
            var allBranchesResult = await branchListUseCase.ExecuteAsync(new GetBranchListRequest { IncludeInactive = false });

            if (!allBranchesResult.IsSuccess || allBranchesResult.Value == null)
            {
                throw new InvalidOperationException("Failed to retrieve branch list or no branches found.");
            }

            var allBranchIds = allBranchesResult.Value.Branches.Select(branch => (long)branch.Id).ToList();

            var (createIds, updateIds) = _permissionService.IsAdmin 
                ? (allBranchIds, allBranchIds)
                : (
                    permissions.FirstOrDefault(p => p.Key == "Product_Create").Value,
                    permissions.FirstOrDefault(p => p.Key == "Product_Update").Value
                );

            var response = new GetProductSaleBranchResponse
            {
                NotAllowSaleBranch = result.Value.NotAllowSaleBranch,
                BranchHasCreatePrivilege = createIds,
                BranchHasUpdatePrivilege = updateIds
            };

            return Ok(response);
        }

        private async Task SetBranchPermissionsAsync(CreateProductRequest request)
        {
            var permissions = await _permissionService.GetUserPermissionsAsync();
            var branchIds = permissions.FirstOrDefault(p => p.Key == "Product_Create").Value;

            if (branchIds.Count == 0)
            {
                throw new UnauthorizedAccessException("User does not have permission to create products.");
            }

            var branchListUseCase = HttpContext.RequestServices.GetRequiredService<GetBranchListUseCase>();
            var allBranchesResult = await branchListUseCase.ExecuteAsync(new GetBranchListRequest { IncludeInactive = false });

            if (!allBranchesResult.IsSuccess || allBranchesResult.Value == null)
            {
                throw new InvalidOperationException("Failed to retrieve branch list or no branches found.");
            }

            request.BranchesWithCreatePermission = branchIds.Select(id => (int)id).ToList();
            request.AllBranchIds = allBranchesResult.Value.Branches.Select(branch => branch.Id).ToList();
        }

        private async Task SetBranchPermissionsAsync(UpdateProductRequest request)
        {
            var permissions = await _permissionService.GetUserPermissionsAsync();
            var branchIds = permissions.FirstOrDefault(p => p.Key == "Product_Update").Value;

            if (branchIds.Count == 0)
            {
                throw new UnauthorizedAccessException("User does not have permission to create products.");
            }

            var branchListUseCase = HttpContext.RequestServices.GetRequiredService<GetBranchListUseCase>();
            var allBranchesResult = await branchListUseCase.ExecuteAsync(new GetBranchListRequest { IncludeInactive = false });

            if (!allBranchesResult.IsSuccess || allBranchesResult.Value == null)
            {
                throw new InvalidOperationException("Failed to retrieve branch list or no branches found.");
            }

            request.BranchesWithCreatePermission = [.. branchIds.Select(id => (int)id)];
            request.AllBranchIds = allBranchesResult.Value.Branches.Select(branch => branch.Id).ToList();
        }
    }

    /// <summary>
    /// Request model for updating all aspects of a product in a single call
    /// </summary>
    public class ProductUpdateRequest
    {
        /// <summary>
        /// Basic information update request
        /// </summary>
        [JsonPropertyName("basic_info"), Description("Basic information update request")]
        public UpdateProductBasicInfoRequest? BasicInfo { get; set; }

        /// <summary>
        /// Combo details update request
        /// </summary>
        [JsonPropertyName("combo"), Description("Combo details update request")]
        public UpdateProductComboRequest? Combo { get; set; }

        /// <summary>
        /// Images update request
        /// </summary>
        [JsonPropertyName("images"), Description("Images update request")]
        public UpdateProductImagesRequest? Images { get; set; }

        /// <summary>
        /// On-hand quantity update request
        /// </summary>
        [JsonPropertyName("on_hand_quantity"), Description("On-hand quantity update request")]
        public UpdateProductOnHandQuantityRequest? OnHandQuantity { get; set; }

        /// <summary>
        /// Price books update request
        /// </summary>
        [JsonPropertyName("price_books"), Description("Price books update request")]
        public UpdateProductPriceBooksRequest? PriceBooks { get; set; }

        /// <summary>
        /// Toppings update request
        /// </summary>
        [JsonPropertyName("toppings"), Description("Toppings update request")]
        public UpdateProductToppingsRequest? Toppings { get; set; }

         /// <summary>
        /// List of branch IDs where the product is not allowed for sale
        /// </summary>
        [JsonPropertyName("not_allow_sale_branch"), Description("List of branch IDs where the product is not allowed for sale")]
        public List<long>? NotAllowSaleBranch { get; set; }

        /// <summary>
        /// All branch IDs available in the system
        /// </summary>
        [JsonPropertyName("all_branch_ids"), Description("All branch IDs available in the system")]
        [JsonIgnore]
        public List<int>? AllBranchIds { get; set; }

        /// <summary>
        /// Branch IDs where the user has permission to create the product
        /// </summary>
        [JsonIgnore]
        [JsonPropertyName("branches_with_create_permission"), Description("Branch IDs where the user has permission to create the product")]
        public List<int>? BranchesWithCreatePermission { get; set; }
    }
} 