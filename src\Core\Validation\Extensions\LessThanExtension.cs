namespace KvFnB.Core.Validation.Extensions
{
    public static class LessThanExtension
    {
        public static RuleBuilder<T, TProperty?> LessThan<T, TProperty>(
            this RuleBuilder<T, TProperty?> ruleBuilder, 
            TProperty compareValue,
            string errorMessage = "Value must be less than the specified value."
        ) where TProperty : struct, IComparable
        {
            ruleBuilder.Validator.AddRule(
                ruleBuilder.GroupId, 
                x => IsLessThan(ruleBuilder.Expression.Compile()(x), compareValue), 
                errorMessage
            );
            return ruleBuilder;
        }

        public static RuleBuilder<T, TProperty> LessThan<T, TProperty>(
            this RuleBuilder<T, TProperty> ruleBuilder,
            TProperty compareValue, 
            string errorMessage = "Value must be less than the specified value."
        ) where TProperty : struct, IComparable
        {
            ruleBuilder.Validator.AddRule(
                ruleBuilder.GroupId, 
                x => <PERSON><PERSON>ess<PERSON>han(ruleBuilder.Expression.Compile()(x), compareValue), 
                errorMessage
            );
            return ruleBuilder;
        }

        private static bool IsLessThan<TProperty>(TProperty? value, TProperty valueCompare) where TProperty : struct, IComparable
        {
            return value.HasValue && value.Value.CompareTo(valueCompare) < 0;
        }

        private static bool IsLessThan<TProperty>(TProperty value, TProperty valueCompare) where TProperty : struct, IComparable
        {
            return value.CompareTo(valueCompare) < 0;
        }
    }
} 