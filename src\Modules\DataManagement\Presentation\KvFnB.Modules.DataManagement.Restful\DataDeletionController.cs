﻿using KvFnB.Modules.DataManagement.Application.UseCases.VerifyUserUseCases.VerifyUserForDataDeletion;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.DeleteDeleteDataRequest;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Core.Authentication;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetPageDeleteDataRequests;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetPageDeleteDataHistory;

namespace KvFnB.Modules.DataManagement.Restful
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DataDeletionController : BaseApi
    {
        private readonly VerifyUserForDataDeletionUseCase _verifyUserForDataDeletionUseCase;
        private readonly CreateDeleteDataRequestUseCase _createDeleteDataRequestUseCase;
        private readonly UpdateDeleteDataRequestUseCase _updateDeleteDataRequestUseCase;
        private readonly DeleteDeleteDataRequestUseCase _deleteDeleteDataRequestUseCase;
        private readonly GetPageDeleteDataRequestsUseCase _getPageDeleteDataRequestsUseCase;
        private readonly GetPageDeleteDataHistoryUseCase _getPageDeleteDataHistoryUseCase;
        private readonly IOtpService _otpService;
        private readonly IAuthUser _authUser;

        public DataDeletionController(
            VerifyUserForDataDeletionUseCase verifyUserForDataDeletionUseCase,
            CreateDeleteDataRequestUseCase createDeleteDataRequestUseCase,
            UpdateDeleteDataRequestUseCase updateDeleteDataRequestUseCase,
            DeleteDeleteDataRequestUseCase deleteDeleteDataRequestUseCase,
            GetPageDeleteDataRequestsUseCase getPageDeleteDataRequestsUseCase,
            GetPageDeleteDataHistoryUseCase getPageDeleteDataHistoryUseCase,
            IOtpService otpService,
            IUserService userService,
            IAuthUser authUser,
            IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _verifyUserForDataDeletionUseCase = verifyUserForDataDeletionUseCase ?? throw new ArgumentNullException(nameof(verifyUserForDataDeletionUseCase));
            _createDeleteDataRequestUseCase = createDeleteDataRequestUseCase ?? throw new ArgumentNullException(nameof(createDeleteDataRequestUseCase));
            _updateDeleteDataRequestUseCase = updateDeleteDataRequestUseCase ?? throw new ArgumentNullException(nameof(updateDeleteDataRequestUseCase));
            _deleteDeleteDataRequestUseCase = deleteDeleteDataRequestUseCase ?? throw new ArgumentNullException(nameof(deleteDeleteDataRequestUseCase));
            _getPageDeleteDataRequestsUseCase = getPageDeleteDataRequestsUseCase ?? throw new ArgumentNullException(nameof(getPageDeleteDataRequestsUseCase));
            _getPageDeleteDataHistoryUseCase = getPageDeleteDataHistoryUseCase ?? throw new ArgumentNullException(nameof(getPageDeleteDataHistoryUseCase));
            _otpService = otpService ?? throw new ArgumentNullException(nameof(otpService));
            _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
        }

        /// <summary>
        /// Xác thực người dùng trước khi thực hiện yêu cầu xóa dữ liệu.
        /// </summary>
        /// <param name="request">Yêu cầu chứa mật khẩu và số điện thoại của người dùng.</param>
        /// <returns>Kết quả xác thực và thông tin OTP nếu thành công.</returns>
        /// <response code="200">Trả về kết quả xác thực và trạng thái OTP.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ hoặc xác thực thất bại.</response>
        [HttpPost("verify-user")]
        [SwaggerOperation(Summary = "Xác thực thông tin người dùng cho việc xóa dữ liệu", Description = "Xác thực mật khẩu người dùng với cơ sở dữ liệu và số điện thoại với dịch vụ KMA trước khi gửi OTP.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về kết quả xác thực và trạng thái OTP", typeof(VerifyUserForDataDeletionResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ hoặc xác thực thất bại")]
        public async Task<IActionResult> VerifyUserForDataDeletion([FromBody] VerifyUserForDataDeletionRequest request)
        {
            try
            {
                var result = await _verifyUserForDataDeletionUseCase.ExecuteAsync(request);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                // Get current user info to send OTP
                var userId = _authUser.Id;
                // Gửi OTP sau khi xác thực thành công
                bool otpSent = false;
                try
                {
                    await _otpService.GenerateAndSendOtpAsync(
                        request.PhoneNumber,
                        request.FingerPrintKey);
                }
                catch
                {
                    // Không throw exception vì xác thực đã thành công, chỉ việc gửi OTP bị lỗi
                }

                // Cập nhật thông tin trạng thái OTP trong response
                var response = new VerifyUserForDataDeletionResponse
                {
                    Success = true,
                    OtpSent = true,
                };

                return Ok(response);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }

        /// <summary>
        /// Tạo mới yêu cầu xóa dữ liệu.
        /// </summary>
        /// <param name="request">Yêu cầu chứa thông tin chi tiết cho việc xóa dữ liệu.</param>
        /// <returns>Kết quả và thông tin của yêu cầu đã tạo.</returns>
        /// <response code="200">Trả về thông tin của yêu cầu đã tạo.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ hoặc xác thực thất bại.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Tạo mới yêu cầu xóa dữ liệu", Description = "Tạo mới yêu cầu xóa dữ liệu dựa trên thông tin được cung cấp.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về thông tin của yêu cầu đã tạo", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ")]
        public async Task<IActionResult> CreateDeleteDataRequest([FromBody] CreateDeleteDataRequestRequest request)
        {
            try
            {
                if(!_authUser.IsAdmin)
                    return Unauthorized("Không có quyền");
                var result = await _createDeleteDataRequestUseCase.ExecuteAsync(request);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                return Ok(result.Value);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }

        /// <summary>
        /// Cập nhật yêu cầu xóa dữ liệu hiện có.
        /// </summary>
        /// <param name="request">Yêu cầu chứa ID và thông tin cập nhật.</param>
        /// <returns>Kết quả và thông tin của yêu cầu đã cập nhật.</returns>
        /// <response code="200">Trả về thông tin của yêu cầu đã cập nhật.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ hoặc xác thực thất bại.</response>
        /// <response code="404">Nếu không tìm thấy yêu cầu.</response>
        [HttpPut]
        [SwaggerOperation(Summary = "Cập nhật yêu cầu xóa dữ liệu", Description = "Cập nhật yêu cầu xóa dữ liệu hiện có dựa trên thông tin được cung cấp.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về thông tin của yêu cầu đã cập nhật", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "Nếu không tìm thấy yêu cầu")]
        public async Task<IActionResult> UpdateDeleteDataRequest([FromBody] UpdateDeleteDataRequestRequest request)
        {
            try
            {
                if (!_authUser.IsAdmin)
                    return Unauthorized("Không có quyền");
                var result = await _updateDeleteDataRequestUseCase.ExecuteAsync(request);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                return Ok(result.Value);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }

        /// <summary>
        /// Xóa yêu cầu xóa dữ liệu.
        /// </summary>
        /// <param name="id">ID của yêu cầu cần xóa.</param>
        /// <param name="password">Mật khẩu để xác thực người dùng trước khi xóa.</param>
        /// <returns>Kết quả của thao tác xóa.</returns>
        /// <response code="200">Trả về kết quả thành công nếu xóa được yêu cầu.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ hoặc mật khẩu không đúng.</response>
        /// <response code="404">Nếu không tìm thấy yêu cầu.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Xóa yêu cầu xóa dữ liệu", Description = "Xóa yêu cầu xóa dữ liệu dựa trên ID được cung cấp. Yêu cầu xác thực mật khẩu.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về kết quả thành công nếu xóa được yêu cầu", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ hoặc mật khẩu không đúng")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "Nếu không tìm thấy yêu cầu")]
        public async Task<IActionResult> DeleteDeleteDataRequest(long id, [FromQuery] string password)
        {
            try
            {
                if (!_authUser.IsAdmin)
                    return Unauthorized("Không có quyền");   
                var request = new DeleteDeleteDataRequestRequest { 
                    Id = id,
                    Password = password 
                };
                var result = await _deleteDeleteDataRequestUseCase.ExecuteAsync(request);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                return Ok(result.Value);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }

        /// <summary>
        /// Lấy yêu cầu xóa dữ liệu theo trang.
        /// </summary>
        /// <param name="request">Yêu cầu phân trang với các bộ lọc tùy chọn.</param>
        /// <returns>Danh sách yêu cầu xóa dữ liệu được phân trang.</returns>
        /// <response code="200">Trả về danh sách yêu cầu xóa dữ liệu được phân trang.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Lấy yêu cầu xóa dữ liệu theo trang", Description = "Lấy danh sách yêu cầu xóa dữ liệu theo trang với bộ lọc tùy chọn.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về danh sách yêu cầu xóa dữ liệu được phân trang", typeof(GetPageDeleteDataRequestsResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ")]
        public async Task<IActionResult> GetPageDeleteDataRequests([FromQuery] GetPageDeleteDataRequestsRequest request)
        {
            try
            {
                if (!_authUser.IsAdmin)
                    return Unauthorized("Không có quyền");
                var result = await _getPageDeleteDataRequestsUseCase.ExecuteAsync(request, CancellationToken.None);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                return Ok(result.Value);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }

        /// <summary>
        /// Lấy lịch sử xóa dữ liệu theo trang.
        /// </summary>
        /// <param name="request">Yêu cầu phân trang với các bộ lọc tùy chọn.</param>
        /// <returns>Danh sách lịch sử xóa dữ liệu được phân trang.</returns>
        /// <response code="200">Trả về danh sách lịch sử xóa dữ liệu được phân trang.</response>
        /// <response code="400">Nếu yêu cầu không hợp lệ.</response>
        [HttpGet("history")]
        [SwaggerOperation(Summary = "Lấy lịch sử xóa dữ liệu theo trang", Description = "Lấy danh sách lịch sử xóa dữ liệu theo trang với bộ lọc tùy chọn.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Trả về danh sách lịch sử xóa dữ liệu được phân trang", typeof(GetPageDeleteDataHistoryResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Nếu yêu cầu không hợp lệ")]
        public async Task<IActionResult> GetPageDeleteDataHistory([FromQuery] GetPageDeleteDataHistoryRequest request)
        {
            try
            {
                if (!_authUser.IsAdmin)
                    return Unauthorized("Không có quyền");
                var result = await _getPageDeleteDataHistoryUseCase.ExecuteAsync(request, CancellationToken.None);

                if (!result.IsSuccess)
                {
                    return Failure(result);
                }

                return Ok(result.Value);
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Đã xảy ra lỗi không mong muốn");
            }
        }
    }
}
