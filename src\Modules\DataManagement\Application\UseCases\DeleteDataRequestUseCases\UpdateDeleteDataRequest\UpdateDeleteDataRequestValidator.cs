using System.Text.RegularExpressions;
using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Modules.DataManagement.Domain.Enums;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest
{
    public partial class UpdateDeleteDataRequestValidator : Validator<UpdateDeleteDataRequestRequest>
    {
        [GeneratedRegex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled)]
        public static partial Regex EmailRegex();

        [GeneratedRegex(@"^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$", RegexOptions.Compiled)]
        public static partial Regex TimeFormatRegex();

        public UpdateDeleteDataRequestValidator()
        {
            SetupBasicValidationRules();
            SetupFilterConditionValidation();
            SetupScheduleConfigValidation();
        }

        private void SetupBasicValidationRules()
        {
            // Validate request ID
            RuleFor(x => x.Id)
                .GreaterThan(0, "Invalid request ID");

            // Email validation
            RuleFor(x => x.Email)
                .Must(email => email == null || EmailRegex().IsMatch(email), "Email is not in a valid format");

            // Validate OTP
            RuleFor(x => x.Otp)
                .NotNullOrEmpty("OTP is required");

            // Validate the type of data to delete - support both enum and int
            RuleFor(x => x.Type)
                .Must(type => IsValidDeleteDataType(type), "Invalid delete data type");

            // Validate the schedule type - support both enum and int
            RuleFor(x => x.ScheduleType)
                .NotNull("ScheduleType cannot be null")
                .Must(type => type != null && IsValidScheduleType((DeleteDataScheduleType)type), "Invalid schedule type");

            // Validate branch IDs
            RuleFor(x => x.BranchIds)
                .NotNull("Branch IDs list cannot be null")
                .Must(list => list != null && list.Count > 0, "At least one branch ID is required");

            // Validate FingerPrintKey for OTP verification
            RuleFor(x => x.FingerPrintKey)
                .NotNullOrEmpty("Fingerprint key is required for OTP verification");

            // Validate ExecuteDate - must be in the future
            RuleFor(x => x.ExecuteDate)
                .Must(date => date != default, "Execution date is required")
                .Must(date => date.Date >= DateTime.Now.Date, "ExecuteDate must be in future");
        }

        private void SetupFilterConditionValidation()
        {
            // Validate ToDate in FilterCondition - must be in the past or present
            RuleFor(x => x.FilterCondition)
                .Must(IsFilterConditionValidForManualSchedule, "ToDate in FilterCondition is required")
                .When(x => x.FilterCondition != null && x.ScheduleType == DeleteDataScheduleType.Manual);

            // Validate ToDate must be before ExecuteDate
            RuleFor(x => x)
                .Must(IsToDateValidAgainstExecuteDate, "ToDate must be less than or equal ExecuteDate")
                .When(x => x.FilterCondition != null && x.FilterCondition.ToDate.HasValue);
        }

        private void SetupScheduleConfigValidation()
        {
            // Validate ScheduleConfig
            RuleFor(x => x.ScheduleConfig)
                .NotNull("Schedule configuration cannot be null")
                .When(x => x.ScheduleType == DeleteDataScheduleType.Scheduled);

            // Validate ScheduleConfig.ScheduleType
            RuleFor(x => x)
                .Must(IsScheduleConfigTypeValid, "Invalid schedule type in configuration")
                .When(IsScheduledType);

            // Validate ScheduleConfig.ExecutionTime format (HH:MM:SS)
            RuleFor(x => x)
                .Must(IsExecutionTimeFormatValid, "Execution time must be in HH:MM:SS format")
                .When(IsScheduledTypeWithExecutionTime);

            // Validate ScheduleConfig.DayOfWeek for Weekly schedules
            RuleFor(x => x)
                .Must(IsDayOfWeekValid, "Day of week must be between 0 (Sunday) and 6 (Saturday)")
                .When(IsWeeklySchedule);

            // Validate ScheduleConfig.DayOfMonth for Monthly schedules
            RuleFor(x => x)
                .Must(IsDayOfMonthValid, "Day of month must be between 1 and 31")
                .When(IsMonthlyBasedSchedule);

            // Validate ScheduleConfig.Month for Yearly schedules
            RuleFor(x => x)
                .Must(IsMonthValid, "Month must be between 1 and 12")
                .When(IsYearlySchedule);
        }

        // Helper methods for validation logic
        private static bool IsValidDeleteDataType(DeleteDataType? type) =>
    type.HasValue && Enum.IsDefined(typeof(DeleteDataType), (int)type);

        private static bool IsValidScheduleType(DeleteDataScheduleType type) =>
            Enum.IsDefined(typeof(DeleteDataScheduleType), (int)type);

        private static bool IsFilterConditionValidForManualSchedule(object? filterCondition) =>
            filterCondition == null || ((dynamic)filterCondition).ToDate.HasValue;

        private static bool IsToDateValidAgainstExecuteDate(UpdateDeleteDataRequestRequest request) =>
            request.FilterCondition?.ToDate == null ||
            request.FilterCondition.ToDate.Value.Date <= request.ExecuteDate.Date;

        private static bool IsScheduleConfigTypeValid(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && Enum.IsDefined(typeof(ScheduleType), x.ScheduleConfig.ScheduleType);

        private static bool IsExecutionTimeFormatValid(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && TimeFormatRegex().IsMatch(x.ScheduleConfig.ExecutionTime ?? "");

        private static bool IsDayOfWeekValid(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && x.ScheduleConfig.DayOfWeek >= 0 && x.ScheduleConfig.DayOfWeek <= 6;

        private static bool IsDayOfMonthValid(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && x.ScheduleConfig.DayOfMonth >= 1 && x.ScheduleConfig.DayOfMonth <= 31;

        private static bool IsMonthValid(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && x.ScheduleConfig.Month >= 1 && x.ScheduleConfig.Month <= 12;

        // Helper methods for When conditions
        private static bool IsScheduledType(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null && x.ScheduleType == DeleteDataScheduleType.Scheduled;

        private static bool IsScheduledTypeWithExecutionTime(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null &&
            x.ScheduleType == DeleteDataScheduleType.Scheduled &&
            x.ScheduleConfig.ExecutionTime != null;

        private static bool IsWeeklySchedule(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null &&
            x.ScheduleType == DeleteDataScheduleType.Scheduled &&
            x.ScheduleConfig.ScheduleType == (int)ScheduleType.Weekly;

        private static bool IsMonthlyBasedSchedule(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null &&
            x.ScheduleType == DeleteDataScheduleType.Scheduled &&
            (x.ScheduleConfig.ScheduleType == (int)ScheduleType.Monthly ||
             x.ScheduleConfig.ScheduleType == (int)ScheduleType.EveryThreeMonths ||
             x.ScheduleConfig.ScheduleType == (int)ScheduleType.Quarterly ||
             x.ScheduleConfig.ScheduleType == (int)ScheduleType.Yearly);

        private static bool IsYearlySchedule(UpdateDeleteDataRequestRequest x) =>
            x.ScheduleConfig != null &&
            x.ScheduleType == DeleteDataScheduleType.Scheduled &&
            x.ScheduleConfig.ScheduleType == (int)ScheduleType.Yearly;

    }
} 