using KvFnB.Core.Contracts;
using KvFnB.Core.Abstractions;
using KvFnB.Shared.DapperQuery;
using KvFnB.Modules.Customer.Application.Dtos;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetCustomerDetail;

public class GetCustomerDetailUseCase: UseCaseBase<GetCustomerDetailRequest, GetCustomerDetailResponse>
{
    private readonly IQueryService _queryService;
    public GetCustomerDetailUseCase(IQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public override async Task<Result<GetCustomerDetailResponse>> ExecuteAsync(GetCustomerDetailRequest request, CancellationToken cancellationToken = default)
    {
        var qb = new QueryBuilder();
        qb.Select("c.Id");
        qb.Select("c.RetailerId as TenantId");
        qb.Select("c.Code");
        qb.Select("c.Name");
        qb.Select("c.ContactNumber");
        qb.Select("c.Gender");
        qb.Select("c.Email");
        qb.Select("c.Avatar");
        qb.Select("c.Comments");
        qb.Select("c.Address");
        qb.Select("c.IsActive");
        qb.Select("c.BranchId");
        qb.Select("c.BirthDate");
        qb.Select("c.Type");
        qb.Select("c.Organization");
        qb.Select("c.TaxCode");
        qb.Select("c.Debt");
        qb.Select("c.RewardPoint");
        qb.Select("c.AddressId");
        qb.From("Customer c WITH(NOLOCK)");
        qb.Where("c.Id", "=", request.Id);
        qb.WhereGroup(subQuery => subQuery
              .WhereIsNull("c.IsDeleted")
              .Where("c.IsDeleted", "<>", 1, "OR")
        );

        var customer = await _queryService.QueryFirstOrDefaultAsync<GetCustomerDetailResponse>(qb) ?? throw new ApplicationException("Customer not found");
        await GetAddressLocation(customer);
        await GetCustomerGroup(customer);
        return Result<GetCustomerDetailResponse>.Success(customer);
    }

    private async Task GetCustomerGroup(GetCustomerDetailResponse customer)
    {
        var qb = new QueryBuilder();
        qb.Select("cg.Id");
        qb.Select("cg.Name");
        qb.From("CustomerGroup cg WITH(NOLOCK)");
        qb.InnerJoin("CustomerGroupDetail cgd WITH(NOLOCK)", "cg.Id = cgd.GroupId");
        qb.Where("cgd.CustomerId", "=", customer.Id);
        customer.CustomerGroups = [.. await _queryService.QueryAsync<CustomerGroupResponse>(qb)];
    }

    private async Task GetAddressLocation(GetCustomerDetailResponse customer)
    {
        if(customer.AddressId == null) return;
        var qb = new QueryBuilder();
        qb.Select("a.Id");
        qb.Select("a.RetailerId as TenantId");
        qb.Select("a.AddressLine");
        qb.Select("a.AdministrativeAreaId");
        qb.Select("a.PostalCode");
        qb.From("Address a WITH(NOLOCK)");
        qb.Where("a.Id", "=", customer.AddressId!);
        var address = await _queryService.QueryFirstOrDefaultAsync<AddressResponse>(qb);
        customer.AddressLocation = address;

    }
} 