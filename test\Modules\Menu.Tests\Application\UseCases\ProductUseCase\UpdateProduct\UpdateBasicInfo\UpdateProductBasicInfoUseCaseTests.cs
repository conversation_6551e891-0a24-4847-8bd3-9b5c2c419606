using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Dtos.Response;
using KvFnB.Modules.Menu.Application.Tests.Helpers;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Localization;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo
{
    public class UpdateProductBasicInfoUseCaseTests
    {
        private readonly Mock<IValidator<UpdateProductBasicInfoRequest>> _validator;
        private readonly Mock<IProductRepository> _productRepository;
        private readonly ILocalizationProvider _multiLang;
        private readonly Mock<ICategoryRepository> _categoryRepository;
        private readonly Mock<ITaxRepository> _taxRepository;
        private readonly Mock<IUnitOfWork> _unitOfWork;
        private readonly Mock<IMapper> _mapper;
        private readonly Mock<ITenantProvider> _tenantProvider;
        private readonly UpdateProductBasicInfoUseCase _useCase;

        public UpdateProductBasicInfoUseCaseTests()
        {
            _validator = new Mock<IValidator<UpdateProductBasicInfoRequest>>();
            _productRepository = new Mock<IProductRepository>();
            _categoryRepository = new Mock<ICategoryRepository>();
            _taxRepository = new Mock<ITaxRepository>();
            _unitOfWork = new Mock<IUnitOfWork>();
            _mapper = new Mock<IMapper>();
            _tenantProvider = new Mock<ITenantProvider>();
            _multiLang = TestLocalizationHelper.CreateVietnameseLocalizationProvider();

            _useCase = new UpdateProductBasicInfoUseCase(
                _multiLang,
                _validator.Object,
                _productRepository.Object,
                _categoryRepository.Object,
                _taxRepository.Object,
                _unitOfWork.Object,
                _tenantProvider.Object,
                _mapper.Object
            );
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-001",
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f,
                Unit = "pcs",
                ConversionValue = 1.0f,
                AllowSale = true,
                ProductGroupId = (byte)ProductGroupTypes.Food.Id
            };
            
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            _productRepository.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-001",
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f,
                Unit = "pcs",
                ConversionValue = 1.0f,
                AllowSale = true,
                ProductGroupId = (byte)ProductGroupTypes.Food.Id
            };
            
            var validationResult = ValidationResult.Success();
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            _ = _productRepository.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenCategoryNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-001",
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f,
                Unit = "pcs",
                ConversionValue = 1.0f,
                AllowSale = true,
                ProductGroupId = (byte)ProductGroupTypes.Food.Id
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            var validationResult = ValidationResult.Success();
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepository.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepository.Setup(r => r.IsUniqueProductCodeAsync(request.Code, request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(true);
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            _ = _categoryRepository.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>())).ReturnsAsync((Category)null);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldUpdateProductBasicInfo()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-001",
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f,
                Unit = "pcs",
                ConversionValue = 1.0f,
                AllowSale = true,
                ProductGroupId = (byte)ProductGroupTypes.Food.Id
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 2,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            // Set ModifiedAt to the current time
            typeof(Product).GetProperty("ModifiedAt")!.SetValue(product, DateTime.UtcNow);
            
            var category = new Category { Name = "Test Category" };
            typeof(Category).GetProperty("Id")!.SetValue(category, 2);
            
            var response = new UpdateProductBasicInfoResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name,
                CategoryId = request.CategoryId,
                Description = request.Description,
                Weight = request.Weight,
                Unit = request.Unit,
                ConversionValue = request.ConversionValue,
                AllowSale = request.AllowSale,
                Tax = new TaxResponse
                {
                    Id = 1,
                    Name = "Test Tax",
                    TaxRate = 10,
                    TaxType = 1
                },
                ProductGroup = ProductGroupTypes.From(request.ProductGroupId.Value),
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };
            
            var validationResult = ValidationResult.Success();
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepository.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepository.Setup(r => r.IsUniqueProductCodeAsync(request.Code, request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(true);
            _categoryRepository.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>())).ReturnsAsync(category);
            _productRepository.Setup(r => r.UpdateAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _mapper.Setup(m => m.Map<UpdateProductBasicInfoResponse>(product)).Returns(response);
            
            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(response, result.Value);
            _productRepository.Verify(r => r.UpdateAsync(product, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWork.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenCodeNotUnique_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-002", // Changed code
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f,
                Unit = "pcs",
                ConversionValue = 1.0f
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001", // Original code
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            var validationResult = ValidationResult.Success();
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepository.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepository.Setup(r => r.IsUniqueProductCodeAsync(request.Code, request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(false);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductBasicInfoRequest
            {
                ProductId = 1,
                Code = "PROD-001",
                Name = "Updated Product",
                CategoryId = 2,
                Description = "Updated Description",
                BasePrice = 10.99m,
                Weight = 1.5f
            };
            
            var validationResult = ValidationResult.Success();
            _validator.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepository.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var exception = await Assert.ThrowsAsync<Exception>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
        }
    }
}