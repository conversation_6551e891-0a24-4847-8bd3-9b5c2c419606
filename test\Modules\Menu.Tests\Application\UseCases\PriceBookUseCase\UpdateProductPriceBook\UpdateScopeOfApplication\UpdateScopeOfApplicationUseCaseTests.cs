using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Dtos.Request;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateScopeOfApplication;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.MultiTenancy;
using Moq;
using IMapper = KvFnB.Core.Abstractions.IMapper;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateScopeOfApplication
{
    public class UpdateScopeOfApplicationUseCaseTests
    {
        private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<IValidator<UpdateScopeOfApplicationRequest>> _validatorMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<TenantInfo> _tenantInfoMock;
        private readonly UpdateScopeOfApplicationUseCase _useCase;

        public UpdateScopeOfApplicationUseCaseTests()
        {
            _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _validatorMock = new Mock<IValidator<UpdateScopeOfApplicationRequest>>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger>();
            _tenantInfoMock = new Mock<TenantInfo>();

            _useCase = new UpdateScopeOfApplicationUseCase(
                _priceBookRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _validatorMock.Object,
                _mapperMock.Object,
                _loggerMock.Object,
                _tenantInfoMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateBasicRequest();
            var validationResult = ValidationResult.Failure(new List<string> { "Validation error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Validation error", result.ValidationErrors!);
            _priceBookRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenPriceBookNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateBasicRequest();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync((PriceBook)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal($"Price book with ID {request.Id} not found", result.ErrorMessage);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldUpdateScopeSettings()
        {
            // Arrange
            var request = CreateBasicRequest();
            var priceBook = CreatePriceBook();
            var response = new UpdateScopeOfApplicationResponse();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync(priceBook);
            _mapperMock.Setup(m => m.Map<UpdateScopeOfApplicationResponse>(It.IsAny<PriceBook>())).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Same(response, result.Value);
            
            // Verify scope settings were updated
            Assert.Equal(request.IsGlobal, priceBook.IsGlobal);
            Assert.Equal(request.ForAllUser, priceBook.ForAllUser);
            Assert.Equal(request.ForAllCustomerGroup, priceBook.ForAllCustomerGroup);
            Assert.Equal(request.ForAllTableAndRoom, priceBook.ForAllTableAndRoom);
            Assert.Equal(request.Type, priceBook.Type);
            
            _priceBookRepositoryMock.Verify(r => r.UpdateAsync(priceBook, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenGlobal_ShouldClearBranches()
        {
            // Arrange
            var request = CreateBasicRequest(isGlobal: true);
            
            var priceBook = CreatePriceBook();
            priceBook.AddPriceBookBranch(1);
            priceBook.AddPriceBookBranch(2);
            
            var response = new UpdateScopeOfApplicationResponse();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync(priceBook);
            _mapperMock.Setup(m => m.Map<UpdateScopeOfApplicationResponse>(It.IsAny<PriceBook>())).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Empty(priceBook.PriceBookBranches);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldAddNewBranchesAndRemoveOldOnes()
        {
            // Arrange
            var request = CreateBasicRequest(
                isGlobal: false,
                priceBookBranchIds: new List<int> { 2, 3 }  // Add 2, 3 (removing 1)
            );
            
            var priceBook = CreatePriceBook();
            priceBook.AddPriceBookBranch(1); // Existing branch 1
            
            var response = new UpdateScopeOfApplicationResponse();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync(priceBook);
            _mapperMock.Setup(m => m.Map<UpdateScopeOfApplicationResponse>(It.IsAny<PriceBook>())).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(2, priceBook.PriceBookBranches.Count);
            
            // Branch 1 should be removed
            Assert.DoesNotContain(priceBook.PriceBookBranches, b => b.BranchId == 1);
            
            // Branches 2 and 3 should be added
            Assert.Contains(priceBook.PriceBookBranches, b => b.BranchId == 2);
            Assert.Contains(priceBook.PriceBookBranches, b => b.BranchId == 3);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionThrown_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateBasicRequest();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Error updating price book: Test exception", result.ErrorMessage);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        private static UpdateScopeOfApplicationRequest CreateBasicRequest(
            bool isGlobal = false, 
            List<int>? priceBookBranchIds = null)
        {
            return new UpdateScopeOfApplicationRequest
            {
                Id = 1,
                IsGlobal = isGlobal,
                ForAllUser = false,
                ForAllCustomerGroup = false,
                ForAllTableAndRoom = false,
                ForTakeAwayTable = false,
                Type = "Regular",
                PriceBookBranchIds = priceBookBranchIds ?? new List<int> { 1 },
                PriceBookCustomerGroupIds = new List<int> { 1 },
                PriceBookTableAndRoomIds = new List<int> { 1 },
                PriceBookGroups = new List<PriceBookGroupRequest> { new() { GroupId = 1, Status = true, Type = 1 } },
                PriceBookDiningOptions = new List<PriceBookDiningOptionRequest> { new() { DiningOption = 1, BranchId = 1 } },
                PriceBookUserIds = new List<long> { 1 }
            };
        }

        private static PriceBook CreatePriceBook()
        {
            var priceBook = PriceBook.Create(new CreatePriceBookModel
            {
                Name = "Test Price Book",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(30),
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            });
            
            // Set ID for the price book
            var field = typeof(PriceBook).GetProperty("Id");
            field!.SetValue(priceBook, 1, null);
            
            return priceBook;
        }
    }
} 