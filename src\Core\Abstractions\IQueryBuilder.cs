namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Abstraction for building SQL queries.
    /// </summary>
    public interface IQueryBuilder
    {
        // SELECT clause: list the fields to return.
        IQueryBuilder Select(params string[] fields);

        // Optionally add DISTINCT.
        IQueryBuilder Distinct();

        // FROM clause: set the base table.
        IQueryBuilder From(string table);

        // JOIN clauses.
        IQueryBuilder Join(string joinType, string table, string onCondition);
        IQueryBuilder InnerJoin(string table, string onCondition);
        IQueryBuilder LeftJoin(string table, string onCondition);
        IQueryBuilder RightJoin(string table, string onCondition);
        IQueryBuilder FullJoin(string table, string onCondition);

        // WHERE conditions.
        IQueryBuilder Where(string field, string op, object value, string logic = "AND");
        IQueryBuilder WhereGroup(Func<IQueryBuilder, IQueryBuilder> groupExpression, string logic = "AND");
        IQueryBuilder WhereIn(string field, IEnumerable<object> values, string logic = "AND");
        IQueryBuilder WhereBetween(string field, object start, object end, string logic = "AND");
        IQueryBuilder WhereIsNull(string field, string logic = "AND");
        IQueryBuilder WhereIsNotNull(string field, string logic = "AND");

        // GROUP BY and HAVING.
        IQueryBuilder GroupBy(params string[] fields);
        IQueryBuilder Having(string field, string op, object value, string logic = "AND");

        // ORDER BY.
        IQueryBuilder OrderBy(string field, bool descending = false);

        // Paging.
        IQueryBuilder Skip(int count);
        IQueryBuilder Take(int count);
    }
}
