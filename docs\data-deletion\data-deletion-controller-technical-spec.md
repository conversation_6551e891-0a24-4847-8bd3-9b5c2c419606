# Data Management Feature - Implementation Guide

## Overview

This implementation guide provides detailed instructions for developing the Data Management feature based on the technical specifications. The feature allows admin users to delete data based on configured conditions and time periods.

## Implementation Structure

### Folder Structure

The implementation follows the hexagonal architecture and is organized as follows:

```
src/
└── Modules/
    └── DataManagement/
        ├── Application/
        │   ├── Dtos/
        │   │   ├── DeleteDataRequestDto.cs
        │   │   ├── DeleteDataHistoryDto.cs
        │   │   └── DeleteDataDetailDto.cs
        │   └── UseCases/
        │       ├── VerifyUserUseCases/
        │       │   └── VerifyUserForDataDeletion/
        │       │       ├── VerifyUserForDataDeletionRequest.cs
        │       │       ├── VerifyUserForDataDeletionResponse.cs
        │       │       ├── VerifyUserForDataDeletionValidator.cs
        │       │       └── VerifyUserForDataDeletionUseCase.cs
        │       ├── DeleteDataRequestUseCases/
        │       │   ├── CreateDeleteDataRequest/
        │       │   │   ├── CreateDeleteDataRequestRequest.cs
        │       │   │   ├── CreateDeleteDataRequestResponse.cs
        │       │   │   ├── CreateDeleteDataRequestValidator.cs
        │       │   │   └── CreateDeleteDataRequestUseCase.cs
        │       │   ├── UpdateDeleteDataRequest/
        │       │   │   ├── UpdateDeleteDataRequestRequest.cs
        │       │   │   ├── UpdateDeleteDataRequestResponse.cs
        │       │   │   ├── UpdateDeleteDataRequestValidator.cs
        │       │   │   └── UpdateDeleteDataRequestUseCase.cs
        │       │   ├── DeleteDeleteDataRequest/
        │       │   │   ├── DeleteDeleteDataRequestRequest.cs
        │       │   │   ├── DeleteDeleteDataRequestValidator.cs
        │       │   │   └── DeleteDeleteDataRequestUseCase.cs
        │       │   └── GetAllDeleteDataRequests/
        │       │       ├── GetAllDeleteDataRequestsRequest.cs
        │       │       ├── GetAllDeleteDataRequestsResponse.cs
        │       │       └── GetAllDeleteDataRequestsUseCase.cs
        │       └── DeleteDataHistoryUseCases/
        │           └── GetAllDeleteDataHistory/
        │               ├── GetAllDeleteDataHistoryRequest.cs
        │               ├── GetAllDeleteDataHistoryResponse.cs
        │               └── GetAllDeleteDataHistoryUseCase.cs
        ├── Domain/
        │   ├── Entities/
        │   │   ├── DeleteDataRequest.cs
        │   │   ├── DeleteDataHistory.cs
        │   │   └── DeleteDataDetail.cs
        │   ├── Enums/
        │   │   ├── DeleteDataRequestStatus.cs
        │   │   ├── DeleteDataScheduleType.cs
        │   │   ├── DeleteDataType.cs
        │   │   ├── TfaDevice.cs
        │   │   └── SmsAction.cs
        │   ├── Repositories/
        │   │   ├── IDeleteDataRequestRepository.cs
        │   │   └── IDeleteDataHistoryRepository.cs
        │   └── Services/
        │       ├── IUserService.cs
        │       ├── IKmaService.cs
        │       └── IOtpService.cs
        ├── Infrastructure/
        │   ├── DependencyInjection/
        │   │   └── ServiceRegistration.cs
        │   ├── Mapping/
        │   │   └── DataManagementMappingProfile.cs
        │   ├── Models/
        │   │   ├── OtpConfiguration.cs
        │   │   └── KmaConfiguration.cs
        │   ├── Services/
        │   │   ├── KmaService.cs
        │   │   ├── UserService.cs
        │   │   ├── OtpService.cs
        │   │   └── DataDeletionProcessingService.cs
        │   └── Repositories/
        │       ├── DeleteDataRequestRepository.cs
        │       └── DeleteDataHistoryRepository.cs
        ├── Persistence/
        │   └── ShardingDb/
        │       └── EntityTypeConfigurations/
        │           ├── DeleteDataRequestConfiguration.cs
        │           ├── DeleteDataHistoryConfiguration.cs
        │           └── DeleteDataDetailConfiguration.cs
        └── Presentation/
            └── KvFnB.Modules.DataManagement.Restful/
                ├── DataDeletionController.cs
                ├── BaseApi.cs
                └── KvFnB.Modules.DataManagement.Restful.csproj
```

## Data Deletion Flow and Architecture

### API Endpoints and Use Cases

```
POST /api/DeleteData/verify-user
  └── VerifyUserUseCases.VerifyUserForDataDeletion.VerifyUserForDataDeletionUseCase
      ├── Input: VerifyUserForDataDeletionRequest
      │         (Password, PhoneNumber)
      └── Output: VerifyUserForDataDeletionResponse
      └── Dependencies:
          ├── IUserService.VerifyUserPasswordAsync
          └── IKmaService.VerifyPhoneNumberAsync

POST /api/DeleteData
  └── DeleteDataRequestUseCases.CreateDeleteDataRequest.CreateDeleteDataRequestUseCase
      ├── Input: CreateDeleteDataRequestRequest
      │         (Email, Type, ScheduleType, ScheduleConfig, BranchIds, etc.)
      └── Output: CreateDeleteDataRequestResponse
      └── Dependencies:
          └── EmailVerificationService.VerifyOtpAsync

PUT /api/DeleteData
  └── DeleteDataRequestUseCases.UpdateDeleteDataRequest.UpdateDeleteDataRequestUseCase
      ├── Input: UpdateDeleteDataRequestRequest
      │         (OTP, RequestId, Status, ScheduleConfig, BranchIds, Type, ScheduleType, etc.)
      └── Output: DeleteDataRequestResponse
      └── Dependencies:
          └── EmailVerificationService.VerifyOtpAsync

DELETE /api/DeleteData/{id}
  └── DeleteDataRequestUseCases.DeleteDeleteDataRequest.DeleteDeleteDataRequestUseCase
      ├── Input: DeleteDeleteDataRequestRequest
      │         (Id)
      └── Output: DeleteDataRequestResponse
      └── Dependencies:
          └── EmailVerificationService.VerifyOtpAsync

GET /api/DeleteData
  └── DeleteDataRequestUseCases.GetAllDeleteDataRequests.GetAllDeleteDataRequestsUseCase
      ├── Input: GetAllDeleteDataRequestsRequest
      └── Output: GetAllDeleteDataRequestsResponse

GET /api/DeleteData/history
  └── DeleteDataHistoryUseCases.GetAllDeleteDataHistory.GetAllDeleteDataHistoryUseCase
      ├── Input: GetAllDeleteDataHistoryRequest
      └── Output: GetAllDeleteDataHistoryResponse
```

### Complete Data Deletion Flow

```
┌─────────────┐                         ┌───────────────────────────────┐                   ┌────────────────────┐
│   Client    │                         │       DeleteDataController    │                   │     Domain Layer    │
└──────┬──────┘                         └─────────────────┬─────────────┘                   └──────────┬─────────┘
       │                                                  │                                            │
       │  1. POST /api/DeleteData/verify-user            │                                            │
       │  (Password + Phone)                             │                                            │
       │─────────────────────────────────────────────────>                                            │
       │                                                  │                                            │
       │                                                  │  VerifyUserUseCases.VerifyUserForDataDeletion
       │                                                  │────────────────────────────────────────────>
       │                                                  │                                            │  Verify Password with
       │                                                  │                                            │  User DB & Phone with
       │                                                  │                                            │  KMA service
       │                                                  │                                            │  Send OTP to Phone
       │                                                  │<────────────────────────────────────────────
       │  Response: Verification Result + OTP Info        │                                            │
       │<─────────────────────────────────────────────────                                            │
       │                                                  │                                            │
       │                   ┌─────────────────────────────┐                                            │
       │                   │                             │                                            │
       │                   │   If creating new request   │                                            │
       │                   │                             │                                            │
       │                   └──────────┬──────────────────┘                                            │
       │                              │                                                               │
       │  2A. POST /api/DeleteData    │                                                               │
       │  (Email + Full Request Data)                                                           │
       │─────────────────────────────>│                                                               │
       │                              │                                                               │
       │                              │  DeleteDataRequestUseCases.CreateDeleteDataRequest            │
       │                              │───────────────────────────────────────────────────────────────>
       │                              │                                                               │  Create DeleteDataRequest
       │                              │                                                               │  using Domain.Repositories
       │                              │                                                               │  with all provided fields:
       │                              │                                                               │  Type, ScheduleType,
       │                              │                                                               │  ScheduleConfig, BranchIds, etc.
       │                              │<───────────────────────────────────────────────────────────────
       │  Response: Created Request Details                                                           │
       │<─────────────────────────────┘                                                               │
       │                                                                                              │
       │                   ┌─────────────────────────────┐                                            │
       │                   │                             │                                            │
       │                   │  If updating existing request│                                            │
       │                   │                             │                                            │
       │                   └──────────┬──────────────────┘                                            │
       │                              │                                                               │
       │  2B. PUT /api/DeleteData     │                                                               │
       │  (OTP + RequestId + Full Model Updates)                                                      │
       │─────────────────────────────>│                                                               │
       │                              │                                                               │
       │                              │  DeleteDataRequestUseCases.UpdateDeleteDataRequest            │
       │                              │───────────────────────────────────────────────────────────────>
       │                              │                                                               │  Verify OTP
       │                              │                                                               │  Update Request with
       │                              │                                                               │  comprehensive changes
       │                              │                                                               │  using Domain.Repositories:
       │                              │                                                               │  Status, Type, ScheduleType
       │                              │                                                               │  ScheduleConfig, BranchIds, etc.
       │                              │<───────────────────────────────────────────────────────────────
       │  Response: Updated Request Details                                                           │
       │<─────────────────────────────┘                                                               │
       │                                                                                              │
       │                   ┌─────────────────────────────┐                                            │
       │                   │                             │                                            │
       │                   │  If deleting existing request│                                            │
       │                   │                             │                                            │
       │                   └──────────┬──────────────────┘                                            │
       │                              │                                                               │
       │  3. DELETE /api/DeleteData/{id} │                                                               │
       │  (RequestId)                                                                         │
       │─────────────────────────────>│                                                               │
       │                              │                                                               │
       │                              │  DeleteDataRequestUseCases.DeleteDeleteDataRequest            │
       │                              │───────────────────────────────────────────────────────────────>
       │                              │                                                               │  Delete DeleteDataRequest
       │                              │                                                               │  using Domain.Repositories
       │                              │                                                               │  with all provided fields:
       │                              │                                                               │  Id, Email, Status, Type, ScheduleType,
       │                              │                                                               │  ScheduleConfig, BranchIds, etc.
       │                              │<───────────────────────────────────────────────────────────────
       │  Response: Deleted Request Details                                                           │
       │<─────────────────────────────┘                                                               │
       │                                                                                              │
```

### Hexagonal Architecture Components

```
┌───────────────────────────────────────────────────────────────────────────────┐
│                                                                               │
│  ┌─────────────────────────────────┐          ┌─────────────────────────────┐ │
│  │                                 │          │                             │ │
│  │      Presentation Layer         │          │      Application Layer      │ │
│  │  (Controllers/API Endpoints)    │◄────────►│     (Use Cases/Services)    │ │
│  │                                 │          │                             │ │
│  └─────────────────────────────────┘          └─────────────┬───────────────┘ │
│                                                             │                 │
│                                                             │                 │
│                                                             ▼                 │
│                                                ┌─────────────────────────────┐ │
│                                                │                             │ │
│                                                │       Domain Layer          │ │
│                                                │   (Entities/Aggregates)     │ │
│                                                │   (Repository Interfaces)   │ │
│                                                │   (Service Interfaces)      │ │
│                                                │                             │ │
│                                                └───────────┬──────────────────┘ │
│                                                             │                 │
│                                                             │                 │
│  ┌─────────────────────────────────┐                        │                 │
│  │                                 │                        │                 │
│  │    Infrastructure Layer         │                        │                 │
│  │ (Repository Implementations)    │◄───────────────────────┘                 │
│  │ (Service Implementations)       │                                          │
│  │                                 │                                          │
│  └─────────────────────────────────┘                                          │
│                                                                               │
│                                        DataManagement Module                  │
└───────────────────────────────────────────────────────────────────────────────┘
```

## UseCase Implementations

The primary use cases for the data deletion flow are:

### 1. VerifyUserForDataDeletion UseCase

This is the first step in the data deletion process. It verifies the user's identity using password and phone number before allowing any data deletion operations.

```csharp
public class VerifyUserForDataDeletionUseCase : UseCaseBase<VerifyUserForDataDeletionRequest, VerifyUserForDataDeletionResponse>
{
    // Dependencies
    private readonly IValidator<VerifyUserForDataDeletionRequest> _validator;
    private readonly IUserService _userService;
    private readonly IKmaService _kmaService;
    private readonly EmailVerificationService _emailVerificationService;
    private readonly ITenantProvider _tenantProvider;
    private readonly IAuthUser _authUser;
    
    public override async Task<Result<VerifyUserForDataDeletionResponse>> ExecuteAsync(
        VerifyUserForDataDeletionRequest request, 
        CancellationToken cancellationToken = default)
    {
        // Validate the request
        // Check permissions
        // Get tenant ID and user ID
        
        // Step 1: Verify password against User DB
        var passwordVerified = await _userService.VerifyUserPasswordAsync(userId, request.Password);
        
        // Step 2: Verify phone number with KMA service
        var phoneVerified = await _kmaService.VerifyPhoneNumberAsync(tenantId, request.PhoneNumber);
        
        // If verification successful, send OTP to the phone number
        await _emailVerificationService.GenerateAndSendOtpAsync(request.PhoneNumber, userFullName);
        
        // Return success with masked phone number and OTP info
        return Result<VerifyUserForDataDeletionResponse>.Success(new VerifyUserForDataDeletionResponse
        {
            Success = true,
            OtpSent = true,
            MaskedPhone = MaskedPhone(request.PhoneNumber),
            ExpirationMinutes = OTP_EXPIRATION_MINUTES
        });
    }
}
```

### 2. CreateDeleteDataRequest UseCase

After user verification, this use case creates a new data deletion request with the provided details.

```csharp
public class CreateDeleteDataRequestUseCase : UseCaseBase<CreateDeleteDataRequestRequest, DeleteDataRequestResponse>
{
    // Dependencies
    private readonly IValidator<CreateDeleteDataRequestRequest> _validator;
    private readonly IDeleteDataRequestRepository _deleteDataRequestRepository;
    private readonly EmailVerificationService _emailVerificationService;
    private readonly IUnitOfWork _unitOfWork;
    
    public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
        CreateDeleteDataRequestRequest request, 
        CancellationToken cancellationToken = default)
    {
        // Validate the request
        // Check permissions
        
        // Create the DeleteDataRequest domain entity
        var deleteDataRequest = DeleteDataRequest.Create(
            request.Email,
            request.Type,
            request.ScheduleType,
            request.BranchIds,
            DeleteDataRequestStatus.Approved,
            request.ScheduleType == DeleteDataScheduleType.Immediate ? DateTime.UtcNow : null);
        
        // Save to repository
        await _deleteDataRequestRepository.AddAsync(deleteDataRequest);
        await _unitOfWork.SaveChangesAsync();
        
        // Return the created request details
        return Result<DeleteDataRequestResponse>.Success(new DeleteDataRequestResponse
        {
            Id = deleteDataRequest.Id,
            Email = deleteDataRequest.Email,
            Status = deleteDataRequest.Status,
            CreatedAt = deleteDataRequest.CreatedAt,
            CreatedBy = deleteDataRequest.CreatedBy,
            Type = deleteDataRequest.Type,
            ScheduleType = deleteDataRequest.ScheduleType,
            BranchIds = deleteDataRequest.BranchIds,
            ScheduleConfig = deleteDataRequest.ScheduleConfig,
            ExecutedDate = deleteDataRequest.ExecutedDate
        });
    }
}
```

### 3. UpdateDeleteDataRequest UseCase

Similar to the create use case, this updates an existing request after verifying the OTP.

```csharp
public class UpdateDeleteDataRequestUseCase : UseCaseBase<UpdateDeleteDataRequestRequest, DeleteDataRequestResponse>
{
    // Dependencies
    private readonly IValidator<UpdateDeleteDataRequestRequest> _validator;
    private readonly IDeleteDataRequestRepository _deleteDataRequestRepository;
    private readonly EmailVerificationService _emailVerificationService;
    private readonly IUnitOfWork _unitOfWork;
    
    public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
        UpdateDeleteDataRequestRequest request, 
        CancellationToken cancellationToken = default)
    {
        // Validate the request
        // Check permissions
        
        // Get the existing request
        var deleteDataRequest = await _deleteDataRequestRepository.GetByIdAsync(request.Id);
        if (deleteDataRequest == null)
        {
            return Result<DeleteDataRequestResponse>.Failure("Delete data request not found");
        }
        
        // Verify OTP
        var otpVerified = await _emailVerificationService.VerifyOtpAsync(deleteDataRequest.Email, request.Otp);
        if (!otpVerified)
        {
            return Result<DeleteDataRequestResponse>.Failure("Invalid or expired OTP");
        }
        
        // Update request fields
        if (request.Status == DeleteDataRequestStatus.Approved)
        {
            deleteDataRequest.Approve();
        }
        else if (request.Status == DeleteDataRequestStatus.Cancelled)
        {
            deleteDataRequest.Cancel();
        }
        
        // Update other properties
        deleteDataRequest.Type = request.Type;
        deleteDataRequest.ScheduleType = request.ScheduleType;
        deleteDataRequest.BranchIds = request.BranchIds;
        deleteDataRequest.ScheduleConfig = request.ScheduleConfig;
        
        // Save changes
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Return the updated request details
        return Result<DeleteDataRequestResponse>.Success(new DeleteDataRequestResponse
        {
            Id = deleteDataRequest.Id,
            Status = deleteDataRequest.Status,
            Email = deleteDataRequest.Email,
            ModifiedAt = deleteDataRequest.ModifiedAt ?? DateTime.UtcNow,
            // Add other required fields here
            CreatedBy = deleteDataRequest.CreatedBy,
            ModifiedBy = deleteDataRequest.ModifiedBy,
            Type = deleteDataRequest.Type,
            ScheduleType = deleteDataRequest.ScheduleType,
            BranchIds = deleteDataRequest.BranchIds,
            ScheduleConfig = deleteDataRequest.ScheduleConfig,
            CreatedAt = deleteDataRequest.CreatedAt,
            ExecutedDate = deleteDataRequest.ExecutedDate,
            CompletedDate = deleteDataRequest.CompletedDate
        });
    }
}
```

### 4. DeleteDeleteDataRequest UseCase

This use case allows the deletion of a data deletion request after verifying the OTP.

```csharp
public static IServiceCollection RegisterDataManagementModule(this IServiceCollection services, IConfiguration configuration)
{
    // Register configurations
    services.Configure<DataManagementOptions>(configuration.GetSection("DataManagement"));
    
    // Register automapper profiles
    services.AddAutoMapper(Assembly.GetExecutingAssembly());
    
    // Register services
    services.AddScoped<EmailVerificationService>();
    services.AddScoped<DataDeletionProcessingService>();
    
    // Register validators
    services.AddScoped<IValidator<VerifyUserForDataDeletionRequest>, VerifyUserForDataDeletionValidator>();
    services.AddScoped<IValidator<CreateDeleteDataRequestRequest>, CreateDeleteDataRequestValidator>();
    services.AddScoped<IValidator<UpdateDeleteDataRequestRequest>, UpdateDeleteDataRequestValidator>();
    
    // Register repositories
    services.AddScoped<IDeleteDataRequestRepository, DeleteDataRequestRepository>();
    services.AddScoped<IDeleteDataHistoryRepository, DeleteDataHistoryRepository>();
    
    // Register use cases
    services.AddScoped<VerifyUserForDataDeletionUseCase>();
    services.AddScoped<CreateDeleteDataRequestUseCase>();
    services.AddScoped<UpdateDeleteDataRequestUseCase>();
    services.AddScoped<GetAllDeleteDataRequestsUseCase>();
    services.AddScoped<GetAllDeleteDataHistoryUseCase>();
    
    // Register entity configurations
    services.AddEFConfigurations<DeleteDataRequestConfiguration>();
    services.AddEFConfigurations<DeleteDataHistoryConfiguration>();
    services.AddEFConfigurations<DeleteDataDetailConfiguration>();
    
    return services;
}
```

## 1. Domain Layer Implementation

### 1.1 Domain Entities

#### DeleteDataRequest Entity

```csharp
using System;
using System.Collections.Generic;
using KvFnB.Core.Domain;
using KvFnB.Core.Domain.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Enums;

namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    public class DeleteDataRequest : AggregateRoot<long>, IAuditableEntity
    {
        // Properties
        public string Email { get; private set; }
        public DeleteDataRequestStatus Status { get; private set; }
        public DeleteDataScheduleType ScheduleType { get; private set; }
        public DeleteDataType Type { get; private set; }
        public string? BranchIds { get; private set; }
        public string? ScheduleConfig { get; private set; }
        public DateTime? ExecutedDate { get; private set; }
        public DateTime? CompletedDate { get; private set; }
        
        // IAuditableEntity properties
        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? ModifiedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        
        // Navigation properties
        public IReadOnlyCollection<DeleteDataDetail> DeleteDataDetails => _deleteDataDetails.AsReadOnly();
        private readonly List<DeleteDataDetail> _deleteDataDetails = new();

        // Constructor (private to enforce creation via factory method)
        private DeleteDataRequest() { }

        // Factory method for creating a new DeleteDataRequest
        public static DeleteDataRequest Create(
            string? email,
            DeleteDataType type,
            DeleteDataScheduleType scheduleType,
            string? branchIds,
            DeleteDataRequestStatus status,
            DateTime? executedDate)
        {
            var request = new DeleteDataRequest
            {
                Email = email,
                Type = type,
                ScheduleType = scheduleType,
                BranchIds = branchIds,
                Status = status,
                ExecutedDate = executedDate
            };
            return request;
        }

        // Methods for state changes
        public void Approve()
        {
            if (Status != DeleteDataRequestStatus.Pending)
                throw new InvalidOperationException("Can only approve pending requests");
                
            Status = DeleteDataRequestStatus.Approved;
            ModifiedAt = DateTime.UtcNow;
        }

        public void MarkAsExecuting()
        {
            if (Status != DeleteDataRequestStatus.Approved)
                throw new InvalidOperationException("Can only execute approved requests");
                
            Status = DeleteDataRequestStatus.Executing;
            ExecutedDate = DateTime.UtcNow;
            ModifiedAt = DateTime.UtcNow;
        }

        public void Complete()
        {
            if (Status != DeleteDataRequestStatus.Executing)
                throw new InvalidOperationException("Can only complete executing requests");
                
            Status = DeleteDataRequestStatus.Completed;
            CompletedDate = DateTime.UtcNow;
            ModifiedAt = DateTime.UtcNow;
        }

        public void Cancel()
        {
            if (Status != DeleteDataRequestStatus.Pending && Status != DeleteDataRequestStatus.Approved)
                throw new InvalidOperationException("Can only cancel pending or approved requests");
                
            Status = DeleteDataRequestStatus.Cancelled;
            ModifiedAt = DateTime.UtcNow;
        }
        
        public void AddDeleteDataDetail(DeleteDataDetail detail)
        {
            _deleteDataDetails.Add(detail);
        }

        // Method to update configuration
        public void UpdateScheduleConfig(string scheduleConfig)
        {
            if (Status != DeleteDataRequestStatus.Pending)
                throw new InvalidOperationException("Can only update pending requests");
                
            ScheduleConfig = scheduleConfig;
            ModifiedAt = DateTime.UtcNow;
        }
    }
}
```

#### DeleteDataHistory Entity

```csharp
using System;
using KvFnB.Core.Domain;
using KvFnB.Core.Domain.Abstractions;

namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    public class DeleteDataHistory : AggregateRoot<long>
    {
        // Properties
        public long RequestId { get; private set; }
        public string TableName { get; private set; }
        public string Summary { get; private set; }
        public long ExecutedById { get; private set; }
        public DateTime ExecutedDate { get; private set; }

        // Constructor (private to enforce creation via factory method)
        private DeleteDataHistory() { }

        // Factory method for creating a new DeleteDataHistory
        public static DeleteDataHistory Create(
            long requestId,
            string tableName,
            string summary,
            long executedById)
        {
            return new DeleteDataHistory
            {
                RequestId = requestId,
                TableName = tableName,
                Summary = summary,
                ExecutedById = executedById,
                ExecutedDate = DateTime.UtcNow
            };
        }
    }
}
```

#### DeleteDataDetail Entity

```csharp
using System;
using KvFnB.Core.Domain;
using KvFnB.Core.Domain.Abstractions;

namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    public class DeleteDataDetail : Entity<long>, ICreatedAt
    {
        // Properties
        public long RequestId { get; private set; }
        public string Type { get; private set; }
        public DateTime? FromDate { get; private set; }
        public DateTime? ToDate { get; private set; }
        public int? BatchSize { get; private set; }
        public int? TimeoutSeconds { get; private set; }
        public string FilterCondition { get; private set; }
        public int SortOrder { get; private set; }
        public bool IsProcessed { get; private set; }
        public DateTime CreatedAt { get; set; }
        
        // Navigation properties
        public DeleteDataRequest DeleteDataRequest { get; private set; }

        // Constructor (private to enforce creation via factory method)
        private DeleteDataDetail() { }

        // Factory method for creating a new DeleteDataDetail
        public static DeleteDataDetail Create(
            long requestId,
            string tableName,
            string filterCondition,
            int sortOrder,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int? batchSize = null,
            int? timeoutSeconds = null)
        {
            return new DeleteDataDetail
            {
                RequestId = requestId,
                Type = tableName,
                FromDate = fromDate,
                ToDate = toDate,
                BatchSize = batchSize,
                TimeoutSeconds = timeoutSeconds,
                FilterCondition = filterCondition,
                SortOrder = sortOrder,
                IsProcessed = false
            };
        }
        
        // Method to mark detail as processed
        public void MarkAsProcessed()
        {
            IsProcessed = true;
        }
    }
}
```

### 1.2 Domain Enums

#### DeleteDataRequestStatus Enum

```csharp
namespace KvFnB.Modules.DataManagement.Domain.Enums
{
    public enum DeleteDataRequestStatus
    {
        Pending = 0,
        Approved = 1,
        Executing = 2,
        Completed = 3,
        Cancelled = 4
    }
}
```

#### DeleteDataScheduleType Enum

```csharp
namespace KvFnB.Modules.DataManagement.Domain.Enums
{
    public enum DeleteDataScheduleType
    {
        Manual = 0,
        Scheduled = 1
    }
}
```

#### DeleteDataType Enum

```csharp
namespace KvFnB.Modules.DataManagement.Domain.Enums
{
    public enum DeleteDataType
    {
        All = 0,
        FinancialOnly = 1
    }
}
```

## 2. Application Layer Implementation

### 2.1 Interfaces

#### IDeleteDataRequestRepository Interface

```csharp
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;

namespace KvFnB.Modules.DataManagement.Domain.Repositories
{
    public interface IDeleteDataRequestRepository : IRepository<DeleteDataRequest, long>
    {
        Task<DeleteDataRequest> GetByIdAsync(long id, CancellationToken cancellationToken = default);
        Task<IEnumerable<DeleteDataRequest>> GetAllByTenantIdAsync(int tenantId, CancellationToken cancellationToken = default);
        Task<IEnumerable<DeleteDataRequest>> GetAllByStatusAsync(int tenantId, DeleteDataRequestStatus status, CancellationToken cancellationToken = default);
        Task<IEnumerable<DeleteDataRequest>> GetScheduledRequestsDueForExecutionAsync(CancellationToken cancellationToken = default);
        Task<bool> ExistsWithEmailAndPendingStatusAsync(int tenantId, string email, CancellationToken cancellationToken = default);
    }
}
```

#### IDeleteDataHistoryRepository Interface

```csharp
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Entities;

namespace KvFnB.Modules.DataManagement.Domain.Repositories
{
    public interface IDeleteDataHistoryRepository : IRepository<DeleteDataHistory, long>
    {
        Task<IEnumerable<DeleteDataHistory>> GetAllByRequestIdAsync(long requestId, CancellationToken cancellationToken = default);
        Task<IEnumerable<DeleteDataHistory>> GetAllByTenantIdAsync(int tenantId, CancellationToken cancellationToken = default);
    }
}
```

### 2.2 DTOs

#### DeleteDataRequestDto.cs

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using KvFnB.Modules.DataManagement.Domain.Enums;

namespace KvFnB.Modules.DataManagement.Application.Contracts
{
    public class DeleteDataRequestDto
    {
        [JsonPropertyName("id"), Description("The unique identifier of the delete data request.")]
        public long Id { get; set; }
        
        [JsonPropertyName("created_by"), Description("The user who created this request.")]
        public long CreatedBy { get; set; }
        
        [JsonPropertyName("modified_by"), Description("The user who last modified this request.")]
        public long? ModifiedBy { get; set; }
        
        [JsonPropertyName("email"), Description("The email address for verification and notifications.")]
        public string Email { get; set; }
        
        [JsonPropertyName("status"), Description("The current status of the delete data request.")]
        public DeleteDataRequestStatus Status { get; set; }
        
        [JsonPropertyName("schedule_type"), Description("The type of schedule for this request.")]
        public DeleteDataScheduleType ScheduleType { get; set; }
        
        [JsonPropertyName("type"), Description("The type of data to delete.")]
        public DeleteDataType Type { get; set; }
        
        [JsonPropertyName("branch_ids"), Description("Comma-separated list of branch IDs to include in the deletion.")]
        public string? BranchIds { get; set; }
        
        [JsonPropertyName("schedule_config"), Description("JSON configuration for scheduled deletion.")]
        public string? ScheduleConfig { get; set; }
        
        [JsonPropertyName("created_at"), Description("The date and time when this request was created.")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("modified_at"), Description("The date and time when this request was last modified.")]
        public DateTime? ModifiedAt { get; set; }
        
        [JsonPropertyName("executed_date"), Description("The date and time when this request was executed.")]
        public DateTime? ExecutedDate { get; set; }
        
        [JsonPropertyName("completed_date"), Description("The date and time when this request was completed.")]
        public DateTime? CompletedDate { get; set; }
    }
}
```

#### DeleteDataHistoryDto.cs

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.DataManagement.Application.Contracts
{
    public class DeleteDataHistoryDto
    {
        [JsonPropertyName("id"), Description("The unique identifier of the delete data history record.")]
        public long Id { get; set; }
        
        [JsonPropertyName("request_id"), Description("The ID of the delete data request this history belongs to.")]
        public long RequestId { get; set; }
        
        [JsonPropertyName("table_name"), Description("The name of the database table affected.")]
        public string TableName { get; set; }
        
        [JsonPropertyName("summary"), Description("JSON summary of records deleted.")]
        public string Summary { get; set; }
        
        [JsonPropertyName("executed_by_id"), Description("The ID of the user who executed this deletion.")]
        public long ExecutedById { get; set; }
        
        [JsonPropertyName("executed_date"), Description("The date and time when this deletion was executed.")]
        public DateTime ExecutedDate { get; set; }
        
        [JsonPropertyName("created_at"), Description("The date and time when this history record was created.")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("modified_at"), Description("The date and time when this history record was last modified.")]
        public DateTime? ModifiedAt { get; set; }
    }
}
```

#### DeleteDataDetailDto.cs

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.DataManagement.Application.Contracts
{
    public class DeleteDataDetailDto
    {
        [JsonPropertyName("id"), Description("The unique identifier of the delete data detail record.")]
        public long Id { get; set; }      
        
        [JsonPropertyName("request_id"), Description("The ID of the delete data request this detail belongs to.")]
        public long RequestId { get; set; }
        
        [JsonPropertyName("table_name"), Description("The name of the database table affected.")]
        public string TableName { get; set; }
        
        [JsonPropertyName("from_date"), Description("The start date of the data range to delete.")]
        public DateTime? FromDate { get; set; }
        
        [JsonPropertyName("to_date"), Description("The end date of the data range to delete.")]
        public DateTime? ToDate { get; set; }
        
        [JsonPropertyName("batch_size"), Description("The batch size for the data deletion process.")]
        public int? BatchSize { get; set; }
        
        [JsonPropertyName("timeout_seconds"), Description("The timeout in seconds for the data deletion process.")]
        public int? TimeoutSeconds { get; set; }
        
        [JsonPropertyName("filter_condition"), Description("The filter condition for the data deletion process.")]
        public string FilterCondition { get; set; }
        
        [JsonPropertyName("sort_order"), Description("The sort order for the data deletion process.")]
        public int SortOrder { get; set; }
        
        [JsonPropertyName("is_processed"), Description("Indicates whether the data deletion detail has been processed.")]
        public bool IsProcessed { get; set; }
        
        [JsonPropertyName("created_at"), Description("The date and time when this detail record was created.")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("modified_at"), Description("The date and time when this detail record was last modified.")]
        public DateTime? ModifiedAt { get; set; }
    }
}
```

### 2.3 Mapping Profiles

```csharp
using AutoMapper;
using KvFnB.Modules.DataManagement.Application.Contracts;
using KvFnB.Modules.DataManagement.Domain.Entities;

namespace KvFnB.Modules.DataManagement.Application.Mapping
{
    public class DataManagementMappingProfile : Profile
    {
        public DataManagementMappingProfile()
        {
            // Domain -> DTO
            CreateMap<DeleteDataRequest, DeleteDataRequestDto>();
            CreateMap<DeleteDataHistory, DeleteDataHistoryDto>();
            CreateMap<DeleteDataDetail, DeleteDataDetailDto>();
            
            // Use case specific mappings can be added here
            CreateMap<DeleteDataRequest, DeleteDataRequestResponse>();
            CreateMap<DeleteDataRequest, GetDeleteDataRequestResponse>();
            CreateMap<DeleteDataRequest, GetAllDeleteDataRequestsResponse>();
            
            CreateMap<DeleteDataHistory, GetAllDeleteDataHistoryResponse>();
        }
    }
}
```

### 2.4 Use Cases

#### VerifyUserForDataDeletion

##### Request Model

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Interfaces;
using KvFnB.Modules.DataManagement.Application.Services;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Shared.Constants;
using Microsoft.Extensions.Logging;

namespace KvFnB.Modules.DataManagement.Application.UseCases.VerifyUserUseCases.VerifyUserForDataDeletion
{
    public class VerifyUserForDataDeletionRequest
    {
        [JsonPropertyName("request_id"), Description("The ID of the validated delete data request.")]
        public long RequestId { get; set; }
        
        [JsonPropertyName("password"), Description("The user's password for verification.")]
        public string Password { get; set; }
        
        [JsonPropertyName("phone_number"), Description("The user's phone number for verification with Kfin service.")]
        public string PhoneNumber { get; set; }
    }
    
    public class VerifyUserForDataDeletionResponse
    {
        [JsonPropertyName("success"), Description("Indicates if the verification was successful.")]
        public bool Success { get; set; }
        
        [JsonPropertyName("otp_sent"), Description("Indicates if the OTP was sent successfully after verification.")]
        public bool OtpSent { get; set; }
        
        [JsonPropertyName("masked_email"), Description("The masked email address to which the OTP was sent.")]
        public string MaskedEmail { get; set; }
        
        [JsonPropertyName("expiration_minutes"), Description("The number of minutes until the OTP expires.")]
        public int ExpirationMinutes { get; set; }
    }
    
    public class VerifyUserForDataDeletionValidator : Validator<VerifyUserForDataDeletionRequest>
    {
        public VerifyUserForDataDeletionValidator()
        {
            RuleFor(x => x.RequestId)
                .GreaterThan(0, "Request ID must be greater than zero");
                
            RuleFor(x => x.Password)
                .NotNullOrEmpty("Password is required");
                
            RuleFor(x => x.PhoneNumber)
                .NotNullOrEmpty("Phone number is required")
                .Matches(@"^\d+$", "Phone number must contain only digits");
        }
    }
    
    public class VerifyUserForDataDeletionUseCase(
        IValidator<VerifyUserForDataDeletionRequest> validator,
        IDeleteDataRequestRepository deleteDataRequestRepository,
        IUserService userService,
        IKvFinService kvFinService,
        EmailVerificationService emailVerificationService,
        ITenantProvider tenantProvider,
        IAuthUser authUser,
        ILogger logger) : UseCaseBase<VerifyUserForDataDeletionRequest, VerifyUserForDataDeletionResponse>
    {
        private readonly IValidator<VerifyUserForDataDeletionRequest> _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
        private readonly IUserService _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        private readonly IKvFinService _kvFinService = kvFinService ?? throw new ArgumentNullException(nameof(kvFinService));
        private readonly EmailVerificationService _emailVerificationService = emailVerificationService ?? throw new ArgumentNullException(nameof(emailVerificationService));
        private readonly ITenantProvider _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        private readonly IAuthUser _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
        private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private const int OTP_EXPIRATION_MINUTES = 5;

        public override async Task<Result<VerifyUserForDataDeletionResponse>> ExecuteAsync(
            VerifyUserForDataDeletionRequest request, 
        CancellationToken cancellationToken = default)
    {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<VerifyUserForDataDeletionResponse>.Failure(validationResult.Errors);
            }
            
            // Check if user has permission
            if (!_authUser.HasAnyPermission([PermissionConsts.DataManagement_Create]))
            {
                return Result<VerifyUserForDataDeletionResponse>.Failure(ErrorMessages.UnauthorizedAccess);
            }
            
            var tenantId = _tenantProvider.GetTenantId() ?? 0;
            var userId = _authUser.UserId;
            
            // Get the delete data request
            var deleteDataRequest = await _deleteDataRequestRepository.GetByIdAsync(request.RequestId, cancellationToken);
            if (deleteDataRequest == null || deleteDataRequest.TenantId != tenantId)
            {
                return Result<VerifyUserForDataDeletionResponse>.Failure("Delete data request not found");
            }
            
            try
            {
                // Step 1: Verify password against User DB
                var passwordVerified = await _userService.VerifyUserPasswordAsync(
                    userId, request.Password, cancellationToken);
                
                if (!passwordVerified)
                {
                    return Result<VerifyUserForDataDeletionResponse>.Failure("Invalid password");
                }
                
                // Step 2: Verify phone number with Kfin service
                var user = await _userService.GetUserByIdAsync(userId, cancellationToken);
                if (user == null)
                {
                    return Result<VerifyUserForDataDeletionResponse>.Failure("User not found");
                }
                
                var phoneVerified = await _kvFinService.VerifyPhoneNumberAsync(
                    tenantId, userId, request.PhoneNumber, cancellationToken);
                
                if (!phoneVerified)
                {
                    return Result<VerifyUserForDataDeletionResponse>.Failure("Phone number verification failed");
                }
                
                // Both verifications succeeded, now send OTP
                var userFullName = user.FullName ?? "User";
                await _emailVerificationService.GenerateAndSendOtpAsync(
                    deleteDataRequest.Email, userFullName, cancellationToken);
                
                // Return masked email for privacy
                var maskedEmail = MaskEmail(deleteDataRequest.Email);
                
                return Result<VerifyUserForDataDeletionResponse>.Success(new VerifyUserForDataDeletionResponse
                {
                    Success = true,
                    OtpSent = true,
                    MaskedEmail = maskedEmail,
                    ExpirationMinutes = OTP_EXPIRATION_MINUTES
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user verification for data deletion request");
                return Result<VerifyUserForDataDeletionResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
        
        private static string MaskEmail(string email)
        {
            if (string.IsNullOrEmpty(email))
                return string.Empty;
                
            var parts = email.Split('@');
            if (parts.Length != 2)
                return email;
                
            var username = parts[0];
            var domain = parts[1];
            
            if (username.Length <= 2)
                return email;
                
            var maskedUsername = $"{username[0]}{"*".PadLeft(username.Length - 2, '*')}{username[^1]}";
            return $"{maskedUsername}@{domain}";
        }
    }
}
```

### 5. GetAllDeleteDataRequests UseCase

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Interfaces;
using KvFnB.Modules.DataManagement.Application.Services;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Shared.Constants;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest
{
    public class CreateDeleteDataRequestRequest
    {
        [JsonPropertyName("email"), Description("The email address for verification and notifications.")]
        public string Email { get; set; }
        
        [JsonPropertyName("otp"), Description("The OTP code received via email during verification.")]
        public string Otp { get; set; }
        
        [JsonPropertyName("type"), Description("The type of data to delete.")]
        public DeleteDataType Type { get; set; }
        
        [JsonPropertyName("schedule_type"), Description("The type of schedule for this request.")]
        public DeleteDataScheduleType ScheduleType { get; set; }
        
        [JsonPropertyName("branch_ids"), Description("Comma-separated list of branch IDs to include in the deletion.")]
        public string? BranchIds { get; set; }
        
        [JsonPropertyName("schedule_config"), Description("JSON configuration for scheduled deletion.")]
        public string? ScheduleConfig { get; set; }
    }
    
    public class DeleteDataRequestResponse
    {
        [JsonPropertyName("id"), Description("The unique identifier of the delete data request.")]
        public long Id { get; set; }
        
        [JsonPropertyName("created_by"), Description("The user who created this request.")]
        public long CreatedBy { get; set; }
        
        [JsonPropertyName("modified_by"), Description("The user who last modified this request.")]
        public long? ModifiedBy { get; set; }
        
        [JsonPropertyName("email"), Description("The email address for verification and notifications.")]
        public string Email { get; set; }
        
        [JsonPropertyName("status"), Description("The current status of the delete data request.")]
        public DeleteDataRequestStatus Status { get; set; }
        
        [JsonPropertyName("schedule_type"), Description("The type of schedule for this request.")]
        public DeleteDataScheduleType ScheduleType { get; set; }
        
        [JsonPropertyName("type"), Description("The type of data to delete.")]
        public DeleteDataType Type { get; set; }
        
        [JsonPropertyName("branch_ids"), Description("Comma-separated list of branch IDs to include in the deletion.")]
        public string? BranchIds { get; set; }
        
        [JsonPropertyName("schedule_config"), Description("JSON configuration for scheduled deletion.")]
        public string? ScheduleConfig { get; set; }
        
        [JsonPropertyName("created_at"), Description("The date and time when this request was created.")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("modified_at"), Description("The date and time when this request was last modified.")]
        public DateTime? ModifiedAt { get; set; }
        
        [JsonPropertyName("executed_date"), Description("The date and time when this request was executed.")]
        public DateTime? ExecutedDate { get; set; }
        
        [JsonPropertyName("completed_date"), Description("The date and time when this request was completed.")]
        public DateTime? CompletedDate { get; set; }
    }
    
    public class CreateDeleteDataRequestValidator : Validator<CreateDeleteDataRequestRequest>
    {
        private static readonly Regex EmailRegex = 
            new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 
                RegexOptions.Compiled);
        
        public CreateDeleteDataRequestValidator()
        {
            RuleFor(x => x.Email)
                .NotNullOrEmpty("Email is required")
                .Must(email => EmailRegex.IsMatch(email), "Email is not in a valid format");

            RuleFor(x => x.Otp)
                .NotNullOrEmpty("OTP is required")
                .Length(6, 6, "OTP must be 6 digits");

            RuleFor(x => x.ScheduleType)
                .Must(type => Enum.IsDefined(typeof(DeleteDataScheduleType), type),
                    "Invalid schedule type");

            RuleFor(x => x.Type)
                .Must(type => Enum.IsDefined(typeof(DeleteDataType), type),
                    "Invalid delete data type");
                
            RuleFor(x => x.ScheduleConfig)
                .NotNullOrEmpty("Schedule configuration is required")
                .When(x => x.ScheduleType == DeleteDataScheduleType.Scheduled, 
                    "Schedule configuration is required for scheduled deletions");
                
            // Branch IDs can be empty, but if provided must be valid format
            RuleFor(x => x.BranchIds)
                .Must(ids => string.IsNullOrEmpty(ids) || IsValidBranchIdsFormat(ids),
                    "Branch IDs must be comma-separated numeric values");
        }
        
        private bool IsValidBranchIdsFormat(string branchIds)
        {
            if (string.IsNullOrEmpty(branchIds))
                return true;
                
            return branchIds.Split(',')
                .All(id => long.TryParse(id.Trim(), out _));
        }
    }
    
    public class CreateDeleteDataRequestUseCase(
        IValidator<CreateDeleteDataRequestRequest> validator,
        IDeleteDataRequestRepository deleteDataRequestRepository,
        IUnitOfWork unitOfWork,
        EmailVerificationService emailVerificationService,
        IMapper mapper,
        ITenantProvider tenantProvider,
        IAuthUser authUser) : UseCaseBase<CreateDeleteDataRequestRequest, DeleteDataRequestResponse>
    {
        private readonly IValidator<CreateDeleteDataRequestRequest> _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
        private readonly IUnitOfWork _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        private readonly EmailVerificationService _emailVerificationService = emailVerificationService ?? throw new ArgumentNullException(nameof(emailVerificationService));
        private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        private readonly ITenantProvider _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        private readonly IAuthUser _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));

        public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
            CreateDeleteDataRequestRequest request, 
            CancellationToken cancellationToken = default)
        {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<DeleteDataRequestResponse>.Failure(validationResult.Errors);
            }
            
            // Check if user has permission
            if (!_authUser.HasAnyPermission([PermissionConsts.DataManagement_Create]))
            {
                return Result<DeleteDataRequestResponse>.Failure(ErrorMessages.UnauthorizedAccess);
            }
            
            var tenantId = _tenantProvider.GetTenantId() ?? 0;
            
            // Check if there's already a pending request with the same email
            var existingRequest = await _deleteDataRequestRepository.ExistsWithEmailAndPendingStatusAsync(
                tenantId, request.Email, cancellationToken);
                
            if (existingRequest)
            {
                return Result<DeleteDataRequestResponse>.Failure("A pending request already exists for this email address");
            }
            
            // Create the DeleteDataRequest domain entity
            var deleteDataRequest = DeleteDataRequest.Create(
                request.Email,
                request.Type,
                request.ScheduleType,
                request.BranchIds,
                DeleteDataRequestStatus.Approved,
                request.ScheduleType == DeleteDataScheduleType.Immediate ? DateTime.UtcNow : null);
            
            // Save to repository
            await _deleteDataRequestRepository.AddAsync(deleteDataRequest, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // Map to response and return
            var response = _mapper.Map<DeleteDataRequestResponse>(deleteDataRequest);
            
            return Result<DeleteDataRequestResponse>.Success(response);
        }
    }
}
```

#### UpdateDeleteDataRequest

```csharp
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Interfaces;
using KvFnB.Modules.DataManagement.Application.Services;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Shared.Constants;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest
{
    public class UpdateDeleteDataRequestRequest
    {
        [JsonPropertyName("id"), Description("The ID of the delete data request to update.")]
        public long Id { get; set; }
        
        [JsonPropertyName("otp"), Description("The OTP code received via email.")]
        public string Otp { get; set; }
        
        [JsonPropertyName("status"), Description("The new status to set for the request.")]
        public DeleteDataRequestStatus Status { get; set; }
        
        [JsonPropertyName("type"), Description("The type of data to delete.")]
        public DeleteDataType Type { get; set; }
        
        [JsonPropertyName("schedule_type"), Description("The type of schedule for this request.")]
        public DeleteDataScheduleType ScheduleType { get; set; }
        
        [JsonPropertyName("branch_ids"), Description("Comma-separated list of branch IDs to include in the deletion.")]
        public string? BranchIds { get; set; }
        
        [JsonPropertyName("schedule_config"), Description("JSON configuration for scheduled deletion.")]
        public string? ScheduleConfig { get; set; }
    }
    
    public class UpdateDeleteDataRequestValidator : Validator<UpdateDeleteDataRequestRequest>
    {
        public UpdateDeleteDataRequestValidator()
        {
            RuleFor(x => x.Id)
                .GreaterThan(0, "Request ID must be greater than zero");
                
            RuleFor(x => x.Otp)
                .NotNullOrEmpty("OTP is required")
                .Length(6, 6, "OTP must be 6 digits");
                
            RuleFor(x => x.Status)
                .Must(status => status == DeleteDataRequestStatus.Approved || 
                                status == DeleteDataRequestStatus.Cancelled,
                    "Status must be either Approved or Cancelled");
        }
    }
    
    public class UpdateDeleteDataRequestUseCase(
        IValidator<UpdateDeleteDataRequestRequest> validator,
        IDeleteDataRequestRepository deleteDataRequestRepository,
        IUnitOfWork unitOfWork,
        EmailVerificationService emailVerificationService,
        IMapper mapper,
        ITenantProvider tenantProvider,
        IAuthUser authUser) : UseCaseBase<UpdateDeleteDataRequestRequest, DeleteDataRequestResponse>
    {
        private readonly IValidator<UpdateDeleteDataRequestRequest> _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
        private readonly IUnitOfWork _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        private readonly EmailVerificationService _emailVerificationService = emailVerificationService ?? throw new ArgumentNullException(nameof(emailVerificationService));
        private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        private readonly ITenantProvider _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        private readonly IAuthUser _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));

        public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
            UpdateDeleteDataRequestRequest request, 
            CancellationToken cancellationToken = default)
        {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<DeleteDataRequestResponse>.Failure(validationResult.Errors);
            }
            
            // Check if user has permission
            if (!_authUser.HasAnyPermission([PermissionConsts.DataManagement_Update]))
            {
                return Result<DeleteDataRequestResponse>.Failure(ErrorMessages.UnauthorizedAccess);
            }
            
            var tenantId = _tenantProvider.GetTenantId() ?? 0;
            
            // Get the delete data request
            var deleteDataRequest = await _deleteDataRequestRepository.GetByIdAsync(request.Id, cancellationToken);
            if (deleteDataRequest == null || deleteDataRequest.TenantId != tenantId)
            {
                return Result<DeleteDataRequestResponse>.Failure("Delete data request not found");
            }
            
            // Verify OTP
            var otpVerified = await _emailVerificationService.VerifyOtpAsync(
                deleteDataRequest.Email, request.Otp, cancellationToken);
                
            if (!otpVerified)
            {
                return Result<DeleteDataRequestResponse>.Failure("Invalid or expired OTP");
            }
            
            // Update the status based on the request
            if (request.Status == DeleteDataRequestStatus.Approved)
            {
                deleteDataRequest.Approve();
            }
            else if (request.Status == DeleteDataRequestStatus.Cancelled)
            {
                deleteDataRequest.Cancel();
            }
            else
            {
                return Result<DeleteDataRequestResponse>.Failure("Invalid status update requested");
            }
            
            // Update other properties
            deleteDataRequest.Type = request.Type;
            deleteDataRequest.ScheduleType = request.ScheduleType;
            deleteDataRequest.BranchIds = request.BranchIds;
            deleteDataRequest.ScheduleConfig = request.ScheduleConfig;
            
            // Save changes
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // Map to response and return
            var response = _mapper.Map<DeleteDataRequestResponse>(deleteDataRequest);
            
            return Result<DeleteDataRequestResponse>.Success(response);
        }
    }
}
```

#### GetAllDeleteDataRequests

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Modules.DataManagement.Application.Interfaces;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Shared.Constants;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetAllDeleteDataRequests
{
    public class GetAllDeleteDataRequestsRequest
    {
        [JsonPropertyName("tenant_id"), Description("The ID of the tenant.")]
        public int TenantId { get; set; }
    }
    
    public class GetAllDeleteDataRequestsResponse
    {
        [JsonPropertyName("data"), Description("List of data deletion requests.")]
        public List<DeleteDataRequestDto> Data { get; set; } = new();
    }
    
    public class GetAllDeleteDataRequestsUseCase(
        IDeleteDataRequestRepository deleteDataRequestRepository,
        IUnitOfWork unitOfWork,
        ITenantProvider tenantProvider,
        IAuthUser authUser) : UseCaseBase<GetAllDeleteDataRequestsRequest, GetAllDeleteDataRequestsResponse>
    {
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
        private readonly IUnitOfWork _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        private readonly ITenantProvider _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        private readonly IAuthUser _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
    
    public override async Task<Result<GetAllDeleteDataRequestsResponse>> ExecuteAsync(
        GetAllDeleteDataRequestsRequest request, 
        CancellationToken cancellationToken = default)
    {
            // Check if user has permission
            if (!_authUser.HasAnyPermission([PermissionConsts.DataManagement_Read]))
            {
                return Result<GetAllDeleteDataRequestsResponse>.Failure(ErrorMessages.UnauthorizedAccess);
            }
            
            var tenantId = _tenantProvider.GetTenantId() ?? 0;
            
            // Get all data deletion requests
            var deleteDataRequests = await _deleteDataRequestRepository.GetAllByTenantIdAsync(tenantId, cancellationToken);
            
            // Map to response and return
            var response = new GetAllDeleteDataRequestsResponse
            {
                Data = deleteDataRequests.Select(x => new DeleteDataRequestDto
                {
                    Id = x.Id,
                    CreatedBy = x.CreatedBy,
                    ModifiedBy = x.ModifiedBy,
                    Email = x.Email,
                    Status = x.Status,
                    ScheduleType = x.ScheduleType,
                    Type = x.Type,
                    BranchIds = x.BranchIds,
                    ScheduleConfig = x.ScheduleConfig,
                    CreatedAt = x.CreatedAt,
                    ModifiedAt = x.ModifiedAt,
                    ExecutedDate = x.ExecutedDate,
                    CompletedDate = x.CompletedDate
                }).ToList()
            };
            
            return Result<GetAllDeleteDataRequestsResponse>.Success(response);
        }
    }
}
```

## 3. Infrastructure Layer Implementation

### 3.1 Entity Configurations

#### DeleteDataRequestConfiguration.cs

```csharp
using KvFnB.Core.Infrastructure.EntityConfigurations;
using KvFnB.Modules.DataManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Modules.DataManagement.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class DeleteDataRequestConfiguration : BaseEntityTypeConfiguration<DeleteDataRequest, long>
    {
        public override void Configure(EntityTypeBuilder<DeleteDataRequest> builder)
        {
            base.Configure(builder);
            
            builder.ToTable("DeleteDataRequest");
            
            // Configure properties
            builder.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(200);
                
            builder.Property(e => e.Status)
                .IsRequired();
                
            builder.Property(e => e.ScheduleType)
                .IsRequired();
                
            builder.Property(e => e.Type)
                .IsRequired();
                
            builder.Property(e => e.BranchIds)
                .HasMaxLength(4000);
                
            builder.Property(e => e.ScheduleConfig)
                .HasMaxLength(4000);
                
            // Configure auditable properties
            builder.Property(e => e.CreatedBy)
                .IsRequired();
                
            builder.Property(e => e.CreatedAt)
                .IsRequired();
                
            builder.Property(e => e.ModifiedBy)
                .IsRequired(false);
                
            builder.Property(e => e.ModifiedAt)
                .IsRequired(false);
                
            // Configure relationships
            builder.HasMany(e => e.DeleteDataDetails)
                .WithOne(e => e.DeleteDataRequest)
                .HasForeignKey(e => e.RequestId)
                .OnDelete(DeleteBehavior.Cascade);
                
            // Configure indexes
            builder.HasIndex(e => e.TenantId);
            builder.HasIndex(e => e.Status);
            builder.HasIndex(e => new { e.TenantId, e.Status });
        }
    }
}
```

#### DeleteDataHistoryConfiguration.cs

```csharp
using KvFnB.Core.Infrastructure.EntityConfigurations;
using KvFnB.Modules.DataManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Modules.DataManagement.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class DeleteDataHistoryConfiguration : BaseEntityTypeConfiguration<DeleteDataHistory, long>
    {
        public override void Configure(EntityTypeBuilder<DeleteDataHistory> builder)
        {
            base.Configure(builder);
            
            builder.ToTable("DeleteDataHistory");
            
            // Configure properties
            builder.Property(e => e.TableName)
                .IsRequired()
                .HasMaxLength(100);
                
            builder.Property(e => e.Summary)
                .IsRequired();
                
            builder.Property(e => e.ExecutedById)
                .IsRequired();
                
            builder.Property(e => e.ExecutedDate)
                .IsRequired();
                
            // Configure indexes
            builder.HasIndex(e => e.TenantId);
            builder.HasIndex(e => e.RequestId);
            builder.HasIndex(e => new { e.TenantId, e.RequestId });
        }
    }
}
```

#### DeleteDataDetailConfiguration.cs

```csharp
using KvFnB.Core.Infrastructure.EntityConfigurations;
using KvFnB.Modules.DataManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Modules.DataManagement.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class DeleteDataDetailConfiguration : BaseEntityTypeConfiguration<DeleteDataDetail, long>
    {
        public override void Configure(EntityTypeBuilder<DeleteDataDetail> builder)
        {
            base.Configure(builder);
            
            builder.ToTable("DeleteDataDetail");
            
            // Configure properties
            builder.Property(e => e.TableName)
                .IsRequired()
                .HasMaxLength(100);
                
            builder.Property(e => e.FilterCondition)
                .IsRequired();
                
            builder.Property(e => e.SortOrder)
                .IsRequired();
                
            // Configure relationships
            builder.HasOne(e => e.DeleteDataRequest)
                .WithMany(e => e.DeleteDataDetails)
                .HasForeignKey(e => e.RequestId)
                .OnDelete(DeleteBehavior.Cascade);
                
            // Configure indexes
            builder.HasIndex(e => e.TenantId);
            builder.HasIndex(e => e.RequestId);
        }
    }
}
```

### 3.2 Repositories

#### DeleteDataRequestRepository.cs

```csharp
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Infrastructure.Repositories;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.DataManagement.Infrastructure.Repositories
{
    public class DeleteDataRequestRepository : RepositoryBase<DeleteDataRequest, long>, IDeleteDataRequestRepository
    {
        public DeleteDataRequestRepository(DbContext dbContext) : base(dbContext)
        {
        }

        public async Task<DeleteDataRequest> GetByIdAsync(long id, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataRequest>()
                .Include(x => x.DeleteDataHistories)
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
        }

        public async Task<IEnumerable<DeleteDataRequest>> GetAllByTenantIdAsync(int tenantId, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataRequest>()
                .Where(x => x.TenantId == tenantId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<DeleteDataRequest>> GetAllByStatusAsync(int tenantId, DeleteDataRequestStatus status, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataRequest>()
                .Where(x => x.TenantId == tenantId && x.Status == status)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<DeleteDataRequest>> GetScheduledRequestsDueForExecutionAsync(CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataRequest>()
                .Where(x => x.Status == DeleteDataRequestStatus.Approved && 
                            x.ScheduleType == DeleteDataScheduleType.Scheduled)
                .ToListAsync(cancellationToken);
        }

        public async Task<bool> ExistsWithEmailAndPendingStatusAsync(int tenantId, string email, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataRequest>()
                .AnyAsync(x => x.TenantId == tenantId && 
                               x.Email == email && 
                               x.Status == DeleteDataRequestStatus.Pending, 
                          cancellationToken);
        }
    }
}
```

#### DeleteDataHistoryRepository.cs

```csharp
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Infrastructure.Repositories;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.DataManagement.Infrastructure.Repositories
{
    public class DeleteDataHistoryRepository : RepositoryBase<DeleteDataHistory, long>, IDeleteDataHistoryRepository
    {
        public DeleteDataHistoryRepository(DbContext dbContext) : base(dbContext)
        {
        }

        public async Task<IEnumerable<DeleteDataHistory>> GetAllByRequestIdAsync(long requestId, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataHistory>()
                .Where(x => x.RequestId == requestId)
                .OrderByDescending(x => x.ExecutedDate)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<DeleteDataHistory>> GetAllByTenantIdAsync(int tenantId, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<DeleteDataHistory>()
                .Where(x => x.TenantId == tenantId)
                .OrderByDescending(x => x.ExecutedDate)
                .ToListAsync(cancellationToken);
        }
    }
}
```

## 4. Presentation Layer Implementation

### 4.1 Controllers

#### DeleteDataController.cs

```csharp
using System;
using System.Threading.Tasks;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.VerifyUserForDataDeletion;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetAllDeleteDataRequests;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetAllDeleteDataHistory;
using KvFnB.Shared.Constants;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;

namespace KvFnB.Modules.DataManagement.Presentation.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DeleteDataController : BaseApi
    {
        private readonly ILogger _logger;
        private readonly VerifyUserForDataDeletionUseCase _verifyUserForDataDeletionUseCase;
        private readonly CreateDeleteDataRequestUseCase _createDeleteDataRequestUseCase;
        private readonly UpdateDeleteDataRequestUseCase _updateDeleteDataRequestUseCase;
        private readonly GetAllDeleteDataRequestsUseCase _getAllDeleteDataRequestsUseCase;
        private readonly GetAllDeleteDataHistoryUseCase _getAllDeleteDataHistoryUseCase;

        public DeleteDataController(
            ILogger logger,
            VerifyUserForDataDeletionUseCase verifyUserForDataDeletionUseCase,
            CreateDeleteDataRequestUseCase createDeleteDataRequestUseCase,
            UpdateDeleteDataRequestUseCase updateDeleteDataRequestUseCase,
            GetAllDeleteDataRequestsUseCase getAllDeleteDataRequestsUseCase,
            GetAllDeleteDataHistoryUseCase getAllDeleteDataHistoryUseCase,
            IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _verifyUserForDataDeletionUseCase = verifyUserForDataDeletionUseCase ?? throw new ArgumentNullException(nameof(verifyUserForDataDeletionUseCase));
            _createDeleteDataRequestUseCase = createDeleteDataRequestUseCase ?? throw new ArgumentNullException(nameof(createDeleteDataRequestUseCase));
            _updateDeleteDataRequestUseCase = updateDeleteDataRequestUseCase ?? throw new ArgumentNullException(nameof(updateDeleteDataRequestUseCase));
            _getAllDeleteDataRequestsUseCase = getAllDeleteDataRequestsUseCase ?? throw new ArgumentNullException(nameof(getAllDeleteDataRequestsUseCase));
            _getAllDeleteDataHistoryUseCase = getAllDeleteDataHistoryUseCase ?? throw new ArgumentNullException(nameof(getAllDeleteDataHistoryUseCase));
        }

        /// <summary>
        /// Verifies user credentials and phone number before sending OTP for data deletion.
        /// </summary>
        /// <param name="request">The request containing user password and phone number.</param>
        /// <returns>The verification result with OTP status if successful.</returns>
        /// <response code="200">Returns the verification result with OTP status.</response>
        /// <response code="400">If the request is invalid or verification fails.</response>
        [HttpPost("verify-user")]
        [SwaggerOperation(Summary = "Verifies user credentials for data deletion", Description = "Verifies user password against database and phone number with Kfin service before sending OTP.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the verification result with OTP status", typeof(VerifyUserForDataDeletionResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or verification fails")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Create)]
        public async Task<IActionResult> VerifyUserForDataDeletion([FromBody] VerifyUserForDataDeletionRequest request)
        {
            try
            {
                var result = await _verifyUserForDataDeletionUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying user for data deletion");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Creates a new data deletion request or updates an existing one based on verification.
        /// </summary>
        /// <param name="request">The request containing the OTP and data for the new or updated request.</param>
        /// <returns>The result of the data deletion request operation.</returns>
        /// <response code="200">Returns the result of the data deletion request operation.</response>
        /// <response code="400">If the request is invalid or OTP verification fails.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new data deletion request or updates an existing one based on verification", Description = "Creates a new data deletion request or updates an existing one based on verification.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the result of the data deletion request operation", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or OTP verification fails")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Create)]
        public async Task<IActionResult> CreateOrUpdateDeleteDataRequest([FromBody] CreateDeleteDataRequestRequest request)
        {
            try
            {
                var result = await _createDeleteDataRequestUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating or updating data deletion request");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Updates a data deletion request after OTP verification.
        /// </summary>
        /// <param name="request">The request containing the ID, OTP, and status update.</param>
        /// <returns>The updated data deletion request.</returns>
        /// <response code="200">Returns the updated data deletion request.</response>
        /// <response code="400">If the request is invalid or OTP verification fails.</response>
        [HttpPut]
        [SwaggerOperation(Summary = "Updates a data deletion request", Description = "Updates a data deletion request after OTP verification.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated data deletion request", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or OTP verification fails")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Update)]
        public async Task<IActionResult> UpdateDeleteDataRequest([FromBody] UpdateDeleteDataRequestRequest request)
        {
            try
            {
                var result = await _updateDeleteDataRequestUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating data deletion request");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Gets all data deletion requests.
        /// </summary>
        /// <param name="request">The request containing optional filters.</param>
        /// <returns>A list of data deletion requests.</returns>
        /// <response code="200">Returns the list of data deletion requests.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Gets all data deletion requests", Description = "Gets all data deletion requests with optional filtering.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of data deletion requests", typeof(List<GetAllDeleteDataRequestsResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Read)]
        public async Task<IActionResult> GetAllDeleteDataRequests([FromQuery] GetAllDeleteDataRequestsRequest request)
        {
            try
            {
                var result = await _getAllDeleteDataRequestsUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data deletion requests");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Gets all data deletion history records.
        /// </summary>
        /// <param name="request">The request containing optional filters.</param>
        /// <returns>A list of data deletion history records.</returns>
        /// <response code="200">Returns the list of data deletion history records.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("history")]
        [SwaggerOperation(Summary = "Gets all data deletion history records", Description = "Gets all data deletion history records with optional filtering.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of data deletion history records", typeof(List<GetAllDeleteDataHistoryResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Read)]
        public async Task<IActionResult> GetAllDeleteDataHistory([FromQuery] GetAllDeleteDataHistoryRequest request)
        {
            try
            {
                var result = await _getAllDeleteDataHistoryUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data deletion history");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }

        /// <summary>
        /// Deletes a data deletion request.
        /// </summary>
        /// <param name="id">The ID of the request to delete.</param>
        /// <returns>The deleted data deletion request details.</returns>
        /// <response code="200">Returns the deleted data deletion request.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="404">If the request is not found.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes a data deletion request", Description = "Deletes a data deletion request. Only pending requests can be deleted.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the deleted data deletion request", typeof(DeleteDataRequestResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        [SwaggerResponse(StatusCodes.Status404NotFound, "If the request is not found")]
        [RequireAnyPermissions(PermissionConsts.DataManagement_Delete)]
        public async Task<IActionResult> DeleteDeleteDataRequest(long id)
        {
            try
            {
                var request = new DeleteDeleteDataRequestRequest { Id = id };
                var result = await _deleteDeleteDataRequestUseCase.ExecuteAsync(request);
                
                if (!result.IsSuccess)
                {
                    if (result.Error.Contains("not found", StringComparison.OrdinalIgnoreCase))
                    {
                        return NotFound(result.Error);
                    }
                    
                    return Failure(result);
                }
                
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting data deletion request");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred");
            }
        }
    }
}
```

## 5. Dependency Injection

### 5.1 Module Registration

```csharp
using System.Reflection;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Application.Services;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.VerifyUserForDataDeletion;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetAllDeleteDataRequests;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetAllDeleteDataHistory;
using KvFnB.Modules.DataManagement.Infrastructure.Repositories;
using KvFnB.Modules.DataManagement.Persistence.ShardingDb.EntityTypeConfigurations;
using KvFnB.Shared.Persistence;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static IServiceCollection RegisterDataManagementModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register configurations
            services.Configure<DataManagementOptions>(configuration.GetSection("DataManagement"));
            
            // Register automapper profiles
            services.AddAutoMapper(Assembly.GetExecutingAssembly());
            
            // Register services
            services.AddScoped<EmailVerificationService>();
            services.AddScoped<DataDeletionProcessingService>();
            
            // Register validators
            services.AddScoped<IValidator<VerifyUserForDataDeletionRequest>, VerifyUserForDataDeletionValidator>();
            services.AddScoped<IValidator<CreateDeleteDataRequestRequest>, CreateDeleteDataRequestValidator>();
            services.AddScoped<IValidator<UpdateDeleteDataRequestRequest>, UpdateDeleteDataRequestValidator>();
            
            // Register repositories
            services.AddScoped<IDeleteDataRequestRepository, DeleteDataRequestRepository>();
            services.AddScoped<IDeleteDataHistoryRepository, DeleteDataHistoryRepository>();
            
            // Register use cases
            services.AddScoped<VerifyUserForDataDeletionUseCase>();
            services.AddScoped<CreateDeleteDataRequestUseCase>();
            services.AddScoped<UpdateDeleteDataRequestUseCase>();
            services.AddScoped<GetAllDeleteDataRequestsUseCase>();
            services.AddScoped<GetAllDeleteDataHistoryUseCase>();
            
            // Register entity configurations
            services.AddEFConfigurations<DeleteDataRequestConfiguration>();
            services.AddEFConfigurations<DeleteDataHistoryConfiguration>();
            services.AddEFConfigurations<DeleteDataDetailConfiguration>();
            
            return services;
        }
    }
    
    public class DataManagementOptions
    {
        public string OtpSecretKey { get; set; }
        public int OtpExpirationMinutes { get; set; } = 5;
        public int DefaultBatchSize { get; set; } = 1000;
        public int DefaultTimeoutSeconds { get; set; } = 300;
    }
}
```

## Revised API Flow for Data Deletion

The API flow for data deletion has been updated as follows:

1. **Verify User (VerifyUserForDataDeletion API)**
   - Client submits the user's password, phone number, and fingerprint key for verification
   - Server performs the following steps:
     1. Verifies password against the User database
     2. Verifies phone number with KMA service
     3. If both verifications succeed:
        - Generates OTP using `OtpConfiguration` settings
        - Sends OTP to the verified phone number via SMS
        - Returns verification result with OTP status
   - Response includes:
     - `Success`: Overall verification status
     - `OtpSent`: Whether OTP was successfully sent
     - `MaskedPhone`: Masked phone number for privacy
     - `ExpirationMinutes`: OTP validity period

2. **Create or Update Request (Based on verification)**
   - **Create New Request (POST /api/DeleteData):**
     - Client submits the OTP along with all required data for the new request
     - Server verifies OTP using `OtpService.VerifyOtpAsync`
     - If OTP is valid, creates a new DeleteDataRequest
     - Returns created request details
   
   - **Update Existing Request (PUT /api/DeleteData):**
     - Client submits the OTP along with RequestId and all fields to update
     - Server verifies OTP using `OtpService.VerifyOtpAsync`
     - If OTP is valid, updates the specified DeleteDataRequest
     - Returns updated request details

### OTP Configuration and Security

The OTP system uses the following configuration and security measures:

1. **OTP Configuration (`OtpConfiguration`):**
   - `OtpSecret`: Secret key for OTP generation
   - `OtpIssuer`: Issuer identifier for OTP
   - `MaximumSmsRequestFromRetailerPerDay`: Daily SMS limit per retailer
   - `SmsCountKeyPrefix`: Redis key prefix for SMS counting

2. **Security Measures:**
   - Rate limiting for SMS requests
   - Device fingerprinting for additional security
   - Tenant isolation for OTP operations
   - Redis-based OTP storage and validation
   - Configurable OTP expiration time

3. **OTP Generation and Validation:**
   - Uses `IKvOTPGenerator` for secure OTP generation
   - Uses `IKvOTPSender` for SMS delivery
   - Implements SMS request limits per retailer
   - Validates device fingerprint for each request

This flow enhances security by requiring multiple verification steps (password, phone number, and OTP) before allowing data deletion operations.

The diagram below illustrates the flow:

```
Client                                 Server
  |                                      |
  |---- 1. VerifyUserForDataDeletion --->|
  |     (Password + Phone + Fingerprint) |
  |                                      | - Verify password against User DB
  |                                      | - Verify phone with KMA service
  |                                      | - Generate & send OTP via SMS
  |<------- Verification Result ---------|
  |     (Success + OTP Status)           |
  |                                      |
  |--- 2. Create/Update Request -------->|
  |     (OTP + Request Data)             |
  |                                      | - Verify OTP
  |                                      | - Create/Update request
  |<--------- Request Result ------------|
  |     (Request Details)                |
```

## Conclusion

This implementation guide provides a comprehensive approach to developing the Data Management feature following domain-driven design principles and clean architecture. The code structure follows the established patterns in the codebase, ensuring consistent implementation and maintainability.

Follow this guide to implement each layer systematically, starting from the domain model and working outward to the presentation layer. The OTP verification process adds an additional security layer to ensure only authorized users can initiate data deletion operations.

The revised API flow for data deletion has been updated to include form validation, user verification with password and phone number, OTP verification, and status updates:

1. **Validate Form (ValidateDeleteDataRequest API)**
   - Client submits form data for validation
   - If ID is 0 or null, a new pending request is created in the database
   - Returns validation result with request ID

2. **Verify User (VerifyUserForDataDeletion API)**
   - Client submits the user's password and phone number for verification
   - Server verifies password against the User database
   - Server verifies phone number with Kfin service
   - If both verifications succeed, server automatically sends OTP to the associated email
   - Returns verification result with OTP status and masked email

3. **Update Request with OTP Verification (UpdateDeleteDataRequest API)**
   - Client submits OTP along with requested status change (Approve/Cancel)
   - Server verifies OTP and updates the request status
   - Returns updated request details

This flow enhances security by requiring multiple verification steps (password, phone number, and OTP) before making changes to the request status.

The diagram below illustrates the flow:

```
Client                                 Server
  |                                      |
  |--- 1. ValidateDeleteDataRequest ---->|
  |                                      | - Validate form data
  |                                      | - Create pending request if new
  |<------ Validation Result -----------|
  |                                      |
  |---- 2. VerifyUserForDataDeletion --->|
  |                                      | - Verify password against User DB
  |                                      | - Verify phone number with Kfin service
  |                                      | - Send OTP to email if verified
  |<------- Verification Result ---------|
  |                                      |
  |--- 3. UpdateDeleteDataRequest ------>|
  |                                      | - Verify OTP
  |                                      | - Update request status
  |<--------- Updated Request -----------|
