namespace KvFnB.Core.Abstractions
{
    public interface IS3Service
    {
        Task<S3UploadResult> UploadFileAsync(Stream fileStream, string key, string contentType, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
        
        Task<S3UploadResult> UploadFileAsync(string filePath, string key, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
        
        Task<bool> DeleteFileAsync(string key, CancellationToken cancellationToken = default);
        
        Task<Stream> DownloadFileAsync(string key, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Extracts the object key from a CloudFront URL
        /// </summary>
        /// <param name="objectUrl">The full URL of the object, including the CloudFront domain</param>
        /// <param name="cloudfrontUrl">The base CloudFront URL</param>
        /// <returns>The object key or empty string if the URL doesn't start with the CloudFront URL</returns>
        string GetObjectKey(string objectUrl, string cloudfrontUrl);
        
        /// <summary>
        /// Extracts the object key from a CloudFront URL using the configured CloudFront URL
        /// </summary>
        /// <param name="objectUrl">The full URL of the object, including the CloudFront domain</param>
        /// <returns>The object key or empty string if the URL doesn't match the configured CloudFront URL</returns>
        string GetObjectKey(string objectUrl);        
    }

    public class S3UploadResult
    {
        public bool Success { get; set; }
        public string? Key { get; set; }
        public string? BucketName { get; set; }
        public string? ETag { get; set; }
        public string? VersionId { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public string? PublicUrl { get; set; }
    }
} 