# Technical Implementation Guide: DataDeletionSchedulerBackgroundService

This document provides step-by-step instructions for implementing the DataDeletionSchedulerBackgroundService for the Data Deletion feature, following the hexagonal architecture and clean code principles defined in the project.

## 1. Overview

The DataDeletionSchedulerBackgroundService is responsible for:
- Scanning scheduled data deletion requests at 1 AM daily
- Calculating next execution times
- Dispatching deletion jobs to the processing service
- Managing distributed locking to ensure single-instance execution using the Shared project's RedisDistributedLockProvider

## 2. Implementation Steps

### 2.1. Create Domain Models and Interfaces

1. Create domain entities in `src/Modules/DataManagement/Domain/Entities/`:

```csharp
// DeleteDataScheduleConfig.cs
namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    public class DeleteDataScheduleConfig
    {
        public string ScheduleType { get; set; } // "daily", "weekly", "monthly", "quarterly", "yearly"
        public int? DayOfWeek { get; set; } // 0-6 (for weekly)
        public int? DayOfMonth { get; set; } // 1-31 (for monthly, quarterly, yearly)
        public int? Month { get; set; } // 1-12 (for yearly)
        public int? QuarterMonth { get; set; } // 1-3 (for quarterly)
        public string ExecutionTime { get; set; } // "HH:MM:SS"
        public int RetentionMonths { get; set; } // Number of months to retain data
        public bool Active { get; set; }
        public DateTime? NextExecutionDate { get; set; }
    }
}
```

2. Create repository interfaces in `src/Modules/DataManagement/Domain/Repositories/`:

```csharp
// IDeleteDataRequestRepository.cs
namespace KvFnB.Modules.DataManagement.Domain.Repositories
{
    public interface IDeleteDataRequestRepository
    {
        Task<IEnumerable<DeleteDataRequest>> GetScheduledRequestsDueForExecutionAsync(DateTime currentTime, CancellationToken cancellationToken);
        Task UpdateNextExecutionDateAsync(long requestId, DateTime nextExecutionDate, CancellationToken cancellationToken);
        Task<DeleteDataRequest> GetByIdAsync(long requestId, CancellationToken cancellationToken);
    }
}

// IDeleteDataJobRepository.cs
namespace KvFnB.Modules.DataManagement.Domain.Repositories
{
    public interface IDeleteDataJobRepository
    {
        Task<DeleteDataJob> CreateJobAsync(DeleteDataJob job, CancellationToken cancellationToken);
    }
}
```

3. Create service interfaces in `src/Modules/DataManagement/Domain/Services/`:

```csharp
// IMessageQueueService.cs
namespace KvFnB.Modules.DataManagement.Domain.Services
{
    public interface IMessageQueueService
    {
        Task EnqueueJobAsync(DeleteDataJob job, CancellationToken cancellationToken);
    }
}
```

### 2.2. Create Application Use Cases

1. Create the schedule scanning use case in `src/Modules/DataManagement/Application/UseCases/DeleteDataScheduleUseCase/`:

```csharp
// ScanScheduledRequestsRequest.cs
namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataScheduleUseCase
{
    public record ScanScheduledRequestsRequest
    {
        public DateTime CurrentTime { get; init; }
    }
}

// ScanScheduledRequestsResponse.cs
namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataScheduleUseCase
{
    public record ScanScheduledRequestsResponse
    {
        public int ProcessedCount { get; init; }
        public List<string> Errors { get; init; } = new();
    }
}

// ScanScheduledRequestsValidator.cs
namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataScheduleUseCase
{
    public class ScanScheduledRequestsValidator : Validator<ScanScheduledRequestsRequest>
    {
        public ScanScheduledRequestsValidator()
        {
            // Validation rules
        }
    }
}

// ScanScheduledRequestsUseCase.cs
namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataScheduleUseCase
{
    public class ScanScheduledRequestsUseCase : UseCaseBase<ScanScheduledRequestsRequest, ScanScheduledRequestsResponse>
    {
        private readonly IDeleteDataRequestRepository _requestRepository;
        private readonly IDeleteDataJobRepository _jobRepository;
        private readonly IMessageQueueService _messageQueueService;
        private readonly ILogger<ScanScheduledRequestsUseCase> _logger;

        public ScanScheduledRequestsUseCase(
            IValidator<ScanScheduledRequestsRequest> validator,
            IDeleteDataRequestRepository requestRepository,
            IDeleteDataJobRepository jobRepository,
            IMessageQueueService messageQueueService,
            ILogger<ScanScheduledRequestsUseCase> logger)
            : base(validator)
        {
            _requestRepository = requestRepository ?? throw new ArgumentNullException(nameof(requestRepository));
            _jobRepository = jobRepository ?? throw new ArgumentNullException(nameof(jobRepository));
            _messageQueueService = messageQueueService ?? throw new ArgumentNullException(nameof(messageQueueService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public override async Task<Result<ScanScheduledRequestsResponse>> ExecuteAsync(
            ScanScheduledRequestsRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Scanning scheduled data deletion requests at {CurrentTime}", request.CurrentTime);
                
                // Get scheduled requests due for execution
                var dueRequests = await _requestRepository.GetScheduledRequestsDueForExecutionAsync(
                    request.CurrentTime, 
                    cancellationToken);
                
                int processedCount = 0;
                var errors = new List<string>();
                
                foreach (var deleteRequest in dueRequests)
                {
                    try
                    {
                        // Create a new job
                        var job = new DeleteDataJob
                        {
                            RequestId = deleteRequest.Id,
                            Status = "Pending",
                            CreatedAt = DateTime.UtcNow,
                            RetailerId = deleteRequest.RetailerId
                        };
                        
                        // Save the job
                        var createdJob = await _jobRepository.CreateJobAsync(job, cancellationToken);
                        
                        // Enqueue the job for processing
                        await _messageQueueService.EnqueueJobAsync(createdJob, cancellationToken);
                        
                        // Calculate next execution date
                        var nextExecutionDate = CalculateNextExecutionDate(deleteRequest.ScheduleConfig, request.CurrentTime);
                        
                        // Update the request with the new execution date
                        await _requestRepository.UpdateNextExecutionDateAsync(
                            deleteRequest.Id, 
                            nextExecutionDate, 
                            cancellationToken);
                        
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing scheduled request {RequestId}", deleteRequest.Id);
                        errors.Add($"Failed to process request {deleteRequest.Id}: {ex.Message}");
                    }
                }
                
                return Result<ScanScheduledRequestsResponse>.Success(new ScanScheduledRequestsResponse
                {
                    ProcessedCount = processedCount,
                    Errors = errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scanning scheduled requests");
                return Result<ScanScheduledRequestsResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
        
        private DateTime CalculateNextExecutionDate(DeleteDataScheduleConfig config, DateTime currentTime)
        {
            switch (config.ScheduleType)
            {
                case "daily":
                    return currentTime.Date.Add(TimeSpan.Parse(config.ExecutionTime)).AddDays(1);
                
                case "weekly":
                    int daysUntilNextDay = ((int)config.DayOfWeek - (int)currentTime.DayOfWeek + 7) % 7;
                    if (daysUntilNextDay == 0 && currentTime.TimeOfDay > TimeSpan.Parse(config.ExecutionTime))
                        daysUntilNextDay = 7;
                    return currentTime.Date.Add(TimeSpan.Parse(config.ExecutionTime)).AddDays(daysUntilNextDay);
                
                case "monthly":
                    var nextMonthDate = new DateTime(currentTime.Year, currentTime.Month, 1).AddMonths(1);
                    int targetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(nextMonthDate.Year, nextMonthDate.Month));
                    return new DateTime(nextMonthDate.Year, nextMonthDate.Month, targetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                
                case "quarterly":
                    var nextQuarterDate = currentTime.Date;
                    int currentQuarter = (currentTime.Month - 1) / 3 + 1;
                    int monthsToAdd = (currentQuarter * 3) - currentTime.Month + config.QuarterMonth.Value;
                    if (monthsToAdd <= 0 || (monthsToAdd == 0 && currentTime.Day > config.DayOfMonth.Value))
                        monthsToAdd += 3;
                    nextQuarterDate = nextQuarterDate.AddMonths(monthsToAdd);
                    int quarterTargetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(nextQuarterDate.Year, nextQuarterDate.Month));
                    return new DateTime(nextQuarterDate.Year, nextQuarterDate.Month, quarterTargetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                
                case "yearly":
                    int yearToUse = currentTime.Year;
                    if (currentTime.Month > config.Month.Value || 
                        (currentTime.Month == config.Month.Value && currentTime.Day > config.DayOfMonth.Value))
                    {
                        yearToUse++;
                    }
                    int yearlyTargetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(yearToUse, config.Month.Value));
                    return new DateTime(yearToUse, config.Month.Value, yearlyTargetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                
                default:
                    throw new ArgumentException($"Unsupported schedule type: {config.ScheduleType}");
            }
        }
    }
}
```

### 2.3. Create Infrastructure Implementations

1. Create repository implementations in `src/Modules/DataManagement/Infrastructure/Repositories/`:

```csharp
// DeleteDataRequestRepository.cs
namespace KvFnB.Modules.DataManagement.Infrastructure.Repositories
{
    public class DeleteDataRequestRepository : IDeleteDataRequestRepository
    {
        private readonly IQueryService _queryService;
        
        public DeleteDataRequestRepository(IQueryService queryService)
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        }
        
        public async Task<IEnumerable<DeleteDataRequest>> GetScheduledRequestsDueForExecutionAsync(
            DateTime currentTime, 
            CancellationToken cancellationToken)
        {
            var query = new QueryBuilder()
                .SelectFrom("DeleteDataRequest r")
                .Where("r.Status = @Status", new { Status = "Approved" })
                .Where("r.Type = @Type", new { Type = "Scheduled" })
                .Where("JSON_VALUE(r.ScheduleConfig, '$.active') = 'true'")
                .Where("CAST(JSON_VALUE(r.ScheduleConfig, '$.nextExecutionDate') AS DATETIME) <= @CurrentTime", 
                       new { CurrentTime = currentTime });
                       
            return await _queryService.QueryAsync<DeleteDataRequest>(query);
        }
        
        public async Task UpdateNextExecutionDateAsync(
            long requestId, 
            DateTime nextExecutionDate, 
            CancellationToken cancellationToken)
        {
            var request = await GetByIdAsync(requestId, cancellationToken);
            if (request == null)
                throw new NotFoundException($"DeleteDataRequest with ID {requestId} not found");
                
            var scheduleConfig = JsonSerializer.Deserialize<DeleteDataScheduleConfig>(request.ScheduleConfig);
            scheduleConfig.NextExecutionDate = nextExecutionDate;
            
            var updateQuery = new QueryBuilder()
                .Update("DeleteDataRequest")
                .Set("ScheduleConfig = @ScheduleConfig", new { ScheduleConfig = JsonSerializer.Serialize(scheduleConfig) })
                .Where("Id = @Id", new { Id = requestId });
                
            await _queryService.ExecuteAsync(updateQuery);
        }
        
        public async Task<DeleteDataRequest> GetByIdAsync(long requestId, CancellationToken cancellationToken)
        {
            var query = new QueryBuilder()
                .SelectFrom("DeleteDataRequest r")
                .Where("r.Id = @Id", new { Id = requestId });
                
            return await _queryService.QueryFirstOrDefaultAsync<DeleteDataRequest>(query);
        }
    }
}

// DeleteDataJobRepository.cs
namespace KvFnB.Modules.DataManagement.Infrastructure.Repositories
{
    public class DeleteDataJobRepository : IDeleteDataJobRepository
    {
        private readonly IQueryService _queryService;
        
        public DeleteDataJobRepository(IQueryService queryService)
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        }
        
        public async Task<DeleteDataJob> CreateJobAsync(DeleteDataJob job, CancellationToken cancellationToken)
        {
            var insertQuery = new QueryBuilder()
                .InsertInto("DeleteDataJob", new
                {
                    RequestId = job.RequestId,
                    Status = job.Status,
                    CreatedAt = job.CreatedAt,
                    RetailerId = job.RetailerId
                })
                .Output("INSERTED.*");
                
            return await _queryService.QueryFirstOrDefaultAsync<DeleteDataJob>(insertQuery);
        }
    }
}
```

2. Create service implementations in `src/Modules/DataManagement/Infrastructure/Services/`:

```csharp
// KafkaMessageQueueService.cs
namespace KvFnB.Modules.DataManagement.Infrastructure.Services
{
    public class KafkaMessageQueueService : IMessageQueueService
    {
        private readonly IProducer<string, string> _producer;
        private readonly ILogger<KafkaMessageQueueService> _logger;
        private readonly string _topic;
        
        public KafkaMessageQueueService(
            IProducer<string, string> producer,
            IConfiguration configuration,
            ILogger<KafkaMessageQueueService> logger)
        {
            _producer = producer ?? throw new ArgumentNullException(nameof(producer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _topic = configuration["Kafka:Topics:DataDeletionJobs"] ?? "data-deletion-jobs";
        }
        
        public async Task EnqueueJobAsync(DeleteDataJob job, CancellationToken cancellationToken)
        {
            try
            {
                var message = new Message<string, string>
                {
                    Key = job.Id.ToString(),
                    Value = JsonSerializer.Serialize(job)
                };
                
                var deliveryResult = await _producer.ProduceAsync(_topic, message, cancellationToken);
                
                _logger.LogInformation(
                    "Job {JobId} for request {RequestId} enqueued to topic {Topic} at offset {Offset}",
                    job.Id, job.RequestId, _topic, deliveryResult.Offset);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex, 
                    "Failed to enqueue job {JobId} for request {RequestId} to topic {Topic}",
                    job.Id, job.RequestId, _topic);
                throw;
            }
        }
    }
}
```

### 2.4. Create Background Service Project

1. Define configuration in `appsettings.json`:
```json
{
  "DataDeletionScheduler": {
    "ScheduledRunTime": "01:00:00"      // Daily run time (HH:mm:ss)
  },
  "DataDeletion": {
    "LockTimeoutMinutes": 5              // How long to hold the distributed lock
  },
  "Kafka": {
    "Topics": {
      "DataDeletionJobs": "data-deletion-jobs"
    }
  }
}
```

2. Create the background service project in `src/Presentation/BackgroundServices/`:

```csharp
// KvFnB.DataDeletionSchedulerBackgroundService.csproj using .NET 8
// DataDeletionSchedulerBackgroundService.cs in DataDeletionSchedulerBackgroundService project
namespace KvFnB.Presentation.BackgroundServices
{
    public class DataDeletionSchedulerBackgroundService : BackgroundService
    {
        private readonly ScanScheduledRequestsUseCase _scanUseCase;
        private readonly IDistributedLockProvider _lockProvider;
        private readonly ILogger<DataDeletionSchedulerBackgroundService> _logger;
        private readonly IConfiguration _configuration;
        private readonly TimeSpan _scheduledRunTime;
        private readonly TimeSpan _lockTimeout;

        public DataDeletionSchedulerBackgroundService(
            ScanScheduledRequestsUseCase scanUseCase,
            IDistributedLockProvider lockProvider,
            ILogger<DataDeletionSchedulerBackgroundService> logger,
            IConfiguration configuration)
        {
            _scanUseCase = scanUseCase ?? throw new ArgumentNullException(nameof(scanUseCase));
            _lockProvider = lockProvider ?? throw new ArgumentNullException(nameof(lockProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // Read scheduled run time from configuration (default to 1:00 AM)
            _scheduledRunTime = TimeSpan.Parse(configuration["DataDeletionScheduler:ScheduledRunTime"] ?? "01:00:00");
            // Read lock timeout from configuration (default to 5 minutes)
            var lockTimeoutMinutes = int.Parse(configuration["DataDeletion:LockTimeoutMinutes"] ?? "5");
            _lockTimeout = TimeSpan.FromMinutes(lockTimeoutMinutes);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("DataDeletionSchedulerBackgroundService is starting");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;
                    var nextRunTime = now.Date.Add(_scheduledRunTime);

                    // If it's already past the scheduled run time today, schedule for tomorrow
                    if (now.TimeOfDay >= _scheduledRunTime)
                    {
                        nextRunTime = nextRunTime.AddDays(1);
                    }

                    var delay = nextRunTime - now;
                    _logger.LogInformation("Next scheduled run at {ScheduledTime}, in {Delay}",
                        nextRunTime, delay);

                    // Wait until the next scheduled time
                    await Task.Delay(delay, stoppingToken);

                    if (stoppingToken.IsCancellationRequested)
                        break;

                    const string lockKey = "data-deletion-scheduler";
                    bool lockAcquired = await _lockProvider.AcquireLockAsync(
                        lockKey,
                        _lockTimeout,
                        stoppingToken);

                    if (lockAcquired)
                    {
                        try
                        {
                            _logger.LogInformation("Running scheduled data deletion scan at {ScheduledRunTime}", _scheduledRunTime);

                            var request = new ScanScheduledRequestsRequest
                            {
                                CurrentTime = DateTime.UtcNow
                            };

                            var result = await _scanUseCase.ExecuteAsync(request, stoppingToken);

                            if (result.IsSuccess)
                            {
                                _logger.LogInformation(
                                    "Scheduled data deletion scan completed: {ProcessedCount} requests processed",
                                    result.Value.ProcessedCount);

                                if (result.Value.Errors.Count > 0)
                                {
                                    _logger.LogWarning(
                                        "Scheduled data deletion scan had {ErrorCount} errors",
                                        result.Value.Errors.Count);
                                }
                            }
                            else
                            {
                                _logger.LogError(
                                    "Scheduled data deletion scan failed: {Errors}",
                                    string.Join(", ", result.Errors));
                            }
                        }
                        finally
                        {
                            // Always release the lock, even if processing fails
                            await _lockProvider.ReleaseLockAsync(lockKey);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Failed to acquire lock for scheduled data deletion scan");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing scheduled data deletion scan");
                }
            }

            _logger.LogInformation("DataDeletionSchedulerBackgroundService is stopping");
        }
    }
}
```

### 2.5. Configure Dependency Injection

1. Add registrations to `src/Modules/DataManagement/Infrastructure/DependencyInjection/ModuleRegistrar.cs`:

```csharp
// ModuleRegistrar.cs
namespace KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static IServiceCollection RegisterDataManagementModule(this IServiceCollection services)
        {
            // Register repositories
            services.AddScoped<IDeleteDataRequestRepository, DeleteDataRequestRepository>();
            services.AddScoped<IDeleteDataJobRepository, DeleteDataJobRepository>();
            
            // Register services
            services.AddSingleton<IMessageQueueService, KafkaMessageQueueService>();
            
            // Register use cases
            services.AddScoped<ScanScheduledRequestsUseCase>();
            services.AddScoped<IValidator<ScanScheduledRequestsRequest>, ScanScheduledRequestsValidator>();
            
            // Note: IDistributedLockProvider is already registered in the Shared Layer
            
            return services;
        }
    }
}
```

2. Register the background service in `src/Presentation/Program.cs`:

```csharp
// In Program.cs or a dedicated HostingServiceExtensions.cs file
services.AddHostedService<DataDeletionSchedulerBackgroundService>();
```

### 2.6. Create Unit Tests

1. Create tests for the ScanScheduledRequestsUseCase:

```csharp
// ScanScheduledRequestsUseCaseTests.cs
namespace KvFnB.Modules.DataManagement.Tests.Application.UseCases.DeleteDataScheduleUseCase.ScanScheduledRequests
{
    public class ScanScheduledRequestsUseCaseTests
    {
        private readonly Mock<IValidator<ScanScheduledRequestsRequest>> _validatorMock;
        private readonly Mock<IDeleteDataRequestRepository> _requestRepositoryMock;
        private readonly Mock<IDeleteDataJobRepository> _jobRepositoryMock;
        private readonly Mock<IMessageQueueService> _messageQueueServiceMock;
        private readonly Mock<ILogger<ScanScheduledRequestsUseCase>> _loggerMock;
        private readonly ScanScheduledRequestsUseCase _useCase;
        
        public ScanScheduledRequestsUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<ScanScheduledRequestsRequest>>();
            _requestRepositoryMock = new Mock<IDeleteDataRequestRepository>();
            _jobRepositoryMock = new Mock<IDeleteDataJobRepository>();
            _messageQueueServiceMock = new Mock<IMessageQueueService>();
            _loggerMock = new Mock<ILogger<ScanScheduledRequestsUseCase>>();
            
            _useCase = new ScanScheduledRequestsUseCase(
                _validatorMock.Object,
                _requestRepositoryMock.Object,
                _jobRepositoryMock.Object,
                _messageQueueServiceMock.Object,
                _loggerMock.Object);
        }
        
        [Fact]
        public async Task ExecuteAsync_WhenNoRequestsDue_ShouldReturnSuccessWithZeroProcessed()
        {
            // Arrange
            var request = new ScanScheduledRequestsRequest { CurrentTime = DateTime.UtcNow };
            _validatorMock.Setup(v => v.Validate(request)).Returns(ValidationResult.Success());
            _requestRepositoryMock
                .Setup(r => r.GetScheduledRequestsDueForExecutionAsync(request.CurrentTime, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest>());
            
            // Act
            var result = await _useCase.ExecuteAsync(request);
            
            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(0, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
        }
        
        [Fact]
        public async Task ExecuteAsync_WhenRequestsDue_ShouldProcessAndUpdateNextExecutionDate()
        {
            // Arrange
            var currentTime = DateTime.UtcNow;
            var request = new ScanScheduledRequestsRequest { CurrentTime = currentTime };
            _validatorMock.Setup(v => v.Validate(request)).Returns(ValidationResult.Success());
            
            var scheduleConfig = new DeleteDataScheduleConfig
            {
                ScheduleType = "daily",
                ExecutionTime = "02:00:00",
                RetentionMonths = 3,
                Active = true,
                NextExecutionDate = currentTime.AddDays(-1)
            };
            
            var deleteRequest = new DeleteDataRequest
            {
                Id = 1,
                RetailerId = 100,
                Status = "Approved",
                Type = "Scheduled",
                ScheduleConfig = JsonSerializer.Serialize(scheduleConfig)
            };
            
            _requestRepositoryMock
                .Setup(r => r.GetScheduledRequestsDueForExecutionAsync(currentTime, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });
                
            _jobRepositoryMock
                .Setup(r => r.CreateJobAsync(It.IsAny<DeleteDataJob>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((DeleteDataJob job, CancellationToken _) => { job.Id = 1; return job; });
            
            // Act
            var result = await _useCase.ExecuteAsync(request);
            
            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify job creation and queue
            _jobRepositoryMock.Verify(
                r => r.CreateJobAsync(It.Is<DeleteDataJob>(j => j.RequestId == 1), It.IsAny<CancellationToken>()),
                Times.Once);
                
            _messageQueueServiceMock.Verify(
                m => m.EnqueueJobAsync(It.Is<DeleteDataJob>(j => j.Id == 1), It.IsAny<CancellationToken>()),
                Times.Once);
                
            // Verify next execution date update
            _requestRepositoryMock.Verify(
                r => r.UpdateNextExecutionDateAsync(1, It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WhenRepositoryThrowsException_ShouldReturnFailure()
        {
            // Arrange
            var request = new ScanScheduledRequestsRequest { CurrentTime = DateTime.UtcNow };
            _validatorMock.Setup(v => v.Validate(request)).Returns(ValidationResult.Success());
            _requestRepositoryMock
                .Setup(r => r.GetScheduledRequestsDueForExecutionAsync(request.CurrentTime, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Database error"));
            
            // Act
            var result = await _useCase.ExecuteAsync(request);
            
            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains(ErrorMessages.InternalServerError, result.Errors);
        }
    }
}
```

2. Create tests for the background service:

```csharp
// DataDeletionSchedulerBackgroundServiceTests.cs
namespace KvFnB.Presentation.Tests.BackgroundServices
{
    public class DataDeletionSchedulerBackgroundServiceTests
    {
        private readonly Mock<ScanScheduledRequestsUseCase> _scanUseCaseMock;
        private readonly Mock<IDistributedLockProvider> _lockProviderMock;
        private readonly Mock<ILogger<DataDeletionSchedulerBackgroundService>> _loggerMock;
        private readonly Mock<IConfiguration> _configurationMock;
        
        public DataDeletionSchedulerBackgroundServiceTests()
        {
            _scanUseCaseMock = new Mock<ScanScheduledRequestsUseCase>();
            _lockProviderMock = new Mock<IDistributedLockProvider>();
            _loggerMock = new Mock<ILogger<DataDeletionSchedulerBackgroundService>>();
            _configurationMock = new Mock<IConfiguration>();
        }
        
        [Fact]
        public async Task ExecuteAsync_WhenLockAcquired_ShouldExecuteScanUseCase()
        {
            // Arrange
            _lockProviderMock
                .Setup(l => l.AcquireLockAsync("data-deletion-scheduler", It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
                
            _scanUseCaseMock
                .Setup(u => u.ExecuteAsync(It.IsAny<ScanScheduledRequestsRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<ScanScheduledRequestsResponse>.Success(
                    new ScanScheduledRequestsResponse { ProcessedCount = 5 }));
            
            var service = new DataDeletionSchedulerBackgroundService(
                _scanUseCaseMock.Object,
                _lockProviderMock.Object,
                _loggerMock.Object,
                _configurationMock.Object);
                
            // Act - Simulate service behavior without waiting for 1 AM
            await service.ExecuteTestAsync(); // You would need to add a test method for this
            
            // Assert
            _lockProviderMock.Verify(
                l => l.AcquireLockAsync("data-deletion-scheduler", It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
                Times.Once);
                
            _scanUseCaseMock.Verify(
                u => u.ExecuteAsync(It.IsAny<ScanScheduledRequestsRequest>(), It.IsAny<CancellationToken>()),
                Times.Once);
                
            _lockProviderMock.Verify(
                l => l.ReleaseLockAsync("data-deletion-scheduler"),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WhenLockNotAcquired_ShouldNotExecuteScanUseCase()
        {
            // Arrange
            _lockProviderMock
                .Setup(l => l.AcquireLockAsync("data-deletion-scheduler", It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(false);
                
            var service = new DataDeletionSchedulerBackgroundService(
                _scanUseCaseMock.Object,
                _lockProviderMock.Object,
                _loggerMock.Object,
                _configurationMock.Object);
                
            // Act - Simulate service behavior without waiting for 1 AM
            await service.ExecuteTestAsync(); // You would need to add a test method for this
            
            // Assert
            _lockProviderMock.Verify(
                l => l.AcquireLockAsync("data-deletion-scheduler", It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
                Times.Once);
                
            _scanUseCaseMock.Verify(
                u => u.ExecuteAsync(It.IsAny<ScanScheduledRequestsRequest>(), It.IsAny<CancellationToken>()),
                Times.Never);
                
            _lockProviderMock.Verify(
                l => l.ReleaseLockAsync("data-deletion-scheduler"),
                Times.Never);
        }
    }
}
```

## 3. Integration and Configuration

### 3.1. Configuration Settings

Add the following to `appsettings.json`:

```json
{
  "DataDeletion": {
    "LockTimeoutMinutes": 5   // How long to hold the distributed lock
  },
  "Kafka": {
    "Topics": {
      "DataDeletionJobs": "data-deletion-jobs"
    }
  }
}
```

### 3.2. Monitor and Logging

Implement logging throughout the service:

1. Log when the service starts and stops
2. Log when jobs are created and dispatched
3. Log errors with detailed information
4. Track metrics for job processing statistics

## 4. Using the Shared DistributedLockProvider

The DataDeletionSchedulerBackgroundService uses the RedisDistributedLockProvider from the Shared project for distributed locking. This component is already registered in the core dependency injection container.

### Key Benefits of Using the Shared Implementation
- Consistent distributed locking behavior across all modules
- Centralized implementation following DRY principles
- Simplified maintenance as lock implementation is in a single place
- Proven implementation with robust error handling

### Usage Example

```csharp
// Inject the IDistributedLockProvider from the Shared project
private readonly IDistributedLockProvider _lockProvider;

public SomeService(IDistributedLockProvider lockProvider)
{
    _lockProvider = lockProvider;
}

public async Task DoWorkWithLockAsync(CancellationToken cancellationToken)
{
    // Acquire lock
    string lockKey = "some-unique-lock-key";
    bool lockAcquired = await _lockProvider.AcquireLockAsync(
        lockKey, 
        TimeSpan.FromMinutes(5),
        cancellationToken);
    
    if (lockAcquired)
    {
        try
        {
            // Do work that requires exclusive access
        }
        finally
        {
            // Always release lock
            await _lockProvider.ReleaseLockAsync(lockKey);
        }
    }
}
```

## 5. Deployment Considerations

1. Configure the service to run as a hosted service in the KvFnB Core application
2. Ensure Redis is properly configured for distributed locking
3. Set up Kafka for message queuing
4. Configure proper retry policies with Polly
5. Setup monitoring and alerting for service health

## 6. Testing Approach

1. **Unit Tests**: Test individual components (use cases, services)
2. **Integration Tests**: Test database interactions and Redis operations
3. **System Tests**: Test end-to-end flow from schedule creation to job processing 
4. **Performance Tests**: Test with high volume of scheduled requests

## 7. Conclusion

The DataDeletionSchedulerBackgroundService implementation follows the clean architecture principles and provides a robust solution for handling scheduled data deletion requests. The service is designed to be highly available, resilient to failures, and easily maintainable.

Key features:
- Scheduled to run at 1 AM daily to minimize impact on system performance
- Uses RedisDistributedLockProvider from the Shared project for distributed locking
- Support for various schedule types (daily, weekly, monthly, quarterly, yearly)
- Error handling and retry mechanisms
- Comprehensive logging and monitoring