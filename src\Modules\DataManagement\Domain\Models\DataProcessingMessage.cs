using System.Text.Json.Serialization;

namespace KvFnB.Modules.DataManagement.Domain.Models
{
    public class DataProcessingMessage
    {
        public long Id { get; set; }
        public long RequestId { get; set; }
        public long RetailerId { get; set; }
        public long BranchId { get; set; }
        public long GroupId { get; set; }
        public string Type { get; set; } = string.Empty;

        public FilterCondition FilterCondition { get; set; } = new FilterCondition();
    }

    public class FilterCondition {
        [JsonPropertyName("ignore_e_invoice")]
        public bool IgnoreEInvoice { get; set; }
        [JsonPropertyName("from_date")]
        public DateTime? FromDate { get; set; }
        [JsonPropertyName("to_date")]
        public DateTime? ToDate { get; set; }

        public void UpdateToDate(DateTime? toDate)
        {
            ToDate = toDate;
        }
    }
}