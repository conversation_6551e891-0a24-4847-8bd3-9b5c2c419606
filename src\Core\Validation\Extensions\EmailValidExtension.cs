using KvFnB.Core.Validation.CommonRules;

namespace KvFnB.Core.Validation.Extensions
{
    public static class EmailValidExtension
    {
        public static RuleBuilder<T, string> EmailValid<T>(this RuleBuilder<T, string> ruleBuilder, string errorMessage)
        {
            ruleBuilder.Validator.AddRule(ruleBuilder.GroupId ,x => EmailRule.IsValidEmail(ruleBuilder.Expression.Compile()(x)), errorMessage);
            return ruleBuilder;
        }
    }
}