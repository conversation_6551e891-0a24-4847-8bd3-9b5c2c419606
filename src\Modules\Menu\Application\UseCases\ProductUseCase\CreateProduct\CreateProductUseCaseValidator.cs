using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct
{
    public class CreateProductUseCaseValidator : Validator<CreateProductRequest>
    {
        public CreateProductUseCaseValidator(ILocalizationProvider multiLang)
        {
            // Basic product information
            RuleFor(x => x.Code)
                .GreaterThanMaxLength(50, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_code_max_length_50));
                
            RuleFor(x => x.Name)
                .NotEmpty(multiLang.GetMessage(LocalizationKeys.man_product_validate_create_name_required))
                .GreaterThanMaxLength(255, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_name_max_length_255));
                
            RuleFor(x => x.CategoryId)
                .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_category_id__more_than_zero));
                
            RuleFor(x => x.Description)
                .Must(desc => desc == null || desc.Length <= 2000, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_description_max_length_2000));
                
            RuleFor(x => x.BasePrice)
                .GreaterThanOrEqual(0m, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_base_price_min));
                
            RuleFor(x => x.TaxRate)
                .Must(x => x >= 0 && x <= 100, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_tax_rate_range));
                
            RuleFor(x => x.Unit)
                .Must(unit => unit == null || unit.Length <= 50, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_unit_max_length));
                
            RuleFor(x => x.ConversionValue)
                .GreaterThan(0f, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_conversion_value_min));
                
            // Product branch information
            RuleFor(x => x.MinQuantity)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_min_quantity_min));
                
            RuleFor(x => x.MaxQuantity)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_max_quantity_min));
                
            // Custom validation for MaxQuantity vs MinQuantity
            RuleFor(x => x)
                .Must(x => x.MaxQuantity >= x.MinQuantity, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_max_quantity_gte_min_quantity));
                
            RuleFor(x => x.Cost)
                .GreaterThanOrEqual(0m, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_cost_min));
                
            RuleFor(x => x.OnHand)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_on_hand_quantity_min));
                
            // Collections
            RuleFor(x => x.PriceBooks)
                .Must(priceBooks => priceBooks == null || priceBooks.All(pb => pb.PriceBookId > 0), multiLang.GetMessage(LocalizationKeys.man_product_validate_create_pricebook_ids_more_than_zero))
                .Must(priceBooks => priceBooks == null || priceBooks.All(pb => pb.Price >= 0), multiLang.GetMessage(LocalizationKeys.man_product_validate_create_pricebook_prices_min));
                
            RuleFor(x => x.Images)
                .Must(images => images == null || images.All(img => !string.IsNullOrEmpty(img)), multiLang.GetMessage(LocalizationKeys.man_product_validate_create_image_urls_not_empty));
                
            RuleFor(x => x.ProductTypeId)
                .Must(id => id == null || id > 0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_product_type_id_positive_optional));
            
            RuleFor(x => x.ProductGroupId)
                .Must(id => id == null || id > 0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_product_group_id_positive_optional));
            
            RuleFor(x => x.ProductGroupId)
                .Must(id => id == null || ProductGroupTypes.List().Any(pg => pg.Id == id), multiLang.GetMessage(LocalizationKeys.man_product_validate_create_product_group_id_invalid));

            RuleFor(x => x.TaxId)
                .Must(id => id == null || id > 0, multiLang.GetMessage(LocalizationKeys.man_product_validate_create_tax_id_positive_optional));
        }
    }
}