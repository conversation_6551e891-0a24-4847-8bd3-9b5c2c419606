using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Domain;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Shared.DapperQuery;
using KvFnB.Shared.MultiTenancy;
using KvFnB.Modules.Menu.Application.Dtos.Response;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductDetail
{
    public class GetProductDetailUseCase : UseCaseBase<GetProductDetailRequest, GetProductDetailResponse>
    {
        private readonly IQueryService _queryService;
        private readonly ITenantProvider _tenantProvider;
        private readonly TenantInfo _tenantInfo;
        private readonly ILogger _logger;

        public GetProductDetailUseCase(
            IQueryService queryService, 
            IMapper mapper, 
            ITenantProvider tenantProvider,
            TenantInfo tenantInfo,
            ILogger logger
            )
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _tenantInfo = tenantInfo ?? throw new ArgumentNullException(nameof(tenantInfo));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public override async Task<Result<GetProductDetailResponse>> ExecuteAsync(GetProductDetailRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var qb = new QueryBuilder();
                qb.Select("p.Id",
                    "p.Name",
                    "p.Code",
                    "p.Description",
                    "p.BasePrice",
                    "p.FullName",
                    "p.MasterCode",
                    "p.AllowsSale",
                    "pb.Cost",
                    "p.CategoryId",
                    "p.IsTimeType",
                    "p.IsTopping",
                    "p.IsProcessedGoods",
                    "p.InventoryTrackingIgnore",
                    "p.IsTimeServices",
                    "p.IsActive",
                    "p.IsDeleted",
                    "p.ProductType",
                    "p.ProductGroup",
                    "p.Unit",
                    "p.IsRewardPoint",
                    "p.OrderTemplate",
                    "p.Tax as TaxRate",
                    "c.Id as Category_Id",
                    "c.Name as Category_Name",
                    "c.Description as Category_Description",
                    "c.IsActive as Category_IsActive",
                    "pb.OnHand",
                    "pb.MinQuantity",
                    "pb.MaxQuantity",
                    "t.Id as Tax_Id",
                    "t.Name as Tax_Name",
                    "t.TaxRate as Tax_TaxRate",
                    "t.TaxType as Tax_TaxType")
                  .From("Product p WITH(NOLOCK)")
                  .LeftJoin("Category c WITH(NOLOCK)", "c.Id = p.CategoryId")
                  .LeftJoin("ProductBranch pb WITH(NOLOCK)", "pb.ProductId = p.Id AND pb.BranchId = " + (_tenantProvider.GetBranchId() ?? 0))
                  .LeftJoin("ProductTax pt WITH(NOLOCK)", "pt.ProductId = p.Id AND pt.Status = 0")
                  .LeftJoin("Tax t WITH(NOLOCK)", "t.Id = pt.TaxId")
                  .Where("p.Id", "=", request.ProductId)
                  .Where("p.IsDeleted", "=", false);

                // Execute the query
                var product = await _queryService.QueryFirstOrDefaultAsync<dynamic>(qb);
                
                if (product == null)
                {
                    return Result<GetProductDetailResponse>.Failure($"Product with ID {request.ProductId} not found");
                }

                // Get product images
                var imageQb = new QueryBuilder();
                imageQb.Select("Image", "CreatedDate")
                       .From("ProductImage WITH(NOLOCK)")
                       .Where("ProductId", "=", request.ProductId)
                       .OrderBy("CreatedDate", false);

                var images = await _queryService.QueryAsync<dynamic>(imageQb);

                var response = new GetProductDetailResponse
                {
                    Id = product.Id,
                    Name = product.Name,
                    Code = product.Code,
                    Description = product.Description ?? string.Empty,
                    BasePrice = Money.Create(product.BasePrice, _tenantInfo.CurrencyCode),
                    FullName = product.FullName ?? string.Empty,
                    MasterCode = product.MasterCode ?? string.Empty,
                    Unit = product.Unit ?? string.Empty,
                    Cost = Money.Create(product.Cost ?? 0m, _tenantInfo.CurrencyCode),
                    OnHand = product.OnHand ?? 0,
                    MinQuantity = product.MinQuantity ?? 0,
                    MaxQuantity = product.MaxQuantity ?? 0,
                    AllowSale = product.AllowsSale,
                    CategoryId = product.CategoryId,
                    IsTimeType = product.IsTimeType,
                    IsRewardPoint = product.IsRewardPoint,
                    IsTopping = product.IsTopping,
                    IsProcessedGoods = product.IsProcessedGoods,
                    InventoryTrackingIgnore = product.InventoryTrackingIgnore,
                    IsTimeServices = product.IsTimeServices,
                    IsActive = product.IsActive,
                    IsDeleted = product.IsDeleted,
                    ProductType = product.ProductType == null ? null : ProductTypes.From(product.ProductType),
                    ProductGroup = product.ProductGroup  == null ? null :  ProductGroupTypes.From(product.ProductGroup),
                    OrderTemplate = product.OrderTemplate ?? string.Empty,
                    Category = new CategoryResponse(
                        product.Category_Id,
                        product.Category_Name ?? string.Empty,
                        product.Category_Description ?? string.Empty,
                        product.Category_IsActive
                    ),
                    Images = [.. images.Select(img => new ProductImageResponse(
                        img.Image,
                        img.CreatedDate
                    ))],
                    Tax = product.Tax_Id != null ? new TaxResponse
                    {
                        Id = (int)product.Tax_Id,
                        Name = product.Tax_Name ?? string.Empty,
                        TaxRate = (decimal)product.Tax_TaxRate,
                        TaxType = product.Tax_TaxType
                    } : null
                };

                return Result<GetProductDetailResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error("Error retrieving product details", ex);
                return Result<GetProductDetailResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
} 