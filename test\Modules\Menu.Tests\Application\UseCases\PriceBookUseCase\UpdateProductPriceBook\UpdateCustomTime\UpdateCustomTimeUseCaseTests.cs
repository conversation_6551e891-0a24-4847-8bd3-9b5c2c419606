using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Dtos.Request;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateCustomTime;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using Moq;
using System.Text.Json;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateCustomTime;

public class UpdateCustomTimeUseCaseTests
{
    private readonly Mock<IValidator<UpdateCustomTimeRequest>> _validatorMock;
    private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly UpdateCustomTimeUseCase _useCase;

    public UpdateCustomTimeUseCaseTests()
    {
        _validatorMock = new Mock<IValidator<UpdateCustomTimeRequest>>();
        _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
        _mapperMock = new Mock<IMapper>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _useCase = new UpdateCustomTimeUseCase(
            _validatorMock.Object,
            _priceBookRepositoryMock.Object,
            _mapperMock.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WhenRequestIsValid_ShouldUpdateCustomTime()
    {
        // Arrange
        var request = CreateValidRequest();
        var priceBook = CreatePriceBook();
        var response = new UpdateCustomTimeResponse
        {
            Id = priceBook.Id,
            Name = priceBook.Name,
            CustomTime = JsonSerializer.Serialize(request.CustomTime),
            IsSuccess = true
        };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        _mapperMock.Setup(m => m.Map<UpdateCustomTimeResponse>(It.IsAny<PriceBook>()))
            .Returns(response);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(response, result.Value);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateValidRequest();
        var validationErrors = new List<string> { "Validation error" };
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(false, validationErrors));

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(validationErrors, result.ValidationErrors);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenPriceBookNotFound_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateValidRequest();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PriceBook?)null);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal($"Price book with ID {request.Id} not found", result.ErrorMessage);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()), Times.Once);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidMonths_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidMonths();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Months must be between 1 and 12", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidDaysOfWeek_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidDaysOfWeek();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Days of week must be between 1 and 7 (1 is Monday)", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidWeeksInMonth_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidWeeksInMonth();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Weeks in month must be between 1 and 5", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidDays_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidDays();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Days must be between 1 and 31", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidTimeRangeFormat_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidTimeRangeFormat();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Invalid time format. Use HH:mm format.", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenInvalidTimeRange_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateRequestWithInvalidTimeRange();
        var priceBook = CreatePriceBook();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Time from must be before time to", result.ValidationErrors!);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenCustomTimeIsNull_ShouldUpdateSuccessfully()
    {
        // Arrange
        var request = new UpdateCustomTimeRequest { Id = 1, CustomTime = null };
        var priceBook = CreatePriceBook();
        var response = new UpdateCustomTimeResponse
        {
            Id = priceBook.Id,
            Name = priceBook.Name,
            CustomTime = null,
            IsSuccess = true
        };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        _mapperMock.Setup(m => m.Map<UpdateCustomTimeResponse>(It.IsAny<PriceBook>()))
            .Returns(response);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    private static UpdateCustomTimeRequest CreateValidRequest()
    {
        return new UpdateCustomTimeRequest
        {
            Id = 1,
            CustomTime = new PriceBookCustomTimeRequest
            {
                Months = new List<int> { 1, 2, 3 },
                DaysOfWeek = new List<int> { 1, 2, 3, 4, 5 },
                WeeksInMonth = 1,
                Days = new List<int> { 1, 15, 30 },
                TimeRanges = new List<TimeRange>
                {
                    new TimeRange { TimeFrom = "08:00", TimeTo = "22:00" }
                }
            }
        };
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidMonths()
    {
        var request = CreateValidRequest();
        request.CustomTime!.Months = new List<int> { 0, 13 }; // Invalid months
        return request;
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidDaysOfWeek()
    {
        var request = CreateValidRequest();
        request.CustomTime!.DaysOfWeek = new List<int> { 0, 8 }; // Invalid days of week
        return request;
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidWeeksInMonth()
    {
        var request = CreateValidRequest();
        request.CustomTime!.WeeksInMonth = 6; // Invalid week number
        return request;
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidDays()
    {
        var request = CreateValidRequest();
        request.CustomTime!.Days = new List<int> { 0, 32 }; // Invalid days
        return request;
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidTimeRangeFormat()
    {
        var request = CreateValidRequest();
        request.CustomTime!.TimeRanges = new List<TimeRange>
        {
            new TimeRange { TimeFrom = "invalid", TimeTo = "invalid" }
        };
        return request;
    }

    private static UpdateCustomTimeRequest CreateRequestWithInvalidTimeRange()
    {
        var request = CreateValidRequest();
        request.CustomTime!.TimeRanges = new List<TimeRange>
        {
            new TimeRange { TimeFrom = "23:00", TimeTo = "22:00" } // End time before start time
        };
        return request;
    }

    private static PriceBook CreatePriceBook()
    {
        return PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
    }
} 