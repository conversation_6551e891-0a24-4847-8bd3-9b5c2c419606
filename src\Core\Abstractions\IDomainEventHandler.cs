using KvFnB.Core.Domain;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for domain event handlers
    /// </summary>
    /// <typeparam name="T">The type of domain event to handle</typeparam>
    public interface IDomainEventHandler<in T> where T : DomainEvent
    {
        /// <summary>
        /// Handles the domain event
        /// </summary>
        /// <param name="domainEvent">The domain event to handle</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task Handle(T domainEvent, CancellationToken cancellationToken = default);
    }
} 