using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.ValueObjects;

namespace KvFnB.Modules.Menu.Domain.Tests.Models
{
    public class ProductTests
    {
        private static Product CreateDefaultProduct()
        {
            return Product.CreateProduct(
                "P001",
                "Test Product",
                1,
                (byte)ProductTypes.Normal.Id,
                "pcs",
                1.0f,
                "MP001"
            );
        }

        [Fact]
        public void CreateProduct_ShouldCreateProduct_WhenValidParameters()
        {
            // Arrange
            var code = "P001";
            var name = "Test Product";
            var categoryId = 1;
            var description = "Test Description";
            var allowSale = true;
            var basePrice = 10.99m;
            var unit = "pcs";
            var conversionValue = 1.0f;
            var masterCode = "MP001";

            // Act
            var product = Product.CreateProduct(
                code,
                name,
                categoryId,
                (byte)ProductTypes.Normal.Id,
                unit,
                conversionValue,
                masterCode);

            product.SetDescription(description);
            product.SetBasePrice(basePrice);

            // Assert
            Assert.NotNull(product);
            Assert.Equal(code, product.Code);
            Assert.Equal(name, product.Name);
            Assert.Equal(categoryId, product.CategoryId);
            Assert.Equal(description, product.Description);
            Assert.Equal(allowSale, product.AllowSale);
            Assert.Equal(basePrice, product.BasePrice);
            Assert.Equal(unit, product.Unit);
            Assert.Equal(conversionValue, product.ConversionValue);
            Assert.Equal(masterCode, product.MasterCode);
            Assert.True(product.IsActive);
            Assert.False(product.IsDeleted);
            Assert.False(product.HasVariants);
            Assert.Empty(product.ProductImages);
            Assert.Empty(product.ProductBranches);
            Assert.Empty(product.ProductToppings);
            Assert.Empty(product.ProductTaxes);
        }

        [Fact]
        public void CreateProduct_ShouldUseSameCodeAsMasterCode_WhenMasterCodeIsNull()
        {
            // Arrange
            var code = "P001";
            var name = "Test Product";
            var categoryId = 1;
            var conversionValue = 1.0f;

            // Act
            var product = Product.CreateProduct(
                code,
                name,
                categoryId,
                (byte)ProductTypes.Normal.Id,
                null,
                conversionValue,
                code);


            // Assert
            Assert.Equal(code, product.MasterCode);
        }

        [Fact]
        public void Update_ShouldUpdateAllProperties()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var newCode = "P002";
            var newName = "Updated Name";
            var newCategoryId = 2;
            var newDescription = "Updated Description";
            var newBasePrice = 19.99m;
            var newUnit = "pcs";
            var newConversionValue = 2.0f;

            // Act
            product.Update(
                newCode,
                newName,
                newCategoryId,
                newDescription,
                newBasePrice,
                newUnit,
                newConversionValue);

            // Assert
            Assert.Equal(newCode, product.Code);
            Assert.Equal(newName, product.Name);
            Assert.Equal(newCategoryId, product.CategoryId);
            Assert.Equal(newDescription, product.Description);
            Assert.Equal(newBasePrice, product.BasePrice);
            Assert.Equal(newUnit, product.Unit);
            Assert.Equal(newConversionValue, product.ConversionValue);
        }

        [Fact]
        public void Activate_ShouldSetIsActiveToTrue()
        {
            // Arrange
            var product = CreateDefaultProduct();
            product.Deactivate();

            // Act
            product.Activate();

            // Assert
            Assert.True(product.IsActive);
        }

        [Fact]
        public void Deactivate_ShouldSetIsActiveToFalse()
        {
            // Arrange
            var product = CreateDefaultProduct();

            // Act
            product.Deactivate();

            // Assert
            Assert.False(product.IsActive);
        }

        [Fact]
        public void Delete_ShouldSetIsDeletedToTrue()
        {
            // Arrange
            var product = CreateDefaultProduct();

            // Act
            product.Delete();

            // Assert
            Assert.True(product.IsDeleted);
        }

        [Fact]
        public void SetAsMasterVariant_ShouldSetMasterProductId()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var masterProductId = 2L;

            // Act
            product.SetAsMasterVariant(masterProductId);

            // Assert
            Assert.Equal(masterProductId, product.MasterProductId);
            Assert.False(product.HasVariants);
        }

        [Fact]
        public void SetAsMasterVariant_ShouldThrowException_WhenProductIsItsOwnVariant()
        {
            // Arrange
            var product = CreateDefaultProduct();
            product.SetAsMasterVariant(2); // Set a different master product first
            var productId = product.Id;

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => product.SetAsMasterVariant(productId));
        }

        [Fact]
        public void SetAsHasVariants_ShouldSetHasVariantsToTrue()
        {
            // Arrange
            var product = CreateDefaultProduct();
            product.SetAsMasterVariant(1);

            // Act
            product.SetAsHasVariants();

            // Assert
            Assert.True(product.HasVariants);
            Assert.Null(product.MasterProductId);
        }

        [Fact]
        public void UpdateProductType_ShouldUpdateProductType()
        {
            // Arrange
            var product = CreateDefaultProduct();
            byte? newProductType = 2;

            // Act
            product.UpdateProductType(newProductType);

            // Assert
            Assert.Equal(newProductType, product.ProductType);
        }

        [Fact]
        public void UpdateProductGroup_ShouldUpdateProductGroup()
        {
            // Arrange
            var product = CreateDefaultProduct();
            byte? newProductGroup = 3;

            // Act
            product.UpdateProductGroup(newProductGroup);

            // Assert
            Assert.Equal(newProductGroup, product.ProductGroup);
        }

        [Fact]
        public void UpdateInventorySettings_ShouldUpdateInventoryTrackingIgnore()
        {
            // Arrange
            var product = CreateDefaultProduct();

            // Act
            product.UpdateInventorySettings(true);

            // Assert
            Assert.True(product.InventoryTrackingIgnore);
        }

        [Fact]
        public void UpdateRank_ShouldUpdateRank()
        {
            // Arrange
            var product = CreateDefaultProduct();
            int? newRank = 5;

            // Act
            product.UpdateRank(newRank);

            // Assert
            Assert.Equal(newRank, product.Rank);
        }

        [Fact]
        public void AddBranch_ShouldAddBranchToProduct()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var branch = ProductBranch.Create(1, 1, 100m, null, false, null);
            
            // Set inventory values
            branch.UpdateInventory(10.0f, 5.0f, 15.0f);

            // Act
            product.AddInventoryOnBranch(branch);

            // Assert
            Assert.Single(product.ProductBranches);
            var addedBranch = Assert.Single(product.ProductBranches);
            Assert.Equal(branch.BranchId, addedBranch.BranchId);
            Assert.Equal(branch.OnHand, addedBranch.OnHand);
            Assert.Equal(branch.OnOrder, addedBranch.OnOrder);
            Assert.Equal(branch.Cost, addedBranch.Cost);
        }

        [Fact]
        public void AddBranch_ShouldThrowException_WhenBranchAlreadyExists()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var branch = ProductBranch.Create(1, 1, 100m, null, false, null);
            product.AddInventoryOnBranch(branch);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => product.AddInventoryOnBranch(branch));
        }

        [Fact]
        public void UpdateBranch_ShouldUpdateExistingBranch()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var branch = ProductBranch.Create(1, 1, 100m, null, false, null);
            product.AddInventoryOnBranch(branch);
            var updatedBranch = ProductBranch.Create(1, 1, 120m, null, false, null);
            
            // Update inventory values
            updatedBranch.UpdateInventory(15.0f, 10.0f, 17.0f);

            // Act
            product.UpdateInventoryOnBranch(updatedBranch);

            // Assert
            var result = Assert.Single(product.ProductBranches);
            Assert.Equal(updatedBranch.BranchId, result.BranchId);
            Assert.Equal(updatedBranch.OnHand, result.OnHand);
            Assert.Equal(updatedBranch.OnOrder, result.OnOrder);
        }

        [Fact]
        public void UpdateBranch_ShouldThrowException_WhenBranchDoesNotExist()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var branch = ProductBranch.Create(1, 1, 100m, null, false, null);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => product.UpdateInventoryOnBranch(branch));
        }

        [Fact]
        public void SetProductTax_ShouldSetProductTax()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var productTax = ProductTax.Create(1, "VAT", 0.1m, 1);

            // Act
            product.SetProductTax(productTax);

            // Assert
            Assert.NotNull(product.ProductTaxes);
            Assert.Equal(productTax.TaxId, product.ProductTaxes.First(x => x.Status == 0).TaxId);
            Assert.Equal(productTax.Name, product.ProductTaxes.First(x => x.Status == 0).Name);
            Assert.Equal(productTax.TaxRate, product.ProductTaxes.First(x => x.Status == 0).TaxRate);
        }

        [Fact]
        public void AddImage_ShouldAddImageToProduct()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var imageUrl = "http://example.com/image.jpg";

            // Act
            product.AddImage(imageUrl, null);

            // Assert
            var image = Assert.Single(product.ProductImages);
            Assert.Equal(imageUrl, image.Image);
        }

        [Fact]
        public void AddTopping_ShouldAddToppingProduct()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var toppingProductId = 2L;

            // Act
            product.AddTopping(toppingProductId, null);

            // Assert
            var result = Assert.Single(product.ProductToppings);
            Assert.Equal(toppingProductId, result.ToppingProductId);
        }

        [Fact]
        public void AddTopping_ShouldThrowException_WhenToppingAlreadyExists()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var toppingProductId = 2L;
            product.AddTopping(toppingProductId, null);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => product.AddTopping(toppingProductId, null));
        }

        [Fact]
        public void RemoveTopping_ShouldRemoveToppingProduct()
        {
            // Arrange
            var product = CreateDefaultProduct();
            var toppingProductId = 2L;
            product.AddTopping(toppingProductId, null);

            // Act
            product.RemoveTopping(toppingProductId);

            // Assert
            Assert.Empty(product.ProductToppings);
        }
    }
}