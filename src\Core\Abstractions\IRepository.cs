using KvFnB.Core.Domain;

namespace KvFnB.Core.Abstractions
{
    public interface IRepository<TEntity, TKey>
        where TEntity : Entity<TKey>
    {
        Task<TEntity?> GetAsync(TKey id, CancellationToken cancellationToken = default);

        Task<TEntity?> GetAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        Task<bool> AnyAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        Task<IList<TEntity>> FindAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);

        Task<TEntity> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);

        Task RemoveAsync(TEntity entity, CancellationToken cancellationToken = default);

    }
}