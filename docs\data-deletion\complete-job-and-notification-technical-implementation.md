# Technical Implementation: Complete Job and Notification Service

## 1. Overview

This technical implementation document outlines the practical implementation of the Complete Job and Notification Service for the data deletion system. The service will be implemented as a background worker in the `KvFnB.DataDeletionSchedulerBackgroundService` project.

## 2. Project Structure

```
src/Presentation/BackgroundServices/KvFnB.DataDeletionSchedulerBackgroundService/
├── Program.cs
├── Worker.cs
├── appsettings.json
├── appsettings.Development.json
├── Services/
│   └── CompleteJobHostedService.cs
├── Configuration/
│   └── JobConfiguration.cs
└── Properties/
    └── launchSettings.json
```

## 3. Configuration Setup

### 3.1. Configuration Models

```csharp
namespace KvFnB.DataDeletionSchedulerBackgroundService.Configuration
{
    public class JobConfiguration
    {
        public CompleteJobConfiguration CompleteJob { get; set; } = new();
        public EmailNotificationConfiguration EmailNotification { get; set; } = new();
    }

    public class CompleteJobConfiguration
    {
        public int IntervalMinutes { get; set; } = 5;
        public ExecutionTimeWindow ExecutionTime { get; set; } = new();
        public int CancellationTimeMinutes { get; set; } = 30;
        public int BatchSize { get; set; } = 100;
    }

    public class ExecutionTimeWindow
    {
        public int FromHour { get; set; } = 23;
        public int ToHour { get; set; } = 5;
    }

    public class EmailNotificationConfiguration
    {
        public bool Enabled { get; set; } = true;
        public string TemplateId { get; set; } = "delete-data-complete";
        public string FromEmail { get; set; } = "<EMAIL>";
        public string FromName { get; set; } = "System Admin";
    }
}
```

### 3.2. appsettings.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=KvFnB;User Id=sa;Password=yourStrongPassword;TrustServerCertificate=True"
  },
  "JobSettings": {
    "CompleteJob": {
      "IntervalMinutes": 5,
      "ExecutionTime": {
        "FromHour": 23,
        "ToHour": 5
      },
      "CancellationTimeMinutes": 30,
      "BatchSize": 100
    },
    "EmailNotification": {
      "Enabled": true,
      "TemplateId": "delete-data-complete",
      "FromEmail": "<EMAIL>",
      "FromName": "System Admin"
    }
  }
}
```

## 4. Background Service Implementation

### 4.1. Complete Job Hosted Service

```csharp
using KvFnB.Core.Abstractions;
using KvFnB.DataDeletionSchedulerBackgroundService.Configuration;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob;
using Microsoft.Extensions.Options;

namespace KvFnB.DataDeletionSchedulerBackgroundService.Services
{
    public class CompleteJobHostedService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<CompleteJobHostedService> _logger;
        private readonly JobConfiguration _configuration;
        private readonly IDistributedLockProvider _lockProvider;
        
        private const string LOCK_KEY = "CompleteJobProcessing";
        
        public CompleteJobHostedService(
            IServiceProvider serviceProvider,
            ILogger<CompleteJobHostedService> logger,
            IOptions<JobConfiguration> configuration,
            IDistributedLockProvider lockProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
            _lockProvider = lockProvider ?? throw new ArgumentNullException(nameof(lockProvider));
        }
        
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Complete Job Service is starting at: {time}", DateTimeOffset.Now);
            
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var currentHour = DateTime.Now.Hour;
                    
                    // Check if current time is within execution window
                    bool isWithinExecutionWindow = IsWithinExecutionWindow(currentHour);
                    
                    if (isWithinExecutionWindow)
                    {
                        _logger.LogInformation("Current time is within execution window. Executing job with timeout of {timeout} minutes.", 
                            _configuration.CompleteJob.CancellationTimeMinutes);
                        
                        // Create cancellation token source for job timeout
                        using (var jobCts = new CancellationTokenSource(
                            TimeSpan.FromMinutes(_configuration.CompleteJob.CancellationTimeMinutes)))
                        using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(jobCts.Token, stoppingToken))
                        {
                            try
                            {
                                // Use distributed lock to ensure only one instance runs at a time
                                await _lockProvider.LockAsync(
                                    LOCK_KEY,
                                    TimeSpan.FromMinutes(_configuration.CompleteJob.CancellationTimeMinutes),
                                    async () =>
                                    {
                                        _logger.LogInformation("Acquired distributed lock for CompleteJob processing");
                                        
                                        using (var scope = _serviceProvider.CreateScope())
                                        {
                                            var useCase = scope.ServiceProvider.GetRequiredService<CompleteProcessingJobUseCase>();
                                            var request = new CompleteProcessingJobRequest
                                            {
                                                BatchSize = _configuration.CompleteJob.BatchSize
                                            };
                                            
                                            var result = await useCase.ExecuteAsync(request, linkedCts.Token);
                                            
                                            if (result.IsSuccess)
                                            {
                                                _logger.LogInformation(
                                                    "CompleteProcessingJob completed successfully. Updated: {total}, Completed: {completed}, Failed: {failed}, Notifications: {notifications}, Time: {time}ms",
                                                    result.Value.UpdatedJobsCount,
                                                    result.Value.CompletedJobsCount,
                                                    result.Value.FailedJobsCount,
                                                    result.Value.NotificationsSent,
                                                    result.Value.ProcessingTimeMs);
                                            }
                                            else
                                            {
                                                _logger.LogWarning("CompleteProcessingJob failed: {errors}", 
                                                    string.Join(", ", result.Errors));
                                            }
                                        }
                                        
                                        _logger.LogInformation("Released distributed lock for CompleteJob processing");
                                    });
                            }
                            catch (OperationCanceledException)
                            {
                                if (jobCts.IsCancellationRequested && !stoppingToken.IsCancellationRequested)
                                {
                                    _logger.LogWarning("Job execution was canceled after timeout of {timeout} minutes", 
                                        _configuration.CompleteJob.CancellationTimeMinutes);
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Current time {currentHour} is outside execution window ({fromHour} to {toHour}). Skipping execution.", 
                            currentHour, 
                            _configuration.CompleteJob.ExecutionTime.FromHour, 
                            _configuration.CompleteJob.ExecutionTime.ToHour);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing completed jobs");
                }
                
                // Wait until next execution time
                await Task.Delay(
                    TimeSpan.FromMinutes(_configuration.CompleteJob.IntervalMinutes), 
                    stoppingToken);
            }
            
            _logger.LogInformation("Complete Job Service is stopping at: {time}", DateTimeOffset.Now);
        }
        
        private bool IsWithinExecutionWindow(int currentHour)
        {
            int fromHour = _configuration.CompleteJob.ExecutionTime.FromHour;
            int toHour = _configuration.CompleteJob.ExecutionTime.ToHour;
            
            // Handle overnight window (e.g., 23:00 - 05:00)
            if (fromHour > toHour)
            {
                return currentHour >= fromHour || currentHour <= toHour;
            }
            // Handle same-day window (e.g., 01:00 - 05:00)
            else
            {
                return currentHour >= fromHour && currentHour <= toHour;
            }
        }
    }
}
```

## 5. Application Startup and DI Configuration

### 5.1. Program.cs

```csharp
using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.DataDeletionSchedulerBackgroundService.Configuration;
using KvFnB.DataDeletionSchedulerBackgroundService.Services;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob;
using KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection;
using KvFnB.Shared.Distributed;
using Microsoft.EntityFrameworkCore;

var builder = Host.CreateApplicationBuilder(args);

// Register configuration
builder.Services.Configure<JobConfiguration>(
    builder.Configuration.GetSection("JobSettings"));

// Register hosted service
builder.Services.AddHostedService<CompleteJobHostedService>();

// Register database context
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlOptions => sqlOptions.EnableRetryOnFailure(3)));

// Register distributed lock provider
builder.Services.AddSingleton<IDistributedLockProvider, DistributedLockProvider>();

// Register data management module services
builder.Services.RegisterDataManagementModule();

// Register email service
builder.Services.AddScoped<IEmailService, EmailService>();

// Register validator
builder.Services.AddScoped<IValidator<CompleteProcessingJobRequest>, CompleteProcessingJobValidator>();

// Register query service
builder.Services.AddScoped<IQueryService, DapperQueryService>();

// Build and run the host
var host = builder.Build();
await host.RunAsync();
```

## 6. Implementation of Distributed Lock Provider

```csharp
using Microsoft.Extensions.Caching.Distributed;

namespace KvFnB.Shared.Distributed
{
    public class DistributedLockProvider : IDistributedLockProvider
    {
        private readonly IDistributedCache _cache;
        private readonly ILogger<DistributedLockProvider> _logger;
        
        public DistributedLockProvider(
            IDistributedCache cache, 
            ILogger<DistributedLockProvider> logger)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public async Task LockAsync(string key, TimeSpan expiry, Func<Task> action)
        {
            string lockId = Guid.NewGuid().ToString();
            bool acquired = false;
            
            try
            {
                acquired = await TryAcquireLockAsync(key, lockId, expiry);
                
                if (acquired)
                {
                    await action();
                }
                else
                {
                    _logger.LogInformation("Could not acquire lock for key {key}. Another process has the lock.", key);
                }
            }
            finally
            {
                if (acquired)
                {
                    await ReleaseLockAsync(key, lockId);
                }
            }
        }
        
        private async Task<bool> TryAcquireLockAsync(string key, string lockId, TimeSpan expiry)
        {
            try
            {
                // Try to add the lock key - if it exists, we couldn't acquire the lock
                await _cache.SetStringAsync(
                    $"lock:{key}", 
                    lockId,
                    new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = expiry
                    });
                
                // Verify we got the lock
                string? currentLockId = await _cache.GetStringAsync($"lock:{key}");
                return string.Equals(currentLockId, lockId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acquiring distributed lock for key {key}", key);
                return false;
            }
        }
        
        private async Task ReleaseLockAsync(string key, string lockId)
        {
            try
            {
                // Check if we still own the lock before removing it
                string? currentLockId = await _cache.GetStringAsync($"lock:{key}");
                
                if (string.Equals(currentLockId, lockId))
                {
                    await _cache.RemoveAsync($"lock:{key}");
                    _logger.LogInformation("Released lock for key {key}", key);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error releasing distributed lock for key {key}", key);
            }
        }
    }
    
    public interface IDistributedLockProvider
    {
        Task LockAsync(string key, TimeSpan expiry, Func<Task> action);
    }
}
```

## 7. Interface Implementation

### 7.1. Email Service Implementation

```csharp
namespace KvFnB.Shared.Email
{
    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly IOptions<EmailConfiguration> _emailConfig;
        private readonly HttpClient _httpClient;
        
        public EmailService(
            ILogger<EmailService> logger,
            IOptions<EmailConfiguration> emailConfig,
            HttpClient httpClient)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _emailConfig = emailConfig ?? throw new ArgumentNullException(nameof(emailConfig));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        }
        
        public async Task SendTemplateEmailAsync(
            string recipientEmail,
            string templateId,
            object templateData,
            string fromEmail,
            string fromName,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Sending email to {recipient} using template {templateId}", 
                    recipientEmail, templateId);
                
                var emailRequest = new EmailRequest
                {
                    To = new[] { recipientEmail },
                    From = new EmailAddress { Email = fromEmail, Name = fromName },
                    TemplateId = templateId,
                    TemplateData = templateData
                };
                
                var response = await _httpClient.PostAsJsonAsync(
                    _emailConfig.Value.ApiEndpoint,
                    emailRequest,
                    cancellationToken);
                
                response.EnsureSuccessStatusCode();
                
                _logger.LogInformation("Successfully sent email to {recipient} using template {templateId}", 
                    recipientEmail, templateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {recipient} using template {templateId}", 
                    recipientEmail, templateId);
                
                throw;
            }
        }
        
        private class EmailRequest
        {
            public string[] To { get; set; } = Array.Empty<string>();
            public EmailAddress From { get; set; } = new();
            public string TemplateId { get; set; } = string.Empty;
            public object? TemplateData { get; set; }
        }
        
        private class EmailAddress
        {
            public string Email { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
        }
    }
    
    public class EmailConfiguration
    {
        public string ApiEndpoint { get; set; } = "https://api.example.com/email";
        public string ApiKey { get; set; } = string.Empty;
    }
}
```

## 8. Integration Testing

### 8.1. Unit Testing the Hosted Service

```csharp
namespace KvFnB.DataDeletionSchedulerBackgroundService.Tests.Services
{
    public class CompleteJobHostedServiceTests
    {
        private readonly Mock<IServiceProvider> _serviceProviderMock;
        private readonly Mock<ILogger<CompleteJobHostedService>> _loggerMock;
        private readonly Mock<IDistributedLockProvider> _lockProviderMock;
        private readonly Mock<IServiceScope> _serviceScopeMock;
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
        private readonly JobConfiguration _configuration;
        
        public CompleteJobHostedServiceTests()
        {
            _serviceProviderMock = new Mock<IServiceProvider>();
            _loggerMock = new Mock<ILogger<CompleteJobHostedService>>();
            _lockProviderMock = new Mock<IDistributedLockProvider>();
            _serviceScopeMock = new Mock<IServiceScope>();
            _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
            
            // Setup service provider to return scope factory
            _serviceProviderMock
                .Setup(x => x.GetService(typeof(IServiceScopeFactory)))
                .Returns(_serviceScopeFactoryMock.Object);
                
            // Setup scope factory to create scope
            _serviceScopeFactoryMock
                .Setup(x => x.CreateScope())
                .Returns(_serviceScopeMock.Object);
                
            // Setup service scope provider
            _serviceScopeMock
                .Setup(x => x.ServiceProvider)
                .Returns(_serviceProviderMock.Object);
                
            // Create configuration
            _configuration = new JobConfiguration
            {
                CompleteJob = new CompleteJobConfiguration
                {
                    IntervalMinutes = 1,
                    ExecutionTime = new ExecutionTimeWindow
                    {
                        FromHour = 0,
                        ToHour = 23
                    },
                    CancellationTimeMinutes = 5,
                    BatchSize = 10
                }
            };
        }
        
        [Fact]
        public async Task ExecuteAsync_ShouldAcquireLock_AndExecuteUseCase()
        {
            // Arrange
            var useCaseMock = new Mock<CompleteProcessingJobUseCase>(
                Mock.Of<IValidator<CompleteProcessingJobRequest>>(),
                Mock.Of<AppDbContext>(),
                Mock.Of<IEmailService>(),
                Mock.Of<ILogger<CompleteProcessingJobUseCase>>(),
                Mock.Of<IConfiguration>(),
                Mock.Of<IQueryService>());
                
            useCaseMock
                .Setup(x => x.ExecuteAsync(It.IsAny<CompleteProcessingJobRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CompleteProcessingJobResponse>.Success(new CompleteProcessingJobResponse
                {
                    UpdatedJobsCount = 5,
                    CompletedJobsCount = 3,
                    FailedJobsCount = 2,
                    NotificationsSent = 3,
                    ProcessingTimeMs = 1500
                }));
                
            _serviceProviderMock
                .Setup(x => x.GetService(typeof(CompleteProcessingJobUseCase)))
                .Returns(useCaseMock.Object);
                
            // Setup lock provider to execute the callback
            _lockProviderMock
                .Setup(x => x.LockAsync(
                    It.IsAny<string>(),
                    It.IsAny<TimeSpan>(),
                    It.IsAny<Func<Task>>()))
                .Returns((string key, TimeSpan expiry, Func<Task> action) => action());
                
            // Create the service
            var options = Options.Create(_configuration);
            var service = new CompleteJobHostedService(
                _serviceProviderMock.Object,
                _loggerMock.Object,
                options,
                _lockProviderMock.Object);
                
            // Create cancellation token that cancels after a short delay
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromMilliseconds(100));
            
            // Act
            await service.StartAsync(cts.Token);
            await Task.Delay(200); // Allow time for execution
            await service.StopAsync(cts.Token);
            
            // Assert
            _lockProviderMock.Verify(
                x => x.LockAsync(
                    It.Is<string>(s => s == "CompleteJobProcessing"),
                    It.IsAny<TimeSpan>(),
                    It.IsAny<Func<Task>>()),
                Times.AtLeastOnce);
                
            useCaseMock.Verify(
                x => x.ExecuteAsync(
                    It.Is<CompleteProcessingJobRequest>(r => r.BatchSize == 10),
                    It.IsAny<CancellationToken>()),
                Times.AtLeastOnce);
        }
    }
}
```

## 9. Deployment Considerations

### 9.1. Docker Configuration

```dockerfile
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/Presentation/BackgroundServices/KvFnB.DataDeletionSchedulerBackgroundService/KvFnB.DataDeletionSchedulerBackgroundService.csproj", "src/Presentation/BackgroundServices/KvFnB.DataDeletionSchedulerBackgroundService/"]
COPY ["src/Modules/DataManagement/Application/KvFnB.Modules.DataManagement.Application.csproj", "src/Modules/DataManagement/Application/"]
COPY ["src/Shared/KvFnB.Shared.csproj", "src/Shared/"]
COPY ["src/Modules/DataManagement/Infrastructure/KvFnB.Modules.DataManagement.Infrastructure.csproj", "src/Modules/DataManagement/Infrastructure/"]
RUN dotnet restore "src/Presentation/BackgroundServices/KvFnB.DataDeletionSchedulerBackgroundService/KvFnB.DataDeletionSchedulerBackgroundService.csproj"
COPY . .
WORKDIR "/src/src/Presentation/BackgroundServices/KvFnB.DataDeletionSchedulerBackgroundService"
RUN dotnet build "KvFnB.DataDeletionSchedulerBackgroundService.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "KvFnB.DataDeletionSchedulerBackgroundService.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "KvFnB.DataDeletionSchedulerBackgroundService.dll"]
```

### 9.2. Health Monitoring

Add health check endpoints to ensure the service is running correctly:

```csharp
// In Program.cs
builder.Services.AddHealthChecks()
    .AddDbContextCheck<AppDbContext>()
    .AddCheck<JobHealthCheck>("job_health");

var app = builder.Build();

// Configure health endpoint
app.MapHealthChecks("/health");

await app.RunAsync();

// Health check implementation
public class JobHealthCheck : IHealthCheck
{
    private readonly ILogger<JobHealthCheck> _logger;
    
    public JobHealthCheck(ILogger<JobHealthCheck> logger)
    {
        _logger = logger;
    }
    
    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check last execution time from some persistent store
            // For example, you might have a status record in the database
            
            return Task.FromResult(HealthCheckResult.Healthy("Background job is running normally"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return Task.FromResult(HealthCheckResult.Unhealthy("Background job health check failed", ex));
        }
    }
}
```

## 10. Conclusion

This technical implementation provides a robust, scalable solution for the Complete Job and Notification Service for the data deletion system. The service features:

1. **Scheduled Execution**: Runs at configurable intervals during specified hours
2. **Distributed Locking**: Ensures only one instance processes jobs at a time
3. **Graceful Timeout Handling**: Prevents jobs from running indefinitely
4. **Comprehensive Logging**: Detailed logging for monitoring and troubleshooting
5. **Unit Testing**: Tests for verifying functionality
6. **Health Monitoring**: Health checks for integration with monitoring systems

The implementation follows clean code practices, dependency injection principles, and separation of concerns to create a maintainable service that integrates with the existing KvFnB Core architecture. 