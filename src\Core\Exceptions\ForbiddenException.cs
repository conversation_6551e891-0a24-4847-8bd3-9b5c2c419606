﻿using System.Runtime.Serialization;
namespace KvFnB.Core.Exceptions
{
    [Serializable]
    public class ForbiddenException : Exception
    {
        public ForbiddenException(string message) : base(message) { }

        public ForbiddenException(string message, Exception innerException) : base(message, innerException) { }

        public ForbiddenException() { }

        /// <summary>
        /// Initializes a new instance of the ForbiddenException class with serialized data.
        /// </summary>
        /// <param name="info">The SerializationInfo that holds the serialized object data about the exception being thrown.</param>
        /// <param name="context">The StreamingContext that contains contextual information about the source or destination.</param>
        [Obsolete("This constructor is obsolete. Use the constructor that does not take a SerializationInfo and StreamingContext as parameters.")]
        protected ForbiddenException(SerializationInfo info, StreamingContext context) : base(info, context) 
        { }
    }
}
