using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Tests.Helpers;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Localization;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings
{
    public class UpdateProductToppingsUseCaseTests
    {
        private readonly Mock<IValidator<UpdateProductToppingsRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly UpdateProductToppingsUseCase _useCase;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly ILocalizationProvider _multiLang;

        public UpdateProductToppingsUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<UpdateProductToppingsRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _multiLang = TestLocalizationHelper.CreateVietnameseLocalizationProvider();

            _useCase = new UpdateProductToppingsUseCase(
                _validatorMock.Object,
                _productRepositoryMock.Object,
                _multiLang,
                _unitOfWorkMock.Object,
                _tenantProviderMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2, 3 }
            };
            
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2, 3 }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
        }

        [Fact]
        public async Task ExecuteAsync_WhenToppingProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2 }
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepositoryMock.Setup(r => r.GetAsync(2, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductIsNotTopping_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2 }
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            // Create a real topping Product instance
            var toppingProduct = Product.CreateProduct(
                code: "TOPPING-001",
                name: "Topping Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "TOPPING-001"
            );

            // Set IsTopping to false
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            typeof(Product).GetProperty("IsTopping").SetValue(toppingProduct, false);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepositoryMock.Setup(r => r.GetAsync(2, It.IsAny<CancellationToken>())).ReturnsAsync(toppingProduct);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldUpdateProductToppings()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2, 3 }
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );

            // Set ModifiedAt to the current time
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            typeof(Product).GetProperty("ModifiedAt").SetValue(product, DateTime.UtcNow);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            // Create real topping Product instances
            var toppingProduct1 = Product.CreateProduct(
                code: "TOPPING-001",
                name: "Topping Product 1",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "TOPPING-001"
            );

            // Set Id and IsTopping
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            typeof(Product).GetProperty("IsTopping").SetValue(toppingProduct1, true);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            var toppingProduct2 = Product.CreateProduct(
                code: "TOPPING-002",
                name: "Topping Product 2",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "TOPPING-002"
            );
            
            // Set Id and IsTopping
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            typeof(Product).GetProperty("IsTopping").SetValue(toppingProduct2, true);

            // Set IDs using reflection
            typeof(Product).GetProperty("Id").SetValue(toppingProduct1, 2L);
            typeof(Product).GetProperty("Id").SetValue(toppingProduct2, 3L);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            
            var response = new UpdateProductToppingsResponse
            {
                Id = 1,
                ToppingProductIds = request.ToppingProductIds,
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepositoryMock.Setup(r => r.GetAsync(2, It.IsAny<CancellationToken>())).ReturnsAsync(toppingProduct1);
            _productRepositoryMock.Setup(r => r.GetAsync(3, It.IsAny<CancellationToken>())).ReturnsAsync(toppingProduct2);
            _productRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>())).ReturnsAsync(product);
            
            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _productRepositoryMock.Verify(r => r.UpdateAsync(product, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductToppingsRequest
            {
                ProductId = 1,
                ToppingProductIds = new List<long> { 2, 3 }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var exception = await Assert.ThrowsAsync<Exception>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
        }
    }
} 