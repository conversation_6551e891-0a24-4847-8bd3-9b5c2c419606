using Confluent.Kafka;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.DeleteDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest;
using KvFnB.Modules.DataManagement.Application.UseCases.ScanScheduledRequestsUseCase;
using KvFnB.Modules.DataManagement.Application.UseCases.VerifyUserUseCases.VerifyUserForDataDeletion;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Modules.DataManagement.Infrastructure.Mapping;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetPageDeleteDataRequests;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetPageDeleteDataHistory;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.Handlers;
using KvFnB.Modules.DataManagement.Infrastructure.Configuration;
using KvFnB.Modules.DataManagement.Infrastructure.Repositories;
using KvFnB.Modules.DataManagement.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob;

namespace KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static IServiceCollection RegisterDataManagementModule(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<DataDeletionOptions>(configuration.GetSection("DataDeletion"));
			
            // Register repositories
            services.AddScoped<IDeleteDataRequestRepository, DeleteDataRequestRepository>();
            // Register services
            services.AddSingleton(provider => new ProducerBuilder<string, string>(provider.GetRequiredService<IConfiguration>().GetSection("KafkaProducer").Get<ProducerConfig>()).Build());
            services.AddSingleton<IMessageQueueService, KafkaMessageQueueService>();
            services.AddDataManagementServices();

            // Register use cases
            services.AddScoped<VerifyUserForDataDeletionUseCase>();
            services.AddScoped<CreateDeleteDataRequestUseCase>();
            services.AddScoped<UpdateDeleteDataRequestUseCase>();
            services.AddScoped<DeleteDeleteDataRequestUseCase>();
            services.AddScoped<GetPageDeleteDataRequestsUseCase>();
            services.AddScoped<GetPageDeleteDataHistoryUseCase>();

            // Register validators
            services.AddScoped<IValidator<VerifyUserForDataDeletionRequest>, VerifyUserForDataDeletionValidator>();
            services.AddScoped<IValidator<ScanScheduledRequestsRequest>, ScanScheduledRequestsValidator>();
            services.AddScoped<IValidator<CreateDeleteDataRequestRequest>, CreateDeleteDataRequestValidator>();
            services.AddScoped<IValidator<UpdateDeleteDataRequestRequest>, UpdateDeleteDataRequestValidator>();
            services.AddScoped<IValidator<DeleteDeleteDataRequestRequest>, DeleteDeleteDataRequestValidator>();
            services.AddScoped<IValidator<GetPageDeleteDataRequestsRequest>, GetPageDeleteDataRequestsValidator>();
            services.AddScoped<IValidator<GetPageDeleteDataHistoryRequest>, GetPageDeleteDataHistoryValidator>();

            // Register AutoMapper profiles
            services.AddAutoMapper(typeof(DataManagementMappingProfile));

            return services;
        }

        public static IServiceCollection RegisterDataProcessingBackgroundServiceModules(this IServiceCollection services)
        {
           // Register Data Processing Use Cases
            services.AddScoped<IDataProcessingUseCase, DataProcessingUseCase>();
            services.AddScoped<IValidator<DataProcessingRequest>, DataProcessingValidator>();
            services.AddScoped<IDataProcessingHandlerFactory, DataProcessingHandlerFactory>();
            services.AddScoped<IDataProcessingHandler, DeleteDataProcessingHandler>();  

            return services;
        }

        public static IServiceCollection RegisterBackgroundDataManagementModule(this IServiceCollection services)
        {
            // Register repositories
            services.AddScoped<IDeleteDataRequestRepository, DeleteDataRequestRepository>();
            services.AddScoped<IProcessingDataJobRepository, ProcessingDataJobRepository>();
            services.AddScoped<IDeleteDataDetailRepository, DeleteDataDetailRepository>();
            services.AddScoped<IDeleteDataHistoryRepository, DeleteDataHistoryRepository>();

            // Register services
            services.AddSingleton(provider => new ProducerBuilder<string, string>(provider.GetRequiredService<IConfiguration>().GetSection("Kafka").Get<ProducerConfig>()).Build());
            services.AddSingleton<IMessageQueueService, KafkaMessageQueueService>();
            services.AddDataManagementServices();

            // Register use cases
            services.AddScoped<ScanScheduledRequestsUseCase>();
            services.AddScoped<CompleteProcessingJobUseCase>();
            // Register validators
            services.AddScoped<IValidator<ScanScheduledRequestsRequest>, ScanScheduledRequestsValidator>();
            services.AddScoped<IValidator<CompleteProcessingJobRequest>, CompleteProcessingJobValidator>();
            // Register AutoMapper profiles
            services.AddAutoMapper(typeof(DataManagementMappingProfile));

            // Note: IDistributedLockProvider is already registered in the Shared Layer

            return services;
        }
    }
}