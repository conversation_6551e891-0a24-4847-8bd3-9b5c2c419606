using AutoMapper;
using KvFnB.Core.Domain;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.Dtos.Response;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.CreateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.RemoveProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.UpdateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Modules.Menu.Infrastructure.Mapping;

namespace KvFnB.Modules.Menu.Infrastructure.Tests.Mapping
{
    public class MenuMappingProfileTests
    {
        private readonly AutoMapper.IMapper _mapper;

        public MenuMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new MenuMappingProfile());
            });

            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Configuration_IsValid()
        {
            // Arrange & Act & Assert
            // The fact that the constructor succeeds means the configuration is valid
            Assert.NotNull(_mapper);
        }

        [Fact]
        public void Should_Map_Category_To_CreateProductCategoryResponse()
        {
            // Arrange
            var category = new Category
            {
                Id = 1,
                Name = "Beverages",
                ParentId = null
            };

            // Act
            var result = _mapper.Map<CreateProductCategoryResponse>(category);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(category.Id, result.Id);
            Assert.Equal(category.Name, result.Name);
            Assert.Equal(category.ParentId, result.ParentId);
        }

        [Fact]
        public void Should_Map_Category_To_UpdateProductCategoryResponse()
        {
            // Arrange
            var category = new Category
            {
                Id = 1,
                Name = "Beverages",
                ParentId = null,
                Rank = 1
            };

            // Act
            var result = _mapper.Map<UpdateProductCategoryResponse>(category);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(category.Id, result.Id);
            Assert.Equal(category.Name, result.Name);
            Assert.Equal(category.ParentId, result.ParentId);
            Assert.Equal(category.Rank, result.Rank);
        }

        [Fact]
        public void Should_Map_Category_To_RemoveProductCategoryResponse()
        {
            // Arrange
            var category = new Category
            {
                Id = 1,
                Name = "Beverages",
                ParentId = null,
                Rank = 1
            };

            // Act
            var result = _mapper.Map<RemoveProductCategoryResponse>(category);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(category.Id, result.Id);
            Assert.Equal(category.Name, result.Name);
            Assert.Equal(category.ParentId, result.ParentId);
            Assert.Equal(category.Rank, result.Rank);
        }

        [Fact]
        public void Should_Map_ProductBranch_To_ProductBranchResponse()
        {
            // Arrange
            var branch = ProductBranch.Create(1, 1, 100, null, false, null);

            // Act
            var result = _mapper.Map<ProductBranchResponse>(branch);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(branch.BranchId, result.BranchId);
            Assert.Equal(branch.OnHand, result.OnHand);
            Assert.Equal(branch.OnOrder, result.OnOrder);
            Assert.Equal(Money.Create(branch.Cost, "VND"), result.Cost);
        }

        [Fact]
        public void Should_Map_PriceBookDetail_To_PriceBookDetailResponse()
        {
            // Arrange
            var priceBook = PriceBookDetail.Create(1, 1, 75000m);

            // Act
            var result = _mapper.Map<PriceBookDetailResponse>(priceBook);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(priceBook.PriceBookId, result.PriceBookId);
            Assert.Equal(Money.Create(priceBook.Price, "VND"), result.Price);
        }

        [Fact]
        public void Should_Map_Product_To_CreateProductResponse()
        {
            // Arrange
            var product = Product.CreateProduct(
                "CF001",
                "Coffee",
                1,
                (byte)ProductTypes.Normal.Id,
                "cup",
                1f,
                "CF001"
            );

            product.SetDescription("Hot coffee");
            product.SetBasePrice(50000m);
            
            // Add a ProductTax to prevent null reference
            product.SetProductTax(ProductTax.Create(1, "VAT", 10.0m, 1));
            
            // Act
            var result = _mapper.Map<CreateProductResponse>(product);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(product.Code, result.Code);
            Assert.Equal(product.Name, result.Name);
            Assert.Equal(product.BasePrice, result.BasePrice);
            Assert.Empty(result.Images);
            Assert.Empty(result.ProductBranches);
            Assert.Empty(result.PriceBooks);
            Assert.Empty(result.ProductToppings);
            Assert.Equal(10.0f, result.Tax); // Verify tax is correctly mapped
        }

        [Fact]
        public void Should_Map_GroupProductResultDto_To_GetListProductDto()
        {
            // Arrange
            var groupProductResult = new GroupProductResultDto
            {
                Id = 1,
                Code = "P001",
                Name = "Test Product",
                FullName = "Test Product Full",
                BasePrice = 100000m,
                Cost = 80000m,
                OnHand = 50,
                CategoryId = 1,
                CategoryName = "Beverages",
                CategoryNameTree = "Food > Beverages",
                Unit = "cup",
                Image = "product1.jpg",
                IsSaleBranch = true,
                ProductType = 1,
                ProductGroup = 1,
                InventoryTrackingIgnore = false,
                IsRewardPoint = false,
                MasterProductId = 1,
                MasterUnitId = 1,
                MinQuantity = 1,
                MaxQuantity = 100,
                HasVariants = true,
                GroupProductType = 1,
                UnitList = "cup,box",
                ChildProductCount = 2,
                MasterCode = "M001",
                Rank = 1,
                Description = "Product description",
                AllowsSale = true,
                IsProcessedGoods = false,
                Reserved = 10,
                TaxName = "VAT 10%",
                Weight = 1.5
            };

            // Act
            var result = _mapper.Map<GetListProductDto>(groupProductResult);

            // Assert - only check the most important properties
            Assert.NotNull(result);
            Assert.Equal(groupProductResult.Id, result.Id);
            Assert.Equal(groupProductResult.Code, result.Code);
            Assert.Equal(groupProductResult.Name, result.Name);
            Assert.Equal(groupProductResult.FullName, result.FullName);
            Assert.Equal(groupProductResult.BasePrice, result.BasePrice);
            Assert.Equal(groupProductResult.Cost, result.Cost);
            Assert.Equal(groupProductResult.OnHand, result.OnHand);
            Assert.Equal(groupProductResult.CategoryId, result.CategoryId);
            Assert.Equal(groupProductResult.CategoryName, result.CategoryName);
            Assert.Equal(groupProductResult.CategoryNameTree, result.CategoryNameTree);
            Assert.Equal(groupProductResult.Unit, result.Unit);
            Assert.Equal(groupProductResult.Image.ToLower(), result.Image);
            Assert.Equal(groupProductResult.MasterCode, result.MasterCode);
            Assert.Equal(groupProductResult.Description, result.Description);
            Assert.Equal(groupProductResult.TaxName, result.TaxName);
            Assert.Equal(groupProductResult.Weight, result.Weight);
            
            // Verify that collection properties are initialized as empty collections
            Assert.NotNull(result.ProductAttributes);
            Assert.Empty(result.ProductAttributes);
            
            Assert.NotNull(result.UnitList);
            Assert.Empty(result.UnitList);
            
            Assert.Null(result.ProductFormulas);
            
            Assert.Null(result.ProductImages);
            
            Assert.NotNull(result.SameTypeProducts);
            Assert.Empty(result.SameTypeProducts);
        }

        [Theory]
        [InlineData("1,2,3,4", "1,2,3,4")]
        [InlineData("", "")]
        [InlineData(null, null)]
        [InlineData("1001,1002,1003", "1001,1002,1003")]
        [InlineData("1001,1002,1003,1004,1005,1006,1007,1008,1009,1010", "1001,1002,1003,1004,1005,1006,1007,1008,1009,1010")]
        public void Should_Map_ProductShelves_With_Various_Values(string? productShelves, string? expectedProductShelvesStr)
        {
            // Arrange
            var groupProductResult = new GroupProductResultDto
            {
                Id = 1,
                Code = "P001",
                Name = "Test Product",
                ProductShelves = productShelves ?? null!,
                // Add other required properties
                BasePrice = 100000m,
                CategoryId = 1
            };

            // Act
            var result = _mapper.Map<GetListProductDto>(groupProductResult);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProductShelvesStr, result.ProductShelvesStr);
        }

        [Fact]
        public void Should_Map_ProductShelves_To_ProductShelvesStr_While_Maintaining_Other_Properties()
        {
            // Arrange
            var productShelves = "101,102,103";
            var groupProductResult = new GroupProductResultDto
            {
                Id = 1,
                Code = "P001",
                Name = "Test Product",
                FullName = "Test Product Full",
                BasePrice = 100000m,
                Cost = 80000m,
                OnHand = 50,
                CategoryId = 1,
                CategoryName = "Beverages",
                CategoryNameTree = "Food > Beverages",
                Unit = "cup",
                Image = "product1.jpg",
                IsSaleBranch = true,
                ProductType = 1,
                ProductGroup = 1,
                ProductShelves = productShelves
            };

            // Act
            var result = _mapper.Map<GetListProductDto>(groupProductResult);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(productShelves, result.ProductShelvesStr);
            // Verify other important properties are also mapped correctly
            Assert.Equal(groupProductResult.Id, result.Id);
            Assert.Equal(groupProductResult.Code, result.Code);
            Assert.Equal(groupProductResult.Name, result.Name);
            Assert.Equal(groupProductResult.BasePrice, result.BasePrice);
            Assert.Equal(groupProductResult.CategoryId, result.CategoryId);
            Assert.Equal(groupProductResult.CategoryName, result.CategoryName);
        }

        [Fact]
        public void Should_Handle_ProductShelves_Empty_String_As_Null_Safe()
        {
            // Arrange - don't explicitly set ProductShelves
            var groupProductResult = new GroupProductResultDto
            {
                Id = 1,
                Code = "P001",
                Name = "Test Product",
                BasePrice = 100000m,
                CategoryId = 1
                // ProductShelves is not set - should default to empty string
            };

            // Act
            var result = _mapper.Map<GetListProductDto>(groupProductResult);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(string.Empty, result.ProductShelvesStr);
        }
    }
}