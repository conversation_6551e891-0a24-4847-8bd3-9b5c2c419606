<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="menu.product.category.name_can_not_be_null" xml:space="preserve">
    <value>Category name can't be null</value>
  </data>
  <data name="Test_Hello" xml:space="preserve">
    <value>Hello, World!</value>
  </data>
  <data name="Test_Greeting" xml:space="preserve">
    <value>Hello, {0}!</value>
  </data>
  <data name="man.cashflow.message.bank_account_exists" xml:space="preserve">
    <value>Bank account already exists</value>
  </data>
  <data name="man.cashflow.message.branch_ids_invalid" xml:space="preserve">
    <value>Some branch IDs do not belong to current retailer</value>
  </data>
  <data name="man.cashflow.message.bank_account_create_failed" xml:space="preserve">
    <value>Error occurred while creating bank account</value>
  </data>
  <data name="man.cashflow.message.account_name_too_long" xml:space="preserve">
    <value>Please enter account name no more than 255 characters.</value>
  </data>
  <data name="man.cashflow.message.account_required" xml:space="preserve">
    <value>Account is not allowed to be empty</value>
  </data>
  <data name="man.cashflow.message.bank_name_required" xml:space="preserve">
    <value>BankName is not allowed to be empty</value>
  </data>
  <data name="man.cashflow.message.bank_name_too_long" xml:space="preserve">
    <value>Please enter BankName no more than 255 characters.</value>
  </data>
  <data name="man.cashflow.message.bank_code_required" xml:space="preserve">
    <value>BankCode is not allowed to be empty</value>
  </data>
  <data name="man.cashflow.message.bank_code_too_long" xml:space="preserve">
    <value>Please enter BankCode no more than 50 characters.</value>
  </data>
  <data name="man.cashflow.message.branch_name_too_long" xml:space="preserve">
    <value>Please enter Branch no more than 255 characters.</value>
  </data>
  <data name="man.cashflow.message.description_too_long" xml:space="preserve">
    <value>Please enter Description no more than 255 characters.</value>
  </data>
  <data name="man.cashflow.message.bank_account_not_found" xml:space="preserve">
    <value>BankAccountNotFound</value>
  </data>
  <data name="man.cashflow.message.bank_account_deleted" xml:space="preserve">
    <value>BankAccountIsDeleted</value>
  </data>
  <data name="man.cashflow.message.bank_account_delete_failed" xml:space="preserve">
    <value>Error occurred while deleting bank account</value>
  </data>
  <data name="man.cashflow.message.bank_account_list_failed" xml:space="preserve">
    <value>Error occurred while getting bank account list</value>
  </data>
  <data name="man.cashflow.message.bank_account_update_failed" xml:space="preserve">
    <value>Error occurred while updating bank account</value>
  </data>
  <data name="man.cashflow.message.account_type_required" xml:space="preserve">
    <value>Please enter Type</value>
  </data>
  <data name="man.cashflow.message.wallet_delete_failed" xml:space="preserve">
    <value>Error occurred while deleting e-wallet</value>
  </data>
  <data name="man.cashflow.message.wallet_not_found" xml:space="preserve">
    <value>E-wallet with ID {request.Id} not found</value>
  </data>
  <data name="man.cashflow.message.wallet_list_failed" xml:space="preserve">
    <value>Error occurred while getting e-wallet list</value>
  </data>
  <data name="man.cashflow.message.wallet_account_exists" xml:space="preserve">
    <value>Account number {request.AccountNumber} already exists</value>
  </data>
  <data name="man.cashflow.message.wallet_update_failed" xml:space="preserve">
    <value>Error occurred while updating e-wallet</value>
  </data>
  <data name="man.product.validate.create.code.max_length_50" xml:space="preserve">
    <value>Product code must not exceed 50 characters</value>
  </data>
  <data name="man.product.validate.create.name.required" xml:space="preserve">
    <value>Product name is required</value>
  </data>
  <data name="man.product.validate.create.name.max_length_255" xml:space="preserve">
    <value>Product name must not exceed 255 characters</value>
  </data>
  <data name="man.product.validate.create.category_id__more_than_zero" xml:space="preserve">
    <value>Product group ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.create.description.max_length_2000" xml:space="preserve">
    <value>Description must not exceed 2000 characters</value>
  </data>
  <data name="man.product.validate.create.base_price.min" xml:space="preserve">
    <value>Selling price must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.tax_rate.range" xml:space="preserve">
    <value>Tax rate must be between 0 and 100</value>
  </data>
  <data name="man.product.validate.create.unit.max_length" xml:space="preserve">
    <value>Unit of measure must not exceed 50 characters</value>
  </data>
  <data name="man.product.validate.create.conversion_value.min" xml:space="preserve">
    <value>Conversion value must be greater than 0</value>
  </data>
  <data name="man.product.validate.create.min_quantity.min" xml:space="preserve">
    <value>Minimum quantity must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.max_quantity.min" xml:space="preserve">
    <value>Maximum quantity must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.max_quantity.gte_min_quantity" xml:space="preserve">
    <value>The maximum quantity must be greater than or equal to the minimum quantity.</value>
  </data>
  <data name="man.product.validate.create.cost.min" xml:space="preserve">
    <value>Cost price must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.on_hand_quantity.min" xml:space="preserve">
    <value>Inventory must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.pricebook_ids_more_than_zero" xml:space="preserve">
    <value>All price list IDs must be greater than 0</value>
  </data>
  <data name="man.product.validate.create.pricebook_prices.min" xml:space="preserve">
    <value>All prices must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.create.image_urls.not_empty" xml:space="preserve">
    <value>Image paths cannot be empty</value>
  </data>
  <data name="man.product.validate.create.product_type_id.positive_optional" xml:space="preserve">
    <value>The item type ID must be greater than 0 if specified.</value>
  </data>
  <data name="man.product.validate.create.product_group_id.positive_optional" xml:space="preserve">
    <value>Product group ID must be greater than 0 if specified</value>
  </data>
  <data name="man.product.validate.create.product_group_id.invalid" xml:space="preserve">
    <value>Invalid product group ID</value>
  </data>
  <data name="man.product.validate.create.tax_id.positive_optional" xml:space="preserve">
    <value>Tax ID must be greater than 0 if specified</value>
  </data>
  <data name="man.product.validate.delete.product_id.required" xml:space="preserve">
    <value>Product ID is required</value>
  </data>
  <data name="man.product.validate.delete.product_id__more_than_zero" xml:space="preserve">
    <value>Product ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.general.search_key_required" xml:space="preserve">
    <value>Search keywords are required</value>
  </data>
  <data name="man.product.validate.general.search_key_not_empty" xml:space="preserve">
    <value>Search keyword cannot be empty</value>
  </data>
  <data name="man.product.validate.general.limit_more_than_zero" xml:space="preserve">
    <value>Search limit must be greater than 0</value>
  </data>
  <data name="man.product.validate.delete.product_id_more_than_zero" xml:space="preserve">
    <value>Product ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.general.search_key.not_empty" xml:space="preserve">
    <value>Search keyword cannot be empty</value>
  </data>
  <data name="man.product.validate.general.limit.more_than_zero" xml:space="preserve">
    <value>Search limit must be greater than 0</value>
  </data>
  <data name="man.product.validate.update.product_id.required" xml:space="preserve">
    <value>Product ID is required</value>
  </data>
  <data name="man.product.validate.update.product_id_more_than_zero" xml:space="preserve">
    <value>Product ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.update.code.required" xml:space="preserve">
    <value>Product code is required</value>
  </data>
  <data name="man.product.validate.update.code.max_length" xml:space="preserve">
    <value>Product code must be less than 50 characters</value>
  </data>
  <data name="man.product.validate.update.name.required" xml:space="preserve">
    <value>Product name is required</value>
  </data>
  <data name="man.product.validate.update.name.max_length" xml:space="preserve">
    <value>Product name must be less than 255 characters</value>
  </data>
  <data name="man.product.validate.update.category_id_more_than_zero" xml:space="preserve">
    <value>Product group ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.update.base_price.min" xml:space="preserve">
    <value>Selling price must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.weight.min" xml:space="preserve">
    <value>Mass must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.product_group_id.positive_optional" xml:space="preserve">
    <value>Product group ID must be greater than 0 if specified</value>
  </data>
  <data name="man.product.validate.update.product_group_id.invalid" xml:space="preserve">
    <value>Invalid product group ID</value>
  </data>
  <data name="man.product.validate.update.tax_id_more_than_zero" xml:space="preserve">
    <value>Tax ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.update.order_template.max_length" xml:space="preserve">
    <value>Order form must be less than 500 characters</value>
  </data>
  <data name="man.product.validate.update.description.max_length" xml:space="preserve">
    <value>Description must be less than 2000 characters</value>
  </data>
  <data name="man.product.validate.update.images.max_count" xml:space="preserve">
    <value>Number of images must be less than 5</value>
  </data>
  <data name="man.product.validate.update.on_hand_quantity.min" xml:space="preserve">
    <value>Actual inventory must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.min_quantity.min" xml:space="preserve">
    <value>Minimum quantity must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.max_quantity.min" xml:space="preserve">
    <value>Maximum quantity must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.cost.min_on_hand" xml:space="preserve">
    <value>Cost price must be greater than or equal to 0</value>
  </data>
  <data name="man.product.validate.update.pricebooks.required" xml:space="preserve">
    <value>Price list is required</value>
  </data>
  <data name="man.product.validate.update.pricebooks.min_items" xml:space="preserve">
    <value>Must provide at least one price list</value>
  </data>
  <data name="man.product.validate.update.combo_group.name.required" xml:space="preserve">
    <value>Each combo group must have a name.</value>
  </data>
  <data name="man.product.validate.update.combo_group.name.max_length" xml:space="preserve">
    <value>Combo group name must be less than 100 characters</value>
  </data>
  <data name="man.product.validate.update.combo_group.items.min_items" xml:space="preserve">
    <value>Each combo group must have at least one item.</value>
  </data>
  <data name="man.product.validate.update.sale_branch.product_id.positive" xml:space="preserve">
    <value>Product ID must be greater than 0</value>
  </data>
  <data name="man.product.validate.update.toppings.required" xml:space="preserve">
    <value>Topping ID list is required</value>
  </data>
  <data name="man.product.validate.update.toppings.min_items" xml:space="preserve">
    <value>Must provide at least one topping product ID</value>
  </data>
  <data name="man.product.validate.update.request.not_empty" xml:space="preserve">
    <value>Request cannot be left blank</value>
  </data>
  <data name="man.product.validate.update.request.has_update_item" xml:space="preserve">
    <value>There must be at least one component to update.</value>
  </data>
  <data name="man.product.msg.create.validation_failed" xml:space="preserve">
    <value>Authentication failed</value>
  </data>
  <data name="man.product.msg.create.category_not_found" xml:space="preserve">
    <value>No product group found</value>
  </data>
  <data name="man.product.msg.create.create_failed" xml:space="preserve">
    <value>Create a failed product</value>
  </data>
  <data name="man.product.msg.create.combo_groups_required" xml:space="preserve">
    <value>Optional combo groups are required for optional combo products</value>
  </data>
  <data name="man.product.msg.create.combo_validation_failed" xml:space="preserve">
    <value>Combo option product validation failed</value>
  </data>
  <data name="man.product.msg.create.missing_product_ids" xml:space="preserve">
    <value>No product IDs found in optional combo groups</value>
  </data>
  <data name="man.product.msg.create.product_missing" xml:space="preserve">
    <value>Product with ID {} does not exist</value>
  </data>
  <data name="man.product.msg.create.product_deleted" xml:space="preserve">
    <value>Product with ID {} has been deleted</value>
  </data>
  <data name="man.product.msg.create.combo_mark_missing" xml:space="preserve">
    <value>The product with ID {} is a product combo but is not marked as a processed product.</value>
  </data>
  <data name="man.product.msg.create.service_invalid_combo" xml:space="preserve">
    <value>Product with ID {} is a time-based service so cannot be used in optional combos.</value>
  </data>
  <data name="man.product.msg.create.invalid_product_type" xml:space="preserve">
    <value>Product with ID {} has an invalid product type. Only regular products, processed combos, and non-timed services are allowed.</value>
  </data>
  <data name="man.product.msg.delete.product_not_found" xml:space="preserve">
    <value>No product found with ID {}</value>
  </data>
  <data name="man.product.msg.delete.product_deleted" xml:space="preserve">
    <value>Product has been deleted</value>
  </data>
  <data name="man.product.msg.general.product_detail_not_found" xml:space="preserve">
    <value>No product found with ID {}</value>
  </data>
  <data name="man.product.msg.general.internal_server_error" xml:space="preserve">
    <value>Internal server error</value>
  </data>
  <data name="man.product.msg.update.product_not_found" xml:space="preserve">
    <value>No products found</value>
  </data>
  <data name="man.product.msg.update.code_exists" xml:space="preserve">
    <value>Product code already exists</value>
  </data>
  <data name="man.product.msg.update.category_not_found" xml:space="preserve">
    <value>No product group found</value>
  </data>
  <data name="man.product.msg.update.tax_not_found" xml:space="preserve">
    <value>No tax found</value>
  </data>
  <data name="man.product.msg.update.product_not_found_images" xml:space="preserve">
    <value>No products found</value>
  </data>
  <data name="man.product.msg.update.branch_not_found" xml:space="preserve">
    <value>No branch found</value>
  </data>
  <data name="man.product.msg.update.product_not_found_quantity" xml:space="preserve">
    <value>No products found</value>
  </data>
  <data name="man.product.msg.update.deleted_product_no_update" xml:space="preserve">
    <value>Unable to update inventory for deleted product</value>
  </data>
  <data name="man.product.msg.update.service_combo_no_inventory_update" xml:space="preserve">
    <value>Unable to update inventory tracking and actual inventory for services and optional combos</value>
  </data>
  <data name="man.product.msg.update.no_tracking_no_inventory_update" xml:space="preserve">
    <value>Unable to update physical inventory for products without inventory tracking</value>
  </data>
  <data name="man.product.msg.update.avg_cost_no_update" xml:space="preserve">
    <value>Unable to update cost price for products with average cost price</value>
  </data>
  <data name="man.product.msg.update.product_not_found_pricebooks" xml:space="preserve">
    <value>No products found</value>
  </data>
  <data name="man.product.msg.update.product_deleted_pricebooks" xml:space="preserve">
    <value>Product has been deleted</value>
  </data>
  <data name="man.product.msg.update.pricebooks_not_found" xml:space="preserve">
    <value>Some price lists not found: {0}</value>
  </data>
  <data name="man.product.msg.update.pricebooks_deleted" xml:space="preserve">
    <value>Some price lists have been deleted: {0}</value>
  </data>
  <data name="man.product.msg.update.pricebooks_inactive" xml:space="preserve">
    <value>Some price lists are not working: {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_sale_branches" xml:space="preserve">
    <value>No product found with ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_combo" xml:space="preserve">
    <value>No products found with ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_combo" xml:space="preserve">
    <value>Product with ID {0} is not an optional combo product</value>
  </data>
  <data name="man.product.msg.update.product_not_in_tenant" xml:space="preserve">
    <value>Product with ID {0} is not managed by the current tenant</value>
  </data>
  <data name="man.product.msg.update.combo_groups_required_update" xml:space="preserve">
    <value>Must have at least one combo group</value>
  </data>
  <data name="man.product.msg.update.combo_group_not_found" xml:space="preserve">
    <value>Combo group with ID {0} not found</value>
  </data>
  <data name="man.product.msg.update.combo_item_not_found" xml:space="preserve">
    <value>No item found in combo group with ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_toppings" xml:space="preserve">
    <value>No products found</value>
  </data>
  <data name="man.product.msg.update.topping_product_not_found" xml:space="preserve">
    <value>No topping product found with ID {0}</value>
  </data>
  <data name="man.product.msg.update.invalid_topping" xml:space="preserve">
    <value>Products with ID {0} are not marked as topping</value>
  </data>
  <data name="man.product.validate.update.combo_groups.min_items" xml:space="preserve">
    <value>Must have at least one combo group</value>
  </data>
  <data name="man.product_note.add.message.group_name_exists" xml:space="preserve">
    <value>The note group name already exists.</value>
  </data>
  <data name="man.product_note.add.message.empty_group_name" xml:space="preserve">
    <value>Note group name cannot be left blank</value>
  </data>
  <data name="man.product_note.add.message.group_name_max_chars" xml:space="preserve">
    <value>Note group name cannot exceed {0} characters</value>
  </data>
  <data name="man.product_note.add.message.invalid_branch" xml:space="preserve">
    <value>Invalid branch</value>
  </data>
  <data name="man.product_note.delete.message.group_id_min_value" xml:space="preserve">
    <value>Note group ID must be greater than 0</value>
  </data>
  <data name="man.product_note.add.message.note_group_id_min_value" xml:space="preserve">
    <value>Note group ID must be greater than 0</value>
  </data>
  <data name="man.product_note.delete.message.group_not_found" xml:space="preserve">
    <value>Note group with ID {0} not found</value>
  </data>
  <data name="man.product_note.update.message.group_id_min_value" xml:space="preserve">
    <value>Note group ID must be greater than 0</value>
  </data>
  <data name="man.product_note.update.message.empty_group_name" xml:space="preserve">
    <value>Note group name cannot be left blank</value>
  </data>
  <data name="man.product_note.update.message.group_name_max_chars" xml:space="preserve">
    <value>Note group name cannot exceed {0} characters</value>
  </data>
  <data name="man.product_note.add.message.empty_note_name" xml:space="preserve">
    <value>The sample note name cannot be left blank.</value>
  </data>
  <data name="man.product_note.add.message.note_name_max_chars" xml:space="preserve">
    <value>Template note name cannot exceed {0} characters</value>
  </data>
  <data name="man.product_note.add.message.note_content_max_chars" xml:space="preserve">
    <value>The template note description must not exceed {0} characters.</value>
  </data>
  <data name="man.product_note.add.message.note_invalid_branch" xml:space="preserve">
    <value>Invalid branch</value>
  </data>
  <data name="man.product_note.delete.message.note_id_min_value" xml:space="preserve">
    <value>Item note ID must be greater than 0</value>
  </data>
  <data name="man.product_note.add.message.note_name_exists" xml:space="preserve">
    <value>The item note name already exists.</value>
  </data>
  <data name="man.product_note.add.message.note_group_not_exists" xml:space="preserve">
    <value>Note group does not exist</value>
  </data>
  <data name="man.product_note.add.message.product_not_found" xml:space="preserve">
    <value>Item with ID {0} was not found</value>
  </data>
  <data name="man.product_note.update.message.note_group_id_min_value" xml:space="preserve">
    <value>Note group ID must be greater than 0</value>
  </data>
  <data name="man.product_note.delete.message.note_not_found" xml:space="preserve">
    <value>Note that the item with ID {0} was not found.</value>
  </data>
  <data name="man.product_note.delete.message.note_already_deleted" xml:space="preserve">
    <value>Note that the item with ID {0} has been deleted</value>
  </data>
  <data name="man.product_note.update.message.note_id_min_value" xml:space="preserve">
    <value>Item note ID must be greater than 0</value>
  </data>
  <data name="man.product_note.update.message.empty_note_name" xml:space="preserve">
    <value>The item note name cannot be left blank.</value>
  </data>
  <data name="man.product_note.update.message.note_name_max_chars" xml:space="preserve">
    <value>The dish note name cannot exceed {0} characters.</value>
  </data>
  <data name="man.product_note.update.message.note_description_max_chars" xml:space="preserve">
    <value>Dish description notes must not exceed {0} characters</value>
  </data>
  <data name="man.product_note.update.message.product_id_min_value" xml:space="preserve">
    <value>All item IDs must be greater than 0</value>
  </data>
  <data name="man.product_note.update.message.product_not_found" xml:space="preserve">
    <value>Item with ID {0} was not found</value>
  </data>
  <data name="man.product_note.update.message.note_name_exists" xml:space="preserve">
    <value>The item note name already exists.</value>
  </data>
  <data name="man.delete_data.message_error.duplicate_date" xml:space="preserve">
    <value>Delete date {0} already exists</value>
  </data>
  <data name="man.product_note.add.message.note_group_required" xml:space="preserve">
    <value>Please select a note group</value>
  </data>
</root>