# Customizable Combo ProductType: Domain Model Design

## Document Information
| Document Type | Domain Model Design |
| ------------- | ------------------- |
| Version       | 1.2                 |
| Status        | Updated             |
| Author        | Development Team    |
| Last Updated  | April 18, 2024      |

## 1. Introduction

### 1.1 Purpose
This document describes the domain model design for implementing the new customizable combo product type in the KiotViet Product Management System. It defines the core domain entities, their relationships, behaviors, and business rules.

### 1.2 Scope
This design includes:
- Domain entity definitions
- Aggregate boundaries and relationships
- Business rules and invariants
- Domain events and commands
- Implementation considerations within the codebase

## 2. Domain Concepts

### 2.1 Key Concepts
- **Customizable Combo**: A combo product with a predefined structure that allows customer selection within component groups
- **Combo Group**: A logical grouping of potential component items within a combo (e.g., "Main Dish")
- **Group Item**: A product that can be selected within a group (e.g., "Grilled Chicken")
- **Selection Rules**: Constraints on how many items can be selected from each group
- **Additional Price**: Price adjustment for premium items within a group

### 2.2 Business Rules
- A customizable combo must have at least one group
- Each group has defined maximum selection quantities
- Component items may have additional prices
- The final price is calculated based on selections

## 3. Domain Model Entities

### 3.1 Product Types

The system defines product types using an Enumeration pattern:

```csharp
namespace KvFnB.Modules.Menu.Domain.Enums
{
    public class ProductTypes : Enumeration
    {
        public static readonly ProductTypes Combo = new(1, nameof(Combo).ToLowerInvariant(), "Combo - Đóng gói");
        public static readonly ProductTypes Normal = new(2, nameof(Normal).ToLowerInvariant(), "Hàng hóa");
        public static readonly ProductTypes Service = new(3, nameof(Service).ToLowerInvariant(), "Dịch vụ");
        public static readonly ProductTypes CustomizableCombo = new(4, nameof(CustomizableCombo).ToLowerInvariant(), "Combo tùy chỉnh");
        
        public static IEnumerable<ProductTypes> List() => [Combo, Normal, Service, CustomizableCombo];

        public static ProductTypes FromName(string name)
        {
            var state = List()
                .SingleOrDefault(s => string.Equals(s.Name, name, StringComparison.CurrentCultureIgnoreCase)) 
                ?? throw new ApplicationException($"Possible values for ProductType: {string.Join(",", List().Select(s => s.Name))}");
            return state;
        }

        public static ProductTypes From(byte id)
        {
            var state = List().SingleOrDefault(s => s.Id == id) 
                ?? throw new ApplicationException($"Possible values for ProductType: {string.Join(",", List().Select(s => s.Name))}");
            return state;
        }
    }
}
```

### 3.2 Core Entities

#### 3.2.1 CustomizableComboGroup
Represents a logical grouping of options within a combo.

```csharp
namespace KvFnB.Modules.Menu.Domain.Entities
{
    /// <summary>
    /// Represents a group of items within a customizable combo product
    /// </summary>
    public class CustomizableComboGroup : Entity<long>, ICreatedAt, IModifiedAt, ICreatedBy, IModifiedBy
    {
        /// <summary>
        /// The ID of the combo product this group belongs to
        /// </summary>
        public long ComboProductId { get; private set; }
        
        /// <summary>
        /// The name of the combo group
        /// </summary>
        public string Name { get; private set; } = string.Empty;
        
        /// <summary>
        /// Optional description of the combo group
        /// </summary>
        public string? Description { get; private set; }
        
        /// <summary>
        /// Maximum number of items that can be selected from this group
        /// </summary>
        public int? MaxQuantity { get; private set; }
        
        /// <summary>
        /// Sort order for display purposes
        /// </summary>
        public int SortOrder { get; private set; }
        
        /// <summary>
        /// When the group was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// When the group was last modified
        /// </summary>
        public DateTime? ModifiedAt { get; set; }

        /// <summary>
        /// The user ID that created this group
        /// </summary>
        public long CreatedBy { get; set; }
        
        /// <summary>
        /// The user ID that last modified this group
        /// </summary>
        public long? ModifiedBy { get; set; }
        
        /// <summary>
        /// The items in this combo group
        /// </summary>
        private readonly List<CustomizableComboGroupItem> _items = new List<CustomizableComboGroupItem>();
        public IReadOnlyCollection<CustomizableComboGroupItem> Items => _items.AsReadOnly();

        /// <summary>
        /// Creates a new combo group
        /// </summary>
        public static CustomizableComboGroup Create(
            long comboProductId,
            string name,
            string? description = null,
            int? maxQuantity = null,
            int sortOrder = 0)
        {
            return new CustomizableComboGroup
            {
                ComboProductId = comboProductId,
                Name = name,
                Description = description,
                MaxQuantity = maxQuantity,
                SortOrder = sortOrder,
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// Adds an item to this combo group
        /// </summary>
        public void AddItem(CustomizableComboGroupItem item)
        {
            _items.Add(item);
        }

        /// <summary>
        /// Removes an item from this combo group
        /// </summary>
        public void RemoveItem(long itemId)
        {
            var item = _items.FirstOrDefault(i => i.Id == itemId);
            if (item != null)
            {
                _items.Remove(item);
            }
        }
    }
}
```

The database mapping for CustomizableComboGroup is defined as:

```csharp
namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class CustomizableComboGroupEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomizableComboGroup>
    {
        public override void Configure(EntityTypeBuilder<CustomizableComboGroup> builder)
        {
            base.Configure(builder);
            builder.ToTable(nameof(CustomizableComboGroup));
            
            builder.HasKey(g => g.Id);
            
            builder.Property(g => g.Id).HasColumnType(SqlServerColumnTypes.BIGINT);
            builder.Property(g => g.ComboProductId).HasColumnType(SqlServerColumnTypes.BIGINT).IsRequired();
            builder.Property(g => g.Name).HasColumnType(SqlServerColumnTypes.NVARCHAR125).IsRequired();
            builder.Property(g => g.Description).HasColumnType(SqlServerColumnTypes.NVARCHAR2000);
            builder.Property(g => g.MaxQuantity).HasColumnType(SqlServerColumnTypes.INT);
            builder.Property(g => g.SortOrder).HasColumnType(SqlServerColumnTypes.INT);
            
            // Configure relation to combo group items
            builder.HasMany(g => g.Items)
                .WithOne()
                .HasForeignKey(i => i.GroupId)
                .OnDelete(DeleteBehavior.Cascade);
                
            // Configure relation to product
            builder.HasOne<Product>()
                .WithMany(p => p.ComboGroups)
                .HasForeignKey(g => g.ComboProductId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
```

#### 3.2.2 CustomizableComboGroupItem
Represents a product option within a combo group.

```csharp
namespace KvFnB.Modules.Menu.Domain.Entities
{
    /// <summary>
    /// Represents an item within a customizable combo group
    /// </summary>
    public class CustomizableComboGroupItem : Entity<long>, ICreatedAt, IModifiedAt, ICreatedBy, IModifiedBy
    {
        /// <summary>
        /// The ID of the group this item belongs to
        /// </summary>
        public long GroupId { get; private set; }
        
        /// <summary>
        /// The ID of the product this item represents
        /// </summary>
        public long ProductId { get; private set; }
        
        /// <summary>
        /// Additional price for this item
        /// </summary>
        public decimal AdditionalPrice { get; private set; }
        
        /// <summary>
        /// Sort order for display purposes
        /// </summary>
        public int SortOrder { get; private set; }
        
        /// <summary>
        /// When the item was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// When the item was last modified
        /// </summary>
        public DateTime? ModifiedAt { get; set; }
        
        /// <summary>
        /// The user ID that created this item
        /// </summary>
        public long CreatedBy { get; set; }
        
        /// <summary>
        /// The user ID that last modified this item
        /// </summary>
        public long? ModifiedBy { get; set; }

        private CustomizableComboGroupItem()
        {
        }

        /// <summary>
        /// Creates a new combo group item
        /// </summary>
        public static CustomizableComboGroupItem Create(
            long groupId,
            long productId,
            decimal additionalPrice = 0,
            int sortOrder = 0)
        {
            return new CustomizableComboGroupItem
            {
                GroupId = groupId,
                ProductId = productId,
                AdditionalPrice = additionalPrice,
                SortOrder = sortOrder,
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// Updates this combo group item's properties
        /// </summary>
        public void Update(
            decimal additionalPrice,
            int sortOrder)
        {
            AdditionalPrice = additionalPrice;
            SortOrder = sortOrder;
            ModifiedAt = DateTime.Now;
        }
    }
}
```

The database mapping for CustomizableComboGroupItem is defined as:

```csharp
namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class CustomizableComboGroupItemEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomizableComboGroupItem>
    {
        public override void Configure(EntityTypeBuilder<CustomizableComboGroupItem> builder)
        {
            base.Configure(builder);
            builder.ToTable(nameof(CustomizableComboGroupItem));
            
            builder.HasKey(i => i.Id);
            
            builder.Property(i => i.Id).HasColumnType(SqlServerColumnTypes.BIGINT);
            builder.Property(i => i.GroupId).HasColumnType(SqlServerColumnTypes.BIGINT);
            builder.Property(i => i.ProductId).HasColumnType(SqlServerColumnTypes.BIGINT);
            builder.Property(i => i.AdditionalPrice).HasColumnType(SqlServerColumnTypes.MONEY);
            builder.Property(i => i.SortOrder).HasColumnType(SqlServerColumnTypes.INT);
            
            // FK to CustomizableComboGroup
            builder.HasOne<CustomizableComboGroup>()
                .WithMany(g => g.Items)
                .HasForeignKey(i => i.GroupId)
                .OnDelete(DeleteBehavior.Cascade);
                
            // FK to Product (ComponentProduct)
            builder.HasOne<Product>()
                .WithMany()
                .HasForeignKey(i => i.ProductId)
                .OnDelete(DeleteBehavior.NoAction); // Using NoAction to avoid circular cascade deletes
        }
    }
}
```

#### 3.2.3 Product (Existing Entity with Extensions)
Extensions to the existing Product entity to support customizable combos.

```csharp
namespace KvFnB.Modules.Menu.Domain.Models
{
    public partial class Product
    {
        // Customizable combo extensions
        private readonly List<CustomizableComboGroup> _comboGroups = new List<CustomizableComboGroup>();
        public IReadOnlyCollection<CustomizableComboGroup> ComboGroups => _comboGroups.AsReadOnly();

        /// <summary>
        /// Adds a new combo group to this product
        /// </summary>
        public CustomizableComboGroup AddComboGroup(
            string name,
            string? description = null,
            int? maxQuantity = null,
            int sortOrder = 0)
        {
            var group = CustomizableComboGroup.Create(
                Id,
                name,
                description,
                maxQuantity,
                sortOrder);
                
            _comboGroups.Add(group);
            return group;
        }

        /// <summary>
        /// Gets a combo group by ID
        /// </summary>
        public CustomizableComboGroup? GetComboGroup(long groupId)
        {
            return ComboGroups.FirstOrDefault(g => g.Id == groupId);
        }

        /// <summary>
        /// Removes a combo group from this product
        /// </summary>
        public void RemoveComboGroup(long groupId)
        {
            var group = ComboGroups.FirstOrDefault(g => g.Id == groupId);
            if (group != null)
            {
                ComboGroups.Remove(group);
            }
        }

        /// <summary>
        /// Adds a new item to a combo group
        /// </summary>
        public CustomizableComboGroupItem AddComboGroupItem(
            long groupId,
            long productId,
            decimal additionalPrice = 0,
            int sortOrder = 0)
        {
            var group = GetComboGroup(groupId) ?? throw new DomainException($"Combo group with ID {groupId} not found");
            var item = CustomizableComboGroupItem.Create(
                groupId,
                productId,
                additionalPrice,
                sortOrder);
                
            group.AddItem(item);
            return item;
        }
    }
}
```

### 3.3 DTOs and Contract Models

#### 3.3.1 ProductComboDto

```csharp
namespace KvFnB.Modules.Menu.Application.Contracts
{
    /// <summary>
    /// Represents a product combo data transfer object
    /// </summary>
    public record ProductComboDto
    {
        /// <summary>
        /// The unique identifier of the product
        /// </summary>
        [JsonPropertyName("id"), Description("The unique identifier of the product.")]
        public long Id { get; init; }

        /// <summary>
        /// The product code
        /// </summary>
        [JsonPropertyName("code"), Description("The product code.")]
        public string Code { get; init; } = string.Empty;

        /// <summary>
        /// The product name
        /// </summary>
        [Required]
        [JsonPropertyName("name"), Description("The product name.")]
        public string Name { get; init; } = string.Empty;

        /// <summary>
        /// The product type (1 for combos)
        /// </summary>
        [JsonPropertyName("product_type"), Description("The product type (4 for customizable combos).")]
        public byte ProductType { get; init; } = 4;

        /// <summary>
        /// The base price of the combo
        /// </summary>
        [JsonPropertyName("base_price"), Description("The base price of the combo.")]
        public decimal BasePrice { get; init; }

        /// <summary>
        /// The retailer ID
        /// </summary>
        [JsonPropertyName("retailer_id"), Description("The retailer ID.")]
        public int RetailerId { get; init; }

        /// <summary>
        /// Collection of combo groups
        /// </summary>
        [JsonPropertyName("combo_groups"), Description("Collection of combo groups.")]
        public ICollection<CustomizableComboGroupDto> ComboGroups { get; init; } = new List<CustomizableComboGroupDto>();
    }
}
```

#### 3.3.2 CustomizableComboGroupDto

```csharp
namespace KvFnB.Modules.Menu.Application.Contracts
{
    /// <summary>
    /// Represents a customizable combo group data transfer object
    /// </summary>
    public record CustomizableComboGroupDto
    {
        /// <summary>
        /// The unique identifier of the combo group
        /// </summary>
        [JsonPropertyName("id"), Description("The unique identifier of the combo group.")]
        public long Id { get; init; }

        /// <summary>
        /// The product ID of the combo
        /// </summary>
        [JsonPropertyName("combo_product_id"), Description("The parent product ID of the combo.")]
        public long ComboProductId { get; init; }

        /// <summary>
        /// The name of the combo group
        /// </summary>
        [JsonPropertyName("name"), Description("The name of the combo group.")]
        public string Name { get; init; } = string.Empty;

        /// <summary>
        /// Optional description of the combo group
        /// </summary>
        [JsonPropertyName("description"), Description("Optional description of the combo group.")]
        public string? Description { get; init; }

        /// <summary>
        /// Maximum quantity of items that can be selected from this group
        /// </summary>
        [JsonPropertyName("max_quantity"), Description("Maximum quantity of items that can be selected from this group.")]
        public int? MaxQuantity { get; init; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [JsonPropertyName("sort_order"), Description("Sort order for display.")]
        public int SortOrder { get; init; }
        
        /// <summary>
        /// Items in this combo group
        /// </summary>
        [JsonPropertyName("items"), Description("Items in this combo group.")]
        public ICollection<CustomizableComboGroupItemDto> Items { get; init; } = new List<CustomizableComboGroupItemDto>();
    }
}
```

#### 3.3.3 CustomizableComboGroupItemDto

```csharp
namespace KvFnB.Modules.Menu.Application.Contracts
{
    /// <summary>
    /// Represents a customizable combo group item data transfer object
    /// </summary>
    public record CustomizableComboGroupItemDto
    {
        /// <summary>
        /// The unique identifier of the combo group item
        /// </summary>
        [JsonPropertyName("id"), Description("The unique identifier of the combo group item.")]
        public long Id { get; init; }

        /// <summary>
        /// The group ID this item belongs to
        /// </summary>
        [JsonPropertyName("group_id"), Description("The group ID this item belongs to.")]
        public long GroupId { get; init; }

        /// <summary>
        /// The product ID of the component item
        /// </summary>
        [JsonPropertyName("product_id"), Description("The product ID of the component item.")]
        public long ProductId { get; init; }
        
        /// <summary>
        /// The product info
        /// </summary>
        [JsonPropertyName("product"), Description("The product info.")]
        public ProductObjectDto Product { get; init; } = new ProductObjectDto();

        /// <summary>
        /// Additional price for this item
        /// </summary>
        [JsonPropertyName("additional_price"), Description("Additional price for this item.")]
        public decimal AdditionalPrice { get; init; }

        /// <summary>
        /// Sort order for display
        /// </summary>
        [JsonPropertyName("sort_order"), Description("Sort order for display.")]
        public int SortOrder { get; init; }

        public record ProductObjectDto
        {
            [JsonPropertyName("id"), Description("The unique identifier of the product.")]
            public long Id { get; init; }
            
            [JsonPropertyName("code"), Description("The product code.")]
            public string Code { get; init; } = string.Empty;
            
            [JsonPropertyName("name"), Description("The product name.")]
            public string Name { get; init; } = string.Empty;
        }
    }
}
```

## 4. API Request/Response Models

### 4.1 GetProductComboResponse

```csharp
namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductCombo
{
    /// <summary>
    /// Represents the response model for the GetProductCombo use case
    /// </summary>
    public record GetProductComboResponse
    {
        /// <summary>
        /// The product combo details
        /// </summary>
        [JsonPropertyName("product_combo")]
        public ProductComboDto ProductCombo { get; init; } = new ProductComboDto();
    }
}
```

### 4.2 UpdateProductComboRequest

```csharp
namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo
{
    /// <summary>
    /// Represents the request model for the UpdateProductCombo use case
    /// </summary>
    public record UpdateProductComboRequest
    {
        /// <summary>
        /// The unique identifier of the product combo to update
        /// </summary>
        [JsonIgnore]
        public long ProductId { get; init; }

        /// <summary>
        /// Collection of combo groups
        /// </summary>
        [JsonPropertyName("combo_groups"), Description("Collection of combo groups")]
        public ICollection<CustomizableComboGroupDto> ComboGroups { get; init; } = [];
    }
}
```

### 4.3 UpdateProductComboResponse

```csharp
namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo
{
    /// <summary>
    /// Represents the response model for the UpdateProductCombo use case
    /// </summary>
    public record UpdateProductComboResponse
    {
        /// <summary>
        /// The updated product combo
        /// </summary>
        [JsonPropertyName("product_combo")]
        public ProductComboDto ProductCombo { get; init; } = new ProductComboDto();
    }
}
```

## 5. Validation and Business Rules

### 5.1 Entity Validation

The following validation rules are implemented in the `UpdateProductComboUseCase`:

```csharp
private async Task<bool> ValidateComboGroupsAsync(
    long productId,
    IEnumerable<CustomizableComboGroupDto> groups,
    CancellationToken cancellationToken)
{
    // Check if groups are valid
    if (!ValidateGroupStructure(groups))
        return false;
    
    // Check component product types
    if (!await ValidateProductTypesAsync(groups, cancellationToken))
        return false;
        
    return true;
}

private async Task<bool> ValidateProductTypesAsync(
    IEnumerable<CustomizableComboGroupDto> groups,
    CancellationToken cancellationToken)
{
    var componentProductIds = groups
        .SelectMany(g => g.Items)
        .Select(i => i.ProductId)
        .Distinct()
        .ToList();

    var products = await _productRepository.GetProductsByIdsAsync(componentProductIds, cancellationToken);
    
    // Cannot use time-based service products in combos
    var serviceProducts = products.Where(p => p.ProductType == (byte)ProductTypes.Service.Id && p.IsTimeServices == true).ToList();
    if (serviceProducts.Count != 0)
    {
        var serviceNames = string.Join(", ", serviceProducts.Select(p => p.Name));
        ValidationErrors.Add($"Service time products cannot be used in combos: {serviceNames}");
        return false;
    }

    // Cannot use customizable combos in combos (prevent circular references)
    var customizableComboProducts = products.Where(p => p.ProductType == (byte)ProductTypes.CustomizableCombo.Id).ToList();
    if (customizableComboProducts.Count != 0)
    {
        var customizableComboNames = string.Join(", ", customizableComboProducts.Select(p => p.Name));
        ValidationErrors.Add($"Customizable combo products cannot be used in combos: {customizableComboNames}");
        return false;
    }

    // Cannot use regular combos without the IsProcessedGoods flag
    var comboProducts = products.Where(p => p.ProductType == (byte)ProductTypes.Combo.Id && p.IsProcessedGoods != true).ToList();
    if (comboProducts.Count != 0)
    {
        var comboNames = string.Join(", ", comboProducts.Select(p => p.Name));
        ValidationErrors.Add($"Combo products cannot be used in combos: {comboNames}");
        return false;
    }
    
    return true;
}

private bool ValidateGroupStructure(IEnumerable<CustomizableComboGroupDto> groups)
{
    foreach (var group in groups)
    {
        if (string.IsNullOrEmpty(group.Name))
        {
            ValidationErrors.Add("Group name is required");
            return false;
        }

        if (group.Name.Length > 100)
        {
            ValidationErrors.Add("Group name must be less than 100 characters");
            return false;
        }

        if (group.Items == null || group.Items.Count == 0)
        {
            ValidationErrors.Add("Group must have at least one item");
            return false;
        }
    }

    return true;
}
```

## 6. Repository Interfaces

### 6.1 ICustomizableComboGroupRepository

```csharp
namespace KvFnB.Modules.Menu.Domain.Repositories
{
    public interface ICustomizableComboGroupRepository : IBaseRepository<CustomizableComboGroup, long>
    {
        Task<List<CustomizableComboGroup>> GetByComboProductIdAsync(long comboProductId, CancellationToken cancellationToken = default);
    }
}
```

### 6.2 ICustomizableComboGroupItemRepository

```csharp
namespace KvFnB.Modules.Menu.Domain.Repositories
{
    public interface ICustomizableComboGroupItemRepository : IBaseRepository<CustomizableComboGroupItem, long>
    {
        Task<List<CustomizableComboGroupItem>> GetByGroupIdAsync(long groupId, CancellationToken cancellationToken = default);
    }
}
```

## 7. Repository Implementations

### 7.1 CustomizableComboGroupRepository

```csharp
namespace KvFnB.Modules.Menu.Infrastructure.Persistence
{
    public class CustomizableComboGroupRepository : BaseRepository<CustomizableComboGroup, long>, ICustomizableComboGroupRepository
    {
        private readonly ShardingDbContext _dbContext;

        public CustomizableComboGroupRepository(ShardingDbContext dbContext) : base(dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<CustomizableComboGroup>> GetByComboProductIdAsync(long comboProductId, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<CustomizableComboGroup>()
                .Where(cg => cg.ComboProductId == comboProductId)
                .OrderBy(cg => cg.SortOrder)
                .ToListAsync(cancellationToken);
        }
    }
}
```

### 7.2 CustomizableComboGroupItemRepository

```csharp
namespace KvFnB.Modules.Menu.Infrastructure.Persistence
{
    public class CustomizableComboGroupItemRepository : BaseRepository<CustomizableComboGroupItem, long>, ICustomizableComboGroupItemRepository
    {
        private readonly ShardingDbContext _dbContext;

        public CustomizableComboGroupItemRepository(ShardingDbContext dbContext) : base(dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<CustomizableComboGroupItem>> GetByGroupIdAsync(long groupId, CancellationToken cancellationToken = default)
        {
            return await _dbContext.Set<CustomizableComboGroupItem>()
                .Where(item => item.GroupId == groupId)
                .OrderBy(item => item.SortOrder)
                .ToListAsync(cancellationToken);
        }
    }
}
```

## 8. Conclusion

This domain model design provides a comprehensive foundation for implementing the customizable combo product type in the KiotViet system. The entities, repositories, and validation rules defined here encapsulate all the business logic required to manage customizable combos effectively.

The design follows the current codebase patterns and practices, ensuring compatibility with existing components while adding new functionality. By implementing this design, the system will be able to support rich customizable combo products that enhance the customer experience. 