using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using KvFnB.Modules.DataManagement.Domain.Enums;

namespace KvFnB.Modules.DataManagement.Application.Dtos
{
    /// <summary>
    /// Data Transfer Object for DeleteDataHistory entity
    /// </summary>
    public record DeleteDataHistoryDto
    {
        /// <summary>
        /// The unique identifier of the delete data history
        /// </summary>
        [JsonPropertyName("id"), Description("The unique identifier of the delete data history.")]
        public long Id { get; set; }
        
        /// <summary>
        /// The ID of the related delete data request
        /// </summary>
        [JsonPropertyName("request_id"), Description("The ID of the related delete data request.")]
        public long RequestId { get; set; }
        
        /// <summary>
        /// The status of the delete data operation
        /// </summary>
        [JsonPropertyName("status"), Description("The status of the delete data operation.")]
        public DeleteDataRequestStatus Status { get; set; }
        
        /// <summary>
        /// Description of the history event
        /// </summary>
        [JsonPropertyName("message"), Description("Description of the history event.")]
        public string? Message { get; set; }
        
        /// <summary>
        /// The date and time when this history record was created
        /// </summary>
        [JsonPropertyName("created_at"), Description("The date and time when this history record was created.")]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// The user who created this history record
        /// </summary>
        [JsonPropertyName("created_by"), Description("The user who created this history record.")]
        public long CreatedBy { get; set; }
    }
} 