using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Services;
using KvFnB.Modules.Menu.Domain.Services.Implements;

namespace KvFnB.Modules.Menu.Domain.Tests.Services
{
    public class ProductPriceBookDomainServiceTests
    {
        private readonly ProductPriceBookDomainService _productPriceBookDomainService;

        public ProductPriceBookDomainServiceTests()
        {
            _productPriceBookDomainService = new ProductPriceBookDomainService();
        }
        
        [Fact]
        public void UpdateProductPrices_ShouldAddNewPriceBookDetails()
        {
            // Arrange
            var product = CreateTestProduct();
            var priceBook = CreateTestPriceBook();
            var priceDetails = new List<ProductPriceDetail>
            {
                new() { PriceBookId = priceBook.Id, Price = 100.50m }
            };
            
            // Act
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(product, new List<PriceBook> { priceBook }, priceDetails).ToList();
            
            // Assert
            Assert.Single(updatedPriceBooks);
            var updatedPriceBook = updatedPriceBooks[0];
            Assert.Equal(priceBook.Id, updatedPriceBook.Id);
            Assert.True(updatedPriceBook.HasProductPrice(product.Id));
            var priceDetail = updatedPriceBook.GetProductPrice(product.Id);
            Assert.NotNull(priceDetail);
            Assert.Equal(100.50m, priceDetail.Price);
        }
        
        [Fact]
        public void UpdateProductPrices_ShouldUpdateExistingPriceBookDetails()
        {
            // Arrange
            var product = CreateTestProduct();
            var priceBook = CreateTestPriceBook();
            priceBook.AddProductPrice(product.Id, 50.25m);
            
            var priceDetails = new List<ProductPriceDetail>
            {
                new() { PriceBookId = priceBook.Id, Price = 75.00m }
            };
            
            // Act
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(product, new List<PriceBook> { priceBook }, priceDetails).ToList();
            
            // Assert
            Assert.Single(updatedPriceBooks);
            var updatedPriceBook = updatedPriceBooks[0];
            Assert.Equal(priceBook.Id, updatedPriceBook.Id);
            Assert.True(updatedPriceBook.HasProductPrice(product.Id));
            var priceDetail = updatedPriceBook.GetProductPrice(product.Id);
            Assert.NotNull(priceDetail);
            Assert.Equal(75.00m, priceDetail.Price);
        }
        
        [Fact]
        public void UpdateProductPrices_ShouldRemoveNonRequestedPriceBookDetails_WhenRemoveOthersIsTrue()
        {
            // Arrange
            var product = CreateTestProduct();
            var priceBook1 = CreateTestPriceBook(1);
            var priceBook2 = CreateTestPriceBook(2);
            
            priceBook1.AddProductPrice(product.Id, 50.00m);
            priceBook2.AddProductPrice(product.Id, 60.00m);
            
            // Only include priceBook1 in the request
            var priceDetails = new List<ProductPriceDetail>
            {
                new() { PriceBookId = priceBook1.Id, Price = 55.00m }
            };
            
            // Act
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(
                product, 
                new List<PriceBook> { priceBook1, priceBook2 }, 
                priceDetails,
                removeOthers: true).ToList();
            
            // Assert
            Assert.Equal(2, updatedPriceBooks.Count);
            
            var updatedPriceBook1 = updatedPriceBooks.First(pb => pb.Id == priceBook1.Id);
            Assert.True(updatedPriceBook1.HasProductPrice(product.Id));
            var priceDetail1 = updatedPriceBook1.GetProductPrice(product.Id);
            Assert.NotNull(priceDetail1);
            Assert.Equal(55.00m, priceDetail1.Price);
            
            var updatedPriceBook2 = updatedPriceBooks.First(pb => pb.Id == priceBook2.Id);
            Assert.False(updatedPriceBook2.HasProductPrice(product.Id));
        }
        
        [Fact]
        public void UpdateProductPrices_ShouldNotRemoveNonRequestedPriceBookDetails_WhenRemoveOthersIsFalse()
        {
            // Arrange
            var product = CreateTestProduct();
            var priceBook1 = CreateTestPriceBook(1);
            var priceBook2 = CreateTestPriceBook(2);
            
            priceBook1.AddProductPrice(product.Id, 50.00m);
            priceBook2.AddProductPrice(product.Id, 60.00m);
            
            // Only include priceBook1 in the request
            var priceDetails = new List<ProductPriceDetail>
            {
                new() { PriceBookId = priceBook1.Id, Price = 55.00m }
            };
            
            // Act
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(
                product, 
                new List<PriceBook> { priceBook1, priceBook2 }, 
                priceDetails,
                removeOthers: false).ToList();
            
            // Assert
            Assert.Single(updatedPriceBooks);
            
            var updatedPriceBook1 = updatedPriceBooks.First(pb => pb.Id == priceBook1.Id);
            Assert.True(updatedPriceBook1.HasProductPrice(product.Id));
            var priceDetail1 = updatedPriceBook1.GetProductPrice(product.Id);
            Assert.NotNull(priceDetail1);
            Assert.Equal(55.00m, priceDetail1.Price);
            
            // priceBook2 should not be in the updated list and should still have its price
            Assert.True(priceBook2.HasProductPrice(product.Id));
            var priceDetail2 = priceBook2.GetProductPrice(product.Id);
            Assert.NotNull(priceDetail2);
            Assert.Equal(60.00m, priceDetail2.Price);
        }
        
        private static Product CreateTestProduct(long id = 1)
        {
            var product = Product.CreateProduct(
                $"PROD-{id}",
                $"Product {id}",
                1,
                (byte)ProductTypes.Normal.Id,
                "Unit",
                1.0f,
                $"PROD-{id}");
            
            // Set the ID using reflection since it's a private setter in the entity base class
            typeof(Product).GetProperty("Id")?.SetValue(product, id);
            
            return product;
        }
        
        private static PriceBook CreateTestPriceBook(long id = 1)
        {
            var priceBook = PriceBook.Create(new CreatePriceBookModel
            {
                Name = $"Price Book {id}",
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            });
            
            // Set the ID using reflection since it's a private setter in the entity base class
            typeof(PriceBook).GetProperty("Id")?.SetValue(priceBook, id);
            
            return priceBook;
        }
    }
} 