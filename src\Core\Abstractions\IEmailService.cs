using System.Threading;
using System.Threading.Tasks;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for email sending operations
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Sends an email using a predefined template
        /// </summary>
        /// <param name="recipientEmail">Email address of the recipient</param>
        /// <param name="templateId">ID of the email template to use</param>
        /// <param name="templateData">Data to populate the template</param>
        /// <param name="fromEmail">Sender email address</param>
        /// <param name="fromName">Sender display name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendTemplateEmailAsync(
            string recipientEmail,
            string templateId,
            object templateData,
            string fromEmail,
            string fromName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends a simple email message
        /// </summary>
        /// <param name="recipientEmail">Email address of the recipient</param>
        /// <param name="subject">Email subject</param>
        /// <param name="htmlBody">HTML content of the email</param>
        /// <param name="textBody">Plain text content of the email</param>
        /// <param name="fromEmail">Sender email address</param>
        /// <param name="fromName">Sender display name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendEmailAsync(
            string recipientEmail,
            string subject,
            string htmlBody,
            string textBody,
            string fromEmail,
            string fromName,
            CancellationToken cancellationToken = default);
    }
} 