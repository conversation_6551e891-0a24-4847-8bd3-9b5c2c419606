using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;
using KvFnB.Shared.Localization;
using Moq;
using System.Globalization;

namespace KvFnB.Modules.Menu.Application.Tests.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks
{
    public class UpdateProductPriceBooksUseCaseTests
    {
        private readonly Mock<IValidator<UpdateProductPriceBooksRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
        private readonly Mock<IPriceBookDetailRepository> _priceBookDetailRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly UpdateProductPriceBooksUseCase _useCase;
        private readonly Mock<IProductPriceBookDomainService> _productPriceBookDomainServiceMock;
        private readonly ILocalizationProvider _multiLang;

        public UpdateProductPriceBooksUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<UpdateProductPriceBooksRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
            _priceBookDetailRepositoryMock = new Mock<IPriceBookDetailRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _productPriceBookDomainServiceMock = new Mock<IProductPriceBookDomainService>();

            // Create a fixed Vietnamese culture provider
            var fixedCultureProvider = new Mock<ICultureProvider>();
            fixedCultureProvider
                .Setup(c => c.GetCultureInfoAsync())
                .ReturnsAsync(new CultureInfo("vi-VN"));
            fixedCultureProvider
                .Setup(c => c.GetUserCultureAsync())
                .ReturnsAsync(new UserCulture { CultureCode = "vi-VN" });

            // Create a real localization provider with the fixed culture
            _multiLang = new LocalizationProvider(fixedCultureProvider.Object);

            _useCase = new UpdateProductPriceBooksUseCase(
                _validatorMock.Object,
                _multiLang,
                _productRepositoryMock.Object,
                _priceBookRepositoryMock.Object,
                _priceBookDetailRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _productPriceBookDomainServiceMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var request = new UpdateProductPriceBooksRequest
            {
                ProductId = 1,
                PriceBookDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>
                {
                    new() { PriceBookId = 10, Price = 100.00m, ProductId = 1 }
                }
            };

            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var product = CreateTestProduct(1);
            _productRepositoryMock.Setup(r => r.GetAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);

            var priceBook = CreateTestPriceBook(10);
            var existingPriceBooks = new List<PriceBook>();
            _priceBookRepositoryMock.Setup(r => r.GetForProductAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(existingPriceBooks);

            _priceBookRepositoryMock.Setup(r => r.GetByIdsAsync(It.IsAny<IEnumerable<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PriceBook> { priceBook });

            // Act
            var result = await _useCase.ExecuteAsync(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value?.Id);
            Assert.Equal(request.PriceBookDetails.Count, result.Value?.PriceBookDetails.Count());
            Assert.All(request.PriceBookDetails, requestDetail =>
            {
                var resultDetail = result.Value?.PriceBookDetails.FirstOrDefault(d => d.PriceBookId == requestDetail.PriceBookId);
                Assert.NotNull(resultDetail);
                Assert.Equal(requestDetail.Price, resultDetail.Price);
                Assert.Equal(requestDetail.ProductId, resultDetail.ProductId);
            });

            // Verify repository was called with a non-empty collection of price books
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(
                It.Is<IEnumerable<PriceBook>>(pbs => pbs.Any())), 
                Times.Once);
            
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WithInvalidRequest_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductPriceBooksRequest
            {
                ProductId = 1,
                PriceBookDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>()
            };

            var validationResult = new ValidationResult(false, ["PriceBookDetails is required"]);
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.NotNull(result.ValidationErrors);
            Assert.Contains("PriceBookDetails is required", result.ValidationErrors);

            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
            _priceBookRepositoryMock.Verify(r => r.GetForProductAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(It.IsAny<IEnumerable<PriceBook>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WithNonExistentProduct_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductPriceBooksRequest
            {
                ProductId = 999,
                PriceBookDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>
                {
                    new() { PriceBookId = 10, Price = 100.00m }
                }
            };

            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            _productRepositoryMock.Setup(r => r.GetAsync(999, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Product)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);

            _priceBookRepositoryMock.Verify(r => r.GetForProductAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(It.IsAny<IEnumerable<PriceBook>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WithNonExistentPriceBook_ShouldThrowException()
        {
            // Arrange
            var request = new UpdateProductPriceBooksRequest
            {
                ProductId = 1,
                PriceBookDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>
                {
                    new() { PriceBookId = 999, Price = 100.00m }
                }
            };

            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var product = CreateTestProduct(1);
            _productRepositoryMock.Setup(r => r.GetAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);

            var existingPriceBooks = new List<PriceBook>();
            _priceBookRepositoryMock.Setup(r => r.GetForProductAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(existingPriceBooks);

            // Return empty list, simulating price book not found
            _priceBookRepositoryMock.Setup(r => r.GetByIdsAsync(It.IsAny<IEnumerable<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PriceBook>());

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
            
            // Verify methods were not called after the exception
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(It.IsAny<IEnumerable<PriceBook>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldRemoveNonRequestedPriceBooks()
        {
            // Arrange
            var request = new UpdateProductPriceBooksRequest
            {
                ProductId = 1,
                PriceBookDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>
                {
                    new() { PriceBookId = 10, Price = 100.00m }
                }
            };

            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var product = CreateTestProduct(1);
            _productRepositoryMock.Setup(r => r.GetAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);

            var priceBook1 = CreateTestPriceBook(10);
            var priceBook2 = CreateTestPriceBook(20);
            priceBook1.AddProductPrice(1, 50.00m);
            priceBook2.AddProductPrice(1, 60.00m);
            
            var existingPriceBooks = new List<PriceBook> { priceBook2 };
            _priceBookRepositoryMock.Setup(r => r.GetForProductAsync(1, It.IsAny<CancellationToken>()))
                .ReturnsAsync(existingPriceBooks);

            _priceBookRepositoryMock.Setup(r => r.GetByIdsAsync(It.IsAny<IEnumerable<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PriceBook> { priceBook1 });

            // Act
            var result = await _useCase.ExecuteAsync(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            
            // Verify that we're updating multiple price books (should be 2)
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(
                It.Is<IEnumerable<PriceBook>>(pbs => pbs.Count() == 2)), 
                Times.Once);
            
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        private static Product CreateTestProduct(long id)
        {
            var product = Product.CreateProduct(
                $"PROD-{id}",
                $"Product {id}",
                1,
                (byte)ProductTypes.Normal.Id,
                "Unit",
                1.0f,
                $"PROD-{id}");
            
            // Set the ID using reflection since it's a private setter in the entity base class
            typeof(Product).GetProperty("Id")?.SetValue(product, id);
            
            return product;
        }
        
        private static PriceBook CreateTestPriceBook(long id)
        {
            var priceBook = PriceBook.Create(new CreatePriceBookModel
            {
                Name = $"Price Book {id}",
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            });
            
            // Set the ID using reflection since it's a private setter in the entity base class
            typeof(PriceBook).GetProperty("Id")?.SetValue(priceBook, id);
            
            return priceBook;
        }
    }
}