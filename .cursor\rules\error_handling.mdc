---
description: when review code request
globs: "**/*.cs"
alwaysApply: true
---

# Error Handling Guidelines

## Exception message handling in UseCases

Never directly expose exception messages in UseCase failure responses. Instead:

1. **DO NOT** use `Result<T>.Failure(ex.Message)` in any UseCase class.
2. **ALWAYS** use standardized error messages from `ErrorMessages` constants:
   ```csharp
   // WRONG ❌
   catch (Exception ex)
   {
       _logger.LogError(ex, "Error occurred");
       return Result<TResponse>.Failure(ex.Message); // Exposing exception details
   }
   
   // CORRECT ✅
   catch (Exception ex)
   {
       _logger.LogError(ex, "Error occurred");
       return Result<TResponse>.Failure(ErrorMessages.InternalServerError);
   }
   ```

3. **WHY**: Exposing exception details can:
   - Reveal sensitive implementation information
   - Create security vulnerabilities
   - Provide attackers with valuable system information
   - Create inconsistent user experience

4. For specific error scenarios (e.g., validation errors), define and use appropriate constants in `ErrorMessages` class.

Remember: Always log the full exception for debugging, but return standardized messages to clients. 