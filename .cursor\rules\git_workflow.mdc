---
description: 
globs: 
alwaysApply: false
---
 # Git Workflow Guidelines

## Branching Strategy

### Creating a New Feature Branch
1. Ensure you're on the main branch: `git checkout main`
2. Pull latest changes: `git pull origin main`
3. Create new feature branch: `git checkout -b feature/[feature-name]`
   - Use descriptive names following the pattern: `feature/[feature-name]`
   - Example: `feature/add-product-pricing`

## Commit Guidelines

### Commit Message Format
Follow this format for all commit messages:
```
[type]: [subject]

[optional body]

[optional footer]
```

### Commit Types
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Formatting changes (white-space, formatting, etc)
- `refactor`: Code change that neither fixes a bug nor adds a feature
- `test`: Adding or modifying tests
- `chore`: Changes to build process or auxiliary tools

### Commit Rules
1. Use imperative mood in the subject line
2. Limit the subject line to 50 characters
3. Capitalize the first letter of the subject
4. Do not end the subject line with a period
5. Separate subject from body with a blank line
6. Wrap the body at 72 characters
7. Use the body to explain what and why vs. how

### Examples
```
feat: Add price book support to product catalog

Implements the ability to assign multiple price books to a product
with different pricing strategies.

Closes #123
```

```
fix: Correct tax calculation for international orders

Tax was being incorrectly applied to exempt items in certain regions.
```

## Merging Process

### Merging Feature into Development
1. Ensure your feature branch is up to date: `git pull origin feature/[feature-name]`
2. Checkout development branch: `git checkout development`
3. Pull latest development changes: `git pull origin development`
4. Merge your feature branch: `git merge feature/[feature-name]`
5. Resolve any merge conflicts if necessary
6. Run tests to ensure everything works
7. Push to development: `git push origin development`

### Alternative: Pull Request Workflow
1. Push your feature branch to remote: `git push origin feature/[feature-name]`
2. Create a pull request from your feature branch to the development branch
3. Request code review from team members
4. Address any feedback and make necessary changes
5. Once approved, merge the pull request

## Best Practices
1. Commit early and often
2. Keep commits focused on a single logical change
3. Rebase feature branches on main before merging
4. Delete branches after they've been merged
5. Never force push to shared branches (main, development)
6. Always run tests before pushing