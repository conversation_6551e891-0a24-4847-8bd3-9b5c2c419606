using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity
{
    /// <summary>
    /// Validates the UpdateProductOnHandQuantity request
    /// </summary>
    public class UpdateProductOnHandQuantityValidator : Validator<UpdateProductOnHandQuantityRequest>
    {
        public UpdateProductOnHandQuantityValidator(ILocalizationProvider multiLang)
        {
            // Rule for Id: Must not be null and greater than 0
            RuleFor(x => x.ProductId)
                .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_required))
                .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_more_than_zero));

            // Rule for OnHandQuantity: Must be greater than or equal to 0
            RuleFor(x => x.OnHandQuantity)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_on_hand_quantity_min));

            // Rule for MinQuantity: Must be greater than or equal to 0
            RuleFor(x => x.MinQuantity)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_min_quantity_min));

            // Rule for MaxQuantity: Must be greater than or equal to 0
            RuleFor(x => x.MaxQuantity)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_max_quantity_min));

            // Rule for Cost: Must be greater than or equal to 0
            RuleFor(x => x.Cost)
                .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_cost_min_on_hand));
        }
    }
}