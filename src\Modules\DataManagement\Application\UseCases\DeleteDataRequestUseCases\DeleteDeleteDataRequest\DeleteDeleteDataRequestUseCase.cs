
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.DeleteDeleteDataRequest
{
    public class DeleteDeleteDataRequestUseCase : UseCaseBase<DeleteDeleteDataRequestRequest, DeleteDataRequestResponse>
    {
        private readonly IValidator<DeleteDeleteDataRequestRequest> _validator;
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ITenantProvider _tenantProvider;
        private readonly IAuthUser _authUser;
        private readonly IUserService _userService;
        public DeleteDeleteDataRequestUseCase(
            IValidator<DeleteDeleteDataRequestRequest> validator,
            IDeleteDataRequestRepository deleteDataRequestRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ITenantProvider tenantProvider,
            IAuthUser authUser,
            IUserService userService
            )
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        }

        public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
            DeleteDeleteDataRequestRequest request,
            CancellationToken cancellationToken = default)
        {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeleteDataRequestResponse>.Failure(validationResult.Errors);
                }

                var userId = _authUser.Id;
                var tenantId = _tenantProvider.GetTenantId() ?? 0;
                // Step 1: Verify password against User DB
                var passwordVerified = await _userService.VerifyUserPasswordAsync(
                    userId, request.Password, cancellationToken);

                if (!passwordVerified)
                {
                    return Result<DeleteDataRequestResponse>.Failure("Invalid password");
                }

                // Get the existing request
                var deleteDataRequest = await _deleteDataRequestRepository.GetAsync(request.Id, cancellationToken);
                if (deleteDataRequest == null || deleteDataRequest.TenantId != tenantId)
                {
                    return Result<DeleteDataRequestResponse>.Failure("Delete data request not found");
                }
                // Delete the request
                deleteDataRequest.UpdateStatus(Domain.Enums.DeleteDataRequestStatus.Cancelled);
                await _unitOfWork.CommitAsync(cancellationToken);

                // Map to response and return
                var response = _mapper.Map<DeleteDataRequestResponse>(deleteDataRequest);

                return Result<DeleteDataRequestResponse>.Success(response);
        }
    }
} 