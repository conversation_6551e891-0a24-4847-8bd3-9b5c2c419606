using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services.Implements;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks
{
    /// <summary>
    /// Implements the UpdateProductPriceBooks use case
    /// </summary>
    public class UpdateProductPriceBooksUseCase
    {
        private readonly ILocalizationProvider _multiLang;
        private readonly IValidator<UpdateProductPriceBooksRequest> _validator;
        private readonly IProductRepository _productRepository;
        private readonly IPriceBookRepository _priceBookRepository;
        private readonly IPriceBookDetailRepository _priceBookDetailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IProductPriceBookDomainService _productPriceBookDomainService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductPriceBooksUseCase"/> class.
        /// </summary>
#pragma warning disable S107 // Methods should not have too many parameters
        public UpdateProductPriceBooksUseCase(
            IValidator<UpdateProductPriceBooksRequest> validator,
            ILocalizationProvider multiLang,
            IProductRepository productRepository,
            IPriceBookRepository priceBookRepository,
            IPriceBookDetailRepository priceBookDetailRepository,
            IUnitOfWork unitOfWork,
            IProductPriceBookDomainService productPriceBookDomainService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _priceBookRepository = priceBookRepository ?? throw new ArgumentNullException(nameof(priceBookRepository));
            _priceBookDetailRepository = priceBookDetailRepository ?? throw new ArgumentNullException(nameof(priceBookDetailRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _productPriceBookDomainService = productPriceBookDomainService ?? throw new ArgumentNullException(nameof(productPriceBookDomainService));
        }
        #pragma warning restore S107 // Methods should not have too many parameters

        /// <summary>
        /// Executes the update product price books use case.
        /// </summary>
        /// <param name="request">The request containing the product price books to update.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the updated product price books or error information.</returns>
        public async Task<Result<UpdateProductPriceBooksResponse>> ExecuteAsync(
            UpdateProductPriceBooksRequest request,
            CancellationToken cancellationToken = default)
        {
           // 1. Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return Result<UpdateProductPriceBooksResponse>.Failure(validationResult.Errors);
            }

            // 2. Get and validate product
            var productResult = await GetAndValidateProductAsync(request.ProductId, cancellationToken);
            if (!productResult.IsSuccess || productResult.Value == null)
            {
                return Result<UpdateProductPriceBooksResponse>.Failure(productResult.ErrorMessage ?? _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            }

            // 3. Get and validate price books
            var priceBookResult = await GetAndValidatePriceBooksAsync(productResult.Value.Id, request.PriceBookDetails, cancellationToken);
            if (!priceBookResult.IsSuccess || priceBookResult.Value == null)
            {
                return Result<UpdateProductPriceBooksResponse>.Failure(priceBookResult.ErrorMessage ?? _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_not_found));
            }

            // 4. Update price books
            var priceDetails = MapPriceDetails(request.PriceBookDetails);
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(
                productResult.Value,
                priceBookResult.Value,
                priceDetails,
                removeOthers: true);

            // 5. Save changes
            await SaveChangesAsync([.. updatedPriceBooks], cancellationToken);

            // 6. Create response
            var responseDetails = new List<UpdateProductPriceBooksRequest.PriceBookDetailRequest>();

            // Fetch the actual price book detail records to get their IDs
            foreach (var requestDetail in request.PriceBookDetails)
            {
                var priceBookDetail = await _priceBookDetailRepository.GetByProductIdAndPriceBookIdAsync(
                    productResult.Value.Id,
                    requestDetail.PriceBookId,
                    cancellationToken);

                if (priceBookDetail != null)
                {
                    responseDetails.Add(new UpdateProductPriceBooksRequest.PriceBookDetailRequest
                    {
                        Id = priceBookDetail.Id,
                        ProductId = productResult.Value.Id,
                        PriceBookId = requestDetail.PriceBookId,
                        Price = requestDetail.Price
                    });
                }
                else
                {
                    // If for some reason the detail wasn't found (unlikely), use the request data
                    responseDetails.Add(new UpdateProductPriceBooksRequest.PriceBookDetailRequest
                    {
                        Id = 0, // This should not happen if saving was successful
                        ProductId = productResult.Value.Id,
                        PriceBookId = requestDetail.PriceBookId,
                        Price = requestDetail.Price
                    });
                }
            }

            var response = new UpdateProductPriceBooksResponse
            {
                Id = productResult.Value.Id,
                PriceBookDetails = responseDetails,
            };

            return Result<UpdateProductPriceBooksResponse>.Success(response);
        }

        private ValidationResult ValidateRequest(UpdateProductPriceBooksRequest request)
        {
            return _validator.Validate(request);
        }

        private async Task<Result<Product>> GetAndValidateProductAsync(long productId, CancellationToken cancellationToken)
        {
            var product = await _productRepository.GetAsync(productId, cancellationToken);
            if (product == null)
            {
                return Result<Product>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            }

            if (product.IsDeleted.GetValueOrDefault())
            {
                return Result<Product>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_delete_product_deleted));
            }

            return Result<Product>.Success(product);
        }

        private async Task<Result<List<PriceBook>>> GetAndValidatePriceBooksAsync(
            long productId, 
            List<UpdateProductPriceBooksRequest.PriceBookDetailRequest> requestedPriceBooks,
            CancellationToken cancellationToken)
        {
            // Get existing price books for the product
            var allProductPriceBooks = await _priceBookRepository.GetForProductAsync(productId, cancellationToken);
            
            // Load additional price books mentioned in the request
            var requestedPriceBookIds = requestedPriceBooks.Select(pb => pb.PriceBookId).Distinct().ToList();
            var loadedPriceBookIds = allProductPriceBooks.Select(pb => pb.Id).ToHashSet();
            var missingPriceBookIds = requestedPriceBookIds.Where(id => !loadedPriceBookIds.Contains(id)).ToList();
            
            if (missingPriceBookIds.Count != 0)
            {
                var additionalPriceBooks = await _priceBookRepository.GetByIdsAsync(missingPriceBookIds, cancellationToken);
                allProductPriceBooks.AddRange(additionalPriceBooks);
            }
            
            // Validate all requested price books were found
            var notFoundPriceBookIds = requestedPriceBookIds.Where(id => !requestedPriceBookIds.Contains(id)).ToList();
            
            if (notFoundPriceBookIds.Count != 0)
            {
                return Result<List<PriceBook>>.Failure(
                    _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_not_found, string.Join(", ", notFoundPriceBookIds)));
            }

            // Check for deleted price books
            var deletedPriceBooks = allProductPriceBooks.Where(x => requestedPriceBookIds.Contains(x.Id))
                .Where(pb => pb.IsDeleted.GetValueOrDefault())
                .ToList();
            
            if (deletedPriceBooks.Count != 0)
            {
                var deletedPriceBookIds = deletedPriceBooks.Select(pb => pb.Id).ToList();
                return Result<List<PriceBook>>.Failure(
                    _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_deleted, string.Join(", ", deletedPriceBookIds)));
            }
            
            // Check for inactive price books
            var inactivePriceBooks = allProductPriceBooks.Where(x => requestedPriceBookIds.Contains(x.Id))
                .Where(pb => !pb.IsActive)
                .ToList();
            
            if (inactivePriceBooks.Count != 0)
            {
                var inactivePriceBookIds = inactivePriceBooks.Select(pb => pb.Id).ToList();
                return Result<List<PriceBook>>.Failure(
                    _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_inactive, string.Join(", ", inactivePriceBookIds)));
            }

            return Result<List<PriceBook>>.Success(allProductPriceBooks);
        }

        private static List<ProductPriceDetail> MapPriceDetails(List<UpdateProductPriceBooksRequest.PriceBookDetailRequest> priceBookDetails)
        {
            return [.. priceBookDetails.Select(pb => new ProductPriceDetail
            {
                PriceBookId = pb.PriceBookId,
                Price = pb.Price
            })];
        }

        private async Task SaveChangesAsync(List<PriceBook> updatedPriceBooks, CancellationToken cancellationToken)
        {
            _priceBookRepository.UpdateRangeAsync(updatedPriceBooks);
            await _unitOfWork.CommitAsync(cancellationToken);
        }
    }
} 