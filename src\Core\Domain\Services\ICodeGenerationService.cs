namespace KvFnB.Core.Domain.Services
{
    /// <summary>
    /// Interface for generating sequential codes for entities (Products, Invoices, Customers, etc.)
    /// </summary>
    public interface ICodeGenerationService
    {
        /// <summary>
        /// Generates a new code for a specific entity type with a given prefix
        /// </summary>
        /// <param name="tenantId">The tenant ID (merchant identifier)</param>
        /// <param name="typeOf">The entity type (e.g., "Product", "Invoice")</param>
        /// <param name="prefix">The fixed code prefix (e.g., "SP" for products)</param>
        /// <param name="customCode">Optional custom code provided by user</param>
        /// <param name="minPadding">Minimum padding for the numeric part (default: 6)</param>
        /// <returns>The newly generated code</returns>
        Task<string> GenerateCodeAsync(
            int tenantId, 
            string typeOf, 
            string prefix, 
            string? customCode = null, 
            int minPadding = 6);

        /// <summary>
        /// Generates a batch of sequential codes atomically
        /// </summary>
        /// <param name="tenantId">The tenant ID (merchant identifier)</param>
        /// <param name="typeOf">The entity type (e.g., "Product", "Invoice")</param>
        /// <param name="prefix">The fixed code prefix (e.g., "SP" for products)</param>
        /// <param name="batchSize">Number of codes to generate</param>
        /// <param name="minPadding">Minimum padding for the numeric part (default: 6)</param>
        /// <returns>A list of sequentially generated codes</returns>
        Task<IReadOnlyList<string>> GenerateBatchCodesAsync(
            int tenantId, 
            string typeOf, 
            string prefix, 
            int batchSize, 
            int minPadding = 6);

        /// <summary>
        /// Executes an operation within a transaction that generates a code and creates an entity
        /// </summary>
        /// <typeparam name="T">The entity type that implements ICode</typeparam>
        /// <param name="tenantId">The tenant ID (merchant identifier)</param>
        /// <param name="entityTypeOf">The entity type (e.g., "Product", "Invoice")</param>
        /// <param name="prefix">The fixed code prefix (e.g., "SP" for products)</param>
        /// <param name="createEntityFunc">Function to create the entity using the generated code</param>
        /// <param name="customCode">Optional custom code provided by user</param>
        /// <param name="minPadding">Minimum padding for the numeric part (default: 6)</param>
        /// <returns>The created entity with generated code</returns>
        Task<T> GenerateCodeAndCreateEntityAsync<T>(
            int tenantId, 
            string entityTypeOf, 
            string prefix, 
            Func<string, Task<T>> createEntityFunc, 
            string? customCode = null, 
            int minPadding = 6) where T : class, ICode;

        /// <summary>
        /// Updates an entity's code and executes an update function within a single transaction
        /// </summary>
        /// <typeparam name="T">The entity type that implements ICode</typeparam>
        /// <param name="tenantId">The tenant ID (merchant identifier)</param>
        /// <param name="entityTypeOf">The entity type (e.g., "Product", "Invoice")</param>
        /// <param name="prefix">The fixed code prefix (e.g., "SP" for products)</param>
        /// <param name="newCode">The new code to assign to the entity</param>
        /// <param name="updateEntityFunc">Function to update the entity using the new code</param>
        /// <param name="minPadding">Minimum padding for the numeric part (default: 6)</param>
        /// <returns>The updated entity with the new code</returns>
        Task<T> UpdateEntityCodeAsync<T>(
            int tenantId, 
            string entityTypeOf, 
            string prefix, 
            string newCode,
            Func<string, Task<T>> updateEntityFunc,
            int minPadding = 6) where T : class, ICode;

        /// <summary>
        /// Generates a deletion code for an entity by appending a deletion marker
        /// </summary>
        /// <typeparam name="T">The entity type that implements ICode and ITenantEntity</typeparam>
        /// <param name="tenantId">The tenant ID (merchant identifier)</param>
        /// <param name="currentCode">The current code of the entity being deleted</param>
        /// <param name="entityTypeOf">The entity type (e.g., "Product", "Invoice")</param>
        /// <returns>A deletion code with incremental marker</returns>
        Task<string> GenerateDeletionCodeAsync<T>(
            int tenantId,
            string currentCode,
            string entityTypeOf) where T : class, ICode, ITenantEntity;
    }
} 