
namespace KvFnB.Core.Contracts
{
    public class Result<T>
    {
        public bool IsSuccess { get; }
        public T? Value { get; }

        public string? ErrorMessage { get; }

        public List<string>? ValidationErrors { get; }

        private Result(bool isSuccess, T? value, string? errorMessage, List<string>? validationErrors = null)
        {
            IsSuccess = isSuccess;
            Value = value;
            ErrorMessage = errorMessage;
            ValidationErrors = validationErrors;
        }

        public static Result<T> Success(T value) => new(true, value, null);

        public static Result<T> Failure(string errorMessage) => new(false, default, errorMessage);

        public static Result<T> Failure(List<string> validationErrors) => new(false, default, validationErrors.FirstOrDefault() ?? "Validation Error", validationErrors);

    }

    public class ValidationError
    {
        public string Field { get; }
        public string Message { get; }

        public ValidationError(string field, string message)
        {
            Field = field;
            Message = message;
        }
    }
}