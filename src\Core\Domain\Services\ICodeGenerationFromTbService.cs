
namespace KvFnB.Core.Domain.Services
{
    /// <summary>
    /// Defines methods for generating unique sequential codes for business entities
    /// </summary>
    public interface ICodeGenerationFromTbService
    {
        /// <summary>
        /// Generates the next sequential code using the specified prefix and padding
        /// </summary>
        /// <param name="prefix">The prefix to use for the code (e.g., "INV-")</param>
        /// <param name="previousCode">The previous code in the sequence (optional)</param>
        /// <param name="padding">The number of digits to pad with leading zeros</param>
        /// <returns>The next sequential code</returns>
        string GenerateNextCode(string prefix, string? previousCode, int padding);

        /// <summary>
        /// Asynchronously generates the next sequential code for an entity type
        /// </summary>
        /// <param name="entityType">The type of entity for which to generate the code</param>
        /// <param name="prefix">The prefix to use for the code</param>
        /// <param name="padding">The number of digits to pad with leading zeros</param>
        /// <returns>The next sequential code</returns>
        Task<string> GenerateNextCodeAsync(Type entityType, string prefix, int padding);

        /// <summary>
        /// Asynchronously checks if a code is unique and generates an alternative if it's not
        /// </summary>
        /// <param name="entityType">The type of entity to check against</param>
        /// <param name="suggestedCode">The code to check for uniqueness</param>
        /// <param name="prefix">The prefix to use if a new code needs to be generated</param>
        /// <returns>The original code if unique, or a new unique code otherwise</returns>
        Task<string> EnsureUniqueCodeAsync(Type entityType, string suggestedCode, string prefix);
    }
} 