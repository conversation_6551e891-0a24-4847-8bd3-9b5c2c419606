using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.CreateNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.DeleteNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetListNoteTemplateByGroup;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateBasicInfoNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateProductNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetListProductByNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetAllProductByNoteTemplate;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NoteTemplateController : BaseController
    {
        public NoteTemplateController(IHttpContextAccessor httpContextAccessor) 
            : base(httpContextAccessor)
        {}

        /// <summary>
        /// Creates a new note template.
        /// </summary>
        /// <param name="request">The request object containing the details of the note template to create.</param>
        /// <returns>A response indicating the result of the create operation.</returns>
        /// <response code="200">Returns the newly created note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new note template", Description = "Creates a new note template with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the newly created note template", typeof(CreateNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> CreateNoteTemplate([FromBody] CreateNoteTemplateRequest request)
        {
            var createUseCase = HttpContext.RequestServices.GetRequiredService<CreateNoteTemplateUseCase>();
            var result = await createUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Updates the basic information of an existing note template.
        /// </summary>
        /// <param name="id">The ID of the note template to update.</param>
        /// <param name="request">The request object containing the updated details of the note template.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}/basic-info")]
        [SwaggerOperation(Summary = "Updates an existing note template", Description = "Updates an existing note template with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated note template", typeof(UpdateBasicInfoNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateBasicInfoNoteTemplate(long id, [FromBody] UpdateBasicInfoNoteTemplateRequest request)
        {
            request.Id = id;
            var updateBasicInfoUseCase = HttpContext.RequestServices.GetRequiredService<UpdateBasicInfoNoteTemplateUseCase>();
            var result = await updateBasicInfoUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Updates the product note template associations for a note template.
        /// </summary>
        /// <param name="id">The ID of the note template to update.</param>
        /// <param name="request">The request object containing the updated product note template associations.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated product note template associations.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}/product")]
        [SwaggerOperation(Summary = "Updates the product note template associations for a note template", Description = "Updates the product note template associations for a note template with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated product note template associations", typeof(UpdateProductNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateProductNoteTemplate(long id, [FromBody] UpdateProductNoteTemplateRequest request)
        {
            request.NoteTemplateId = id;
            var updateProductNoteTemplateUseCase = HttpContext.RequestServices.GetRequiredService<UpdateProductNoteTemplateUseCase>();
            var result = await updateProductNoteTemplateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Deletes an existing note template.
        /// </summary>
        /// <param name="id">The ID of the note template to delete.</param>
        /// <returns>A response indicating the result of the delete operation.</returns>
        /// <response code="200">Returns the deleted note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes an existing note template", Description = "Deletes an existing note template by ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the deleted note template", typeof(DeleteNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> DeleteNoteTemplate(long id)
        {
            var request = new DeleteNoteTemplateRequest { Id = id };
            var deleteNoteTemplateUseCase = HttpContext.RequestServices.GetRequiredService<DeleteNoteTemplateUseCase>();
            var result = await deleteNoteTemplateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Gets a list of note templates by group.
        /// </summary>
        /// <param name="request">The request object containing the group ID and filtering/paging parameters.</param>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the list of note templates.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("by-group")]
        [SwaggerOperation(Summary = "Gets a list of note templates by group", Description = "Gets a list of note templates filtered by group ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of note templates", typeof(PagingResponse<GetListNoteTemplateByGroupResponseItem>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetNoteTemplatesByGroup([FromQuery] GetListNoteTemplateByGroupRequest request)
        {
            var getListByGroupUseCase = HttpContext.RequestServices.GetRequiredService<GetListNoteTemplateByGroupUseCase>();
            var result = await getListByGroupUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Gets a list of products by note template.
        /// </summary>
        /// <param name="request">The request object containing the note template ID and filtering/paging parameters.</param>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the list of products.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("product/get-all")]
        [SwaggerOperation(Summary = "Gets a list of products by note template", Description = "Gets a list of products filtered by note template ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of products", typeof(PagingResponse<GetAllProductByNoteTemplateResponseItem>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetAllProductsByNoteTemplate([FromQuery] GetAllProductByNoteTemplateRequest request)
        {
            var getAllProductByNoteTemplateUseCase = HttpContext.RequestServices.GetRequiredService<GetAllProductByNoteTemplateUseCase>();
            var result = await getAllProductByNoteTemplateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Gets a list of products by note template.
        /// </summary>
        /// <param name="request">The request object containing the note template ID and filtering/paging parameters.</param>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the list of products.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("products")]
        [SwaggerOperation(Summary = "Gets a list of products by note template", Description = "Gets a list of products filtered by note template ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of products", typeof(PagingResponse<GetListProductByNoteTemplateResponseItem>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetProductsByNoteTemplate([FromQuery] GetListProductByNoteTemplateRequest request)
        {
            var getListProductByNoteTemplateUseCase = HttpContext.RequestServices.GetRequiredService<GetListProductByNoteTemplateUseCase>();
            var result = await getListProductByNoteTemplateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Updates an existing note template.
        /// </summary>
        /// <param name="id">The ID of the note template to update.</param>
        /// <param name="request">The request object containing the updated details of the note template.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}")]
        [SwaggerOperation(Summary = "Updates an existing note template", Description = "Updates an existing note template with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated note template", typeof(UpdateNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateNoteTemplate(long id, [FromBody] UpdateNoteTemplateRequest request)
        {
            request.Id = id;
            var updateNoteTemplateUseCase = HttpContext.RequestServices.GetRequiredService<UpdateNoteTemplateUseCase>();
            var result = await updateNoteTemplateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }
    }
} 