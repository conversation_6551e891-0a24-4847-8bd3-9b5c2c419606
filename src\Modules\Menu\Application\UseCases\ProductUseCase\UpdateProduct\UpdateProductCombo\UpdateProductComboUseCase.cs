using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Localization;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo
{
    /// <summary>
    /// Implements the UpdateProductCombo use case for updating customizable combo details
    /// </summary>
    public class UpdateProductComboUseCase : UseCaseBase<UpdateProductComboRequest, UpdateProductComboResponse>
    {
        private readonly IProductRepository _productRepository;
        private readonly ICustomizableComboGroupRepository _comboGroupRepository;
        private readonly ICustomizableComboGroupItemRepository _comboGroupItemRepository;
        private readonly ILocalizationProvider _multiLang;  
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITenantProvider _tenantProvider;
        private readonly List<string> _validationErrors = new();

        public List<string> ValidationErrors => _validationErrors;

        public UpdateProductComboUseCase(
            IProductRepository productRepository,
            ICustomizableComboGroupRepository comboGroupRepository,
            ICustomizableComboGroupItemRepository comboGroupItemRepository,
            ILocalizationProvider multiLang,
            IUnitOfWork unitOfWork,
            ITenantProvider tenantProvider)
        {
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _comboGroupRepository = comboGroupRepository ?? throw new ArgumentNullException(nameof(comboGroupRepository));
            _comboGroupItemRepository = comboGroupItemRepository ?? throw new ArgumentNullException(nameof(comboGroupItemRepository));
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        }

        public override async Task<Result<UpdateProductComboResponse>> ExecuteAsync(
            UpdateProductComboRequest request,
            CancellationToken cancellationToken = default)
        {
            var tenantId = _tenantProvider.GetTenantId();
            
            // 1. Validate product and request
            var validationResult = await ValidateRequestAsync(request, cancellationToken);
            if (!validationResult.IsSuccess)
                return Result<UpdateProductComboResponse>.Failure(validationResult.ErrorMessage ?? "Unknown error");
            var product = validationResult.Value ?? throw new InvalidOperationException("Product is null");
            // 2. Process combo groups and items
            var processResult = await ProcessComboGroupsAndItemsAsync(
                request, product.Id, cancellationToken);
            var updatedGroupsMap = processResult;

            // 3. Commit changes
            await _unitOfWork.CommitAsync(cancellationToken);

            // 4. Construct and return response
            return Result<UpdateProductComboResponse>.Success(await BuildResponseAsync(product, tenantId, updatedGroupsMap, cancellationToken));
        }

        private async Task<Result<Product>> ValidateRequestAsync(
            UpdateProductComboRequest request,
            CancellationToken cancellationToken)
        {
            var product = await _productRepository.GetAsync(request.ProductId, cancellationToken);
            
            if (product == null)
                return Result<Product>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            
            if (product.ProductType != ProductTypes.CustomizableCombo.Id)
                return Result<Product>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_combo, product.Id));
            
            if (request.ComboGroups == null || request.ComboGroups.Count == 0)
                return Result<Product>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_combo_groups_required));

            ValidationErrors.Clear();
            var isValid = await ValidateComboGroupsAsync(request.ProductId, request.ComboGroups, cancellationToken);
            
            if (!isValid)
                return Result<Product>.Failure(ValidationErrors);

            return Result<Product>.Success(product);
        }

        private async Task<Dictionary<long, CustomizableComboGroup>> ProcessComboGroupsAndItemsAsync(
            UpdateProductComboRequest request,
            long productId,
            CancellationToken cancellationToken)
        {
            var existingGroups = await _comboGroupRepository.GetByComboProductIdAsync(productId, cancellationToken);
            var updatedGroupsMap = new Dictionary<long, CustomizableComboGroup>();

            // Process groups and items
            foreach (var groupDto in request.ComboGroups)
            {
                var group = await ProcessComboGroupAsync(groupDto, productId, existingGroups, cancellationToken);
                updatedGroupsMap[group.Id] = group;
                await ProcessComboGroupItemsAsync(groupDto, group, cancellationToken);
            }

            // Delete unused groups and their items
            await DeleteUnusedGroupsAsync(existingGroups, request.ComboGroups, cancellationToken);

            return updatedGroupsMap;
        }

        private async Task<CustomizableComboGroup> ProcessComboGroupAsync(
            CustomizableComboGroupDto groupDto,
            long productId,
            IEnumerable<CustomizableComboGroup> existingGroups,
            CancellationToken cancellationToken)
        {
            CustomizableComboGroup group;
            
            if (groupDto.Id > 0)
            {
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
                group = existingGroups.FirstOrDefault(g => g.Id == groupDto.Id);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
                if (group == null)
                    throw new InvalidOperationException(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_combo_group_not_found, groupDto.Id));
                
                group.Update(groupDto.Name, groupDto.Description, groupDto.MaxQuantity, groupDto.SortOrder);
                await _comboGroupRepository.UpdateAsync(group, cancellationToken);
            }
            else
            {
                group = CustomizableComboGroup.Create(
                    productId,
                    groupDto.Name,
                    groupDto.Description,
                    groupDto.MaxQuantity,
                    groupDto.SortOrder
                );
                group = await _comboGroupRepository.AddAsync(group, cancellationToken);
            }

            return group;
        }

        private async Task ProcessComboGroupItemsAsync(
            CustomizableComboGroupDto groupDto,
            CustomizableComboGroup group,
            CancellationToken cancellationToken)
        {
            var existingItems = await _comboGroupItemRepository.GetByGroupIdAsync(group.Id);

            foreach (var itemDto in groupDto.Items)
            {
                ProcessComboGroupItemAsync(itemDto, group, existingItems);
            }

            // Delete items that are not in the request
            await DeleteUnusedItemsAsync(existingItems, groupDto.Items, cancellationToken);
        }

        private CustomizableComboGroupItem ProcessComboGroupItemAsync(
            CustomizableComboGroupItemDto itemDto,
            CustomizableComboGroup group,
            IEnumerable<CustomizableComboGroupItem> existingItems)
        {
            CustomizableComboGroupItem item;

            if (itemDto.Id > 0)
            {
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
                item = existingItems.FirstOrDefault(i => i.Id == itemDto.Id);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
                if (item == null)
                    throw new InvalidOperationException(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_combo_item_not_found, itemDto.Id));
                
                item.Update(itemDto.AdditionalPrice, itemDto.SortOrder);
            }
            else
            {
                item = CustomizableComboGroupItem.Create(
                    group.Id,
                    itemDto.ProductId,
                    itemDto.AdditionalPrice,
                    itemDto.SortOrder
                );
                group.AddItem(item);
            }

            return item;
        }

        private async Task DeleteUnusedItemsAsync(
            IEnumerable<CustomizableComboGroupItem> existingItems,
            IEnumerable<CustomizableComboGroupItemDto> requestItems,
            CancellationToken cancellationToken)
        {
            var itemsToDelete = existingItems.Where(i => !requestItems.Any(dto => dto.Id == i.Id));
            foreach (var itemToDelete in itemsToDelete)
            {
                await _comboGroupItemRepository.RemoveAsync(itemToDelete, cancellationToken);
            }
        }

        private async Task DeleteUnusedGroupsAsync(
            IEnumerable<CustomizableComboGroup> existingGroups,
            IEnumerable<CustomizableComboGroupDto> requestGroups,
            CancellationToken cancellationToken)
        {
            var groupsToDelete = existingGroups.Where(g => !requestGroups.Any(dto => dto.Id == g.Id));
            foreach (var groupToDelete in groupsToDelete)
            {
                var itemsToDelete = await _comboGroupItemRepository.GetByGroupIdAsync(groupToDelete.Id);
                foreach (var itemToDelete in itemsToDelete)
                {
                    await _comboGroupItemRepository.RemoveAsync(itemToDelete, cancellationToken);
                }
                await _comboGroupRepository.RemoveAsync(groupToDelete, cancellationToken);
            }
        }

        private async Task<UpdateProductComboResponse> BuildResponseAsync(
            Product product,
            long? tenantId,
            Dictionary<long, CustomizableComboGroup> updatedGroupsMap,
            CancellationToken cancellationToken)
        {
            // Get all product IDs used in combo items
            var productIds = updatedGroupsMap.Values
                .SelectMany(group => group.Items)
                .Select(item => item.ProductId)
                .Distinct()
                .ToList();
            
            // Fetch all products in one call
            var products = await _productRepository.GetProductsByIdsAsync(productIds, cancellationToken);
            var productsMap = products.ToDictionary(p => p.Id, p => p);
            
            var comboGroups = new List<CustomizableComboGroupDto>();
            
            foreach (var group in updatedGroupsMap.Values)
            {
                var items = group.Items.ToList();
                var groupDto = BuildComboGroupDto(group, items, productsMap);
                comboGroups.Add(groupDto);
            }
            
            var productCombo = new ProductComboDto
            {
                Id = product.Id,
                Code = product.Code,
                Name = product.Name,
                ProductType = product.ProductType ?? (byte)0,
                BasePrice = product.BasePrice,
                RetailerId = (int)(tenantId ?? 0),
                ComboGroups = comboGroups
            };
            
            return new UpdateProductComboResponse { ProductCombo = productCombo };
        }

        private static CustomizableComboGroupDto BuildComboGroupDto(
            CustomizableComboGroup group,
            List<CustomizableComboGroupItem> items,
            Dictionary<long, Product> productsMap)
        {
            var itemsDto = items.Select(item => new CustomizableComboGroupItemDto
            {
                Id = item.Id,
                GroupId = item.GroupId,
                ProductId = item.ProductId,
                AdditionalPrice = item.AdditionalPrice,
                SortOrder = item.SortOrder,
                Product = new CustomizableComboGroupItemDto.ProductObjectDto
                {
                    Id = item.ProductId,
                    Name = productsMap.TryGetValue(item.ProductId, out var product) ? product.Name : string.Empty,
                    Code = productsMap.TryGetValue(item.ProductId, out var productCode) ? productCode.Code : string.Empty
                }
            }).ToList();

            return new CustomizableComboGroupDto
            {
                Id = group.Id,
                ComboProductId = group.ComboProductId,
                Name = group.Name,
                Description = group.Description,
                MaxQuantity = group.MaxQuantity,
                SortOrder = group.SortOrder,
                Items = itemsDto
            };
        }

        private async Task<bool> ValidateComboGroupsAsync(
            long comboProductId, 
            IEnumerable<CustomizableComboGroupDto> groups,
            CancellationToken cancellationToken)
        {
            if (!await ValidateComponentProductsExistAsync(groups, cancellationToken))
                return false;

            if (!await ValidateNoCircularReferencesAsync(comboProductId, groups, cancellationToken))
                return false;

            if (!await ValidateProductTypesAsync(groups, cancellationToken))
                return false;

            if (!ValidateGroupStructure(groups))
                return false;

            return true;
        }

        private async Task<bool> ValidateComponentProductsExistAsync(
            IEnumerable<CustomizableComboGroupDto> groups,
            CancellationToken cancellationToken)
        {
            var componentProductIds = groups
                .SelectMany(g => g.Items)
                .Select(i => i.ProductId)
                .Distinct()
                .ToList();

            if (componentProductIds.Count == 0)
            {
                ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_groups_min_items));
                return false;
            }

            var products = await _productRepository.GetProductsByIdsAsync(componentProductIds, cancellationToken);
            var foundProductIds = products.Select(p => p.Id).ToHashSet();
            var missingProductIds = componentProductIds.Where(id => !foundProductIds.Contains(id)).ToList();

            if (missingProductIds.Count != 0)
            {
                ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_product_missing, string.Join(", ", missingProductIds)));
                return false;
            }

            return true;
        }

        private async Task<bool> ValidateNoCircularReferencesAsync(
            long comboProductId,
            IEnumerable<CustomizableComboGroupDto> groups,
            CancellationToken cancellationToken)
        {
            var componentProductIds = groups
                .SelectMany(g => g.Items)
                .Select(i => i.ProductId)
                .Distinct()
                .ToList();

            // Direct circular reference check
            if (componentProductIds.Contains(comboProductId))
            {
                ValidationErrors.Add("Circular reference detected: A combo product cannot contain itself");
                return false;
            }

            // Get all products for nested combo check
            var products = await _productRepository.GetProductsByIdsAsync(componentProductIds, cancellationToken);
            var nestedComboProducts = products.Where(p => p.ProductType == ProductTypes.CustomizableCombo.Id).ToList();

            // Check for nested circular references
            foreach (var nestedCombo in nestedComboProducts.Select(nestedCombo => nestedCombo.Id))
            {
                if (await HasCircularReferenceAsync(nestedCombo, comboProductId, [], cancellationToken))
                {
                    ValidationErrors.Add($"Circular reference detected: Product {nestedCombo} contains the current combo product");
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> HasCircularReferenceAsync(
            long currentComboId,
            long targetComboId,
            HashSet<long> visitedCombos,
            CancellationToken cancellationToken)
        {
            if (!visitedCombos.Add(currentComboId))
                return false;

            var nestedGroups = await _comboGroupRepository.GetByComboProductIdAsync(currentComboId, cancellationToken);
            
            foreach (var group in nestedGroups)
            {
                var items = await _comboGroupItemRepository.GetByGroupIdAsync(group.Id);
                foreach (var item in items.Select(item => item.ProductId))
                {
                    if (item == targetComboId)
                        return true;

                    var product = await _productRepository.GetAsync(item, cancellationToken);
                    if (product?.ProductType == ProductTypes.CustomizableCombo.Id && 
                        await HasCircularReferenceAsync(item, targetComboId, visitedCombos, cancellationToken))
                        return true;
                }
            }

            return false;
        }

        private async Task<bool> ValidateProductTypesAsync(
            IEnumerable<CustomizableComboGroupDto> groups,
            CancellationToken cancellationToken)
        {
            var componentProductIds = groups
                .SelectMany(g => g.Items)
                .Select(i => i.ProductId)
                .Distinct()
                .ToList();

            var products = await _productRepository.GetProductsByIdsAsync(componentProductIds, cancellationToken);
            var serviceProducts = products.Where(p => p.ProductType == (byte)ProductTypes.Service.Id && p.IsTimeServices == true).ToList();

            if (serviceProducts.Count != 0)
            {
                var serviceNames = string.Join(", ", serviceProducts.Select(p => p.Name));
                ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_service_invalid_combo, serviceNames));
                return false;
            }

            // Check if the product is a customizable combo
            var customizableComboProducts = products.Where(p => p.ProductType == (byte)ProductTypes.CustomizableCombo.Id).ToList();
            if (customizableComboProducts.Count != 0)
            {
                var customizableComboNames = string.Join(", ", customizableComboProducts.Select(p => p.Name));
                ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_invalid_product_type, customizableComboNames));
                return false;
            }

            // not allow Combo and IsProcessedGoods is true
            var comboProducts = products.Where(p => p.ProductType == (byte)ProductTypes.Combo.Id && p.IsProcessedGoods != true).ToList();
            if (comboProducts.Count != 0)
            {
                var comboNames = string.Join(", ", comboProducts.Select(p => p.Name));
                ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_invalid_product_type, comboNames));
                return false;
            }
            return true;
        }

        private bool ValidateGroupStructure(IEnumerable<CustomizableComboGroupDto> groups)
        {
            foreach (var group in groups)
            {
                if (string.IsNullOrEmpty(group.Name))
                {
                    ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_name_required));
                    return false;
                }

                if (group.Name.Length > 100)
                {
                    ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_name_max_length));
                    return false;
                }

                if (group.Items == null || group.Items.Count == 0)
                {
                    ValidationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_items_min_items));
                    return false;
                }
            }

            return true;
        }
    }
} 