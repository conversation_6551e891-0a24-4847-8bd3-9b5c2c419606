using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.Dtos.Response;
using KvFnB.Modules.Menu.Application.Tests.Helpers;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;
using KvFnB.Shared.Localization;
using Microsoft.Extensions.Logging;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.CreateProduct
{
    public class CreateProductUseCaseTests
    {
        private readonly ILocalizationProvider _multiLang;
        private readonly Mock<IValidator<CreateProductRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<ICategoryRepository> _categoryRepositoryMock;
        private readonly Mock<ITaxRepository> _taxRepositoryMock;
        private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<Core.Abstractions.IMapper> _mapperMock;
        private readonly Mock<ICodeGenerationService> _codeGenerationServiceMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly Mock<IAuthUser> _authUserMock;
        private readonly Mock<IProductPriceBookDomainService> _productPriceBookDomainServiceMock;
        private readonly Mock<ILogger<CreateProductUseCase>> _loggerMock;
        private readonly CreateProductUseCase _useCase;

        public CreateProductUseCaseTests()
        {
            _multiLang = TestLocalizationHelper.CreateVietnameseLocalizationProvider();
            _validatorMock = new Mock<IValidator<CreateProductRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _categoryRepositoryMock = new Mock<ICategoryRepository>();
            _taxRepositoryMock = new Mock<ITaxRepository>();
            _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _mapperMock = new Mock<Core.Abstractions.IMapper>();
            _codeGenerationServiceMock = new Mock<ICodeGenerationService>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _authUserMock = new Mock<IAuthUser>();
            _productPriceBookDomainServiceMock = new Mock<IProductPriceBookDomainService>();
            _loggerMock = new Mock<ILogger<CreateProductUseCase>>();

            _useCase = new CreateProductUseCase(
                _multiLang,
                _validatorMock.Object,
                _productRepositoryMock.Object,
                _categoryRepositoryMock.Object,
                _taxRepositoryMock.Object,
                _priceBookRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _mapperMock.Object,
                _codeGenerationServiceMock.Object,
                _tenantProviderMock.Object,
                _authUserMock.Object,
                _productPriceBookDomainServiceMock.Object
            );
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest 
            { 
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m
            };
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
            _categoryRepositoryMock.Verify(r => r.GetAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenCategoryNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                ProductTypeId = ProductTypes.Normal.Id
            };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Category)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_category_not_found), result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductCodeNotUnique_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m
            };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(false);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Mã sản phẩm đã tồn tại", result.ErrorMessage, StringComparison.OrdinalIgnoreCase);
        }

        [Fact]
        public async Task ExecuteAsync_WhenNormalProductCreated_ShouldReturnSuccess()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id
            };
            var validationResult = ValidationResult.Success();
            
            // Create a category using NewCategory method
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);

            _codeGenerationServiceMock.Setup(r => r.GenerateCodeAsync(
                    It.IsAny<int>(), 
                    It.IsAny<string>(), 
                    It.IsAny<string>(), 
                    It.IsAny<string>(), 
                    It.IsAny<int>()))
                .ReturnsAsync(request.Code);
                
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(response, result.Value);
            _productRepositoryMock.Verify(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }


        [Fact]
        public async Task ExecuteAsync_WithCustomizableComboNoGroups_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                ProductTypeId = ProductTypes.CustomizableCombo.Id,
                CustomizableComboGroups = null
            };
            var validationResult = ValidationResult.Success();
            
            // Create a category using NewCategory method
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_combo_groups_required), result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WithTaxConfiguration_ShouldSetProductTax()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                TaxId = 1
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var tax = Tax.Create("VAT", 10.0m, 1);
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _taxRepositoryMock.Setup(r => r.GetAsync(request.TaxId.Value, It.IsAny<CancellationToken>()))
                .ReturnsAsync(tax);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _taxRepositoryMock.Verify(r => r.GetAsync(request.TaxId.Value, It.IsAny<CancellationToken>()), Times.Once);
            _productRepositoryMock.Verify(
                r => r.AddAsync(
                    It.Is<Product>(p => p.ProductTaxes.Count != 0 && p.ProductTaxes.First(x => x.Status == 0).TaxId == tax.Id && p.ProductTaxes.First(x => x.Status == 0).TaxRate == tax.TaxRate),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithTaxNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                TaxId = 999 // Non-existent tax ID
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _taxRepositoryMock.Setup(r => r.GetAsync(request.TaxId.Value, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Tax)null!);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_tax_not_found), result.ErrorMessage);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithProductImages_ShouldAddImages()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                Images = new List<string> { "image1.jpg", "image2.jpg" }
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _productRepositoryMock.Verify(
                r => r.AddAsync(
                    It.Is<Product>(p => p.ProductImages.Count == 2 && 
                                        p.ProductImages.Any(i => i.Image == "image1.jpg") && 
                                        p.ProductImages.Any(i => i.Image == "image2.jpg")),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithToppingProducts_ShouldAddToppings()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                ToppingProductIds = new List<long> { 101, 102 }
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var toppingProduct1 = Product.CreateProduct("T001", "Topping 1", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "T001");
            typeof(Product).GetProperty("Id")!.SetValue(toppingProduct1, 101L);
            typeof(Product).GetProperty("IsTopping")!.SetValue(toppingProduct1, true);
            
            var toppingProduct2 = Product.CreateProduct("T002", "Topping 2", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "T002");
            typeof(Product).GetProperty("Id")!.SetValue(toppingProduct2, 102L);
            typeof(Product).GetProperty("IsTopping")!.SetValue(toppingProduct2, true);
            
            var toppingProducts = new List<Product> { toppingProduct1, toppingProduct2 };
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _productRepositoryMock.Setup(r => r.GetProductsByIdsAsync(It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(toppingProducts);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _productRepositoryMock.Verify(
                r => r.AddAsync(
                    It.Is<Product>(p => p.ProductToppings.Count == 2 && 
                                        p.ProductToppings.Any(t => t.ToppingProductId == 101) && 
                                        p.ProductToppings.Any(t => t.ToppingProductId == 102)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithProductPriceBooks_ShouldAddPriceBooks()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                PriceBooks = new List<PriceBookRequest> 
                {
                    new PriceBookRequest { PriceBookId = 1, Price = 15.0m },
                    new PriceBookRequest { PriceBookId = 2, Price = 20.0m }
                }
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var priceBook1 = PriceBook.Create(new CreatePriceBookModel
            {
                Name = "Standard",
                StartDate = DateTime.Now,
                EndDate = DateTime.MaxValue,
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            });
            typeof(PriceBook).GetProperty("Id")!.SetValue(priceBook1, 1L);
            
            var priceBook2 = PriceBook.Create(new CreatePriceBookModel
            {
                Name = "Premium",
                StartDate = DateTime.Now,
                EndDate = DateTime.MaxValue,
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            });
            typeof(PriceBook).GetProperty("Id")!.SetValue(priceBook2, 2L);
            
            var priceBooks = new List<PriceBook> { priceBook1, priceBook2 };
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _priceBookRepositoryMock.Setup(r => r.GetByIdsAsync(It.IsAny<IEnumerable<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(priceBooks);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Create a real domain service for this test instead of mocking
            _priceBookRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<IEnumerable<PriceBook>>()));

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _priceBookRepositoryMock.Verify(r => r.GetByIdsAsync(
                It.Is<IEnumerable<long>>(ids => ids.Contains(1) && ids.Contains(2)),
                It.IsAny<CancellationToken>()), 
                Times.Once);
            _priceBookRepositoryMock.Verify(r => r.UpdateRangeAsync(
                It.IsAny<IEnumerable<PriceBook>>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithValidCustomizableCombo_ShouldCreateComboProduct()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Combo Meal",
                CategoryId = 1,
                BasePrice = 25.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.CustomizableCombo.Id,
                CustomizableComboGroups =
                [
                    new CustomizableComboGroupDto
                    {
                        Id = 0,
                        Name = "Main Course",
                        Description = "Choose your main dish",
                        MaxQuantity = 1,
                        SortOrder = 1,
                        Items = new List<CustomizableComboGroupItemDto>
                        {
                            new CustomizableComboGroupItemDto
                            {
                                ProductId = 101,
                                AdditionalPrice = 0.0m
                            },
                            new CustomizableComboGroupItemDto
                            {
                                ProductId = 102,
                                AdditionalPrice = 2.0m
                            }
                        }
                    },
                    new CustomizableComboGroupDto
                    {
                        Id = 0,
                        Name = "Sides",
                        Description = "Choose your side items",
                        MaxQuantity = 2,
                        SortOrder = 2,
                        Items = new List<CustomizableComboGroupItemDto>
                        {
                            new CustomizableComboGroupItemDto
                            {
                                ProductId = 201,
                                AdditionalPrice = 0.0m
                            },
                            new CustomizableComboGroupItemDto
                            {
                                ProductId = 202,
                                AdditionalPrice = 1.0m
                            }
                        }
                    }
                ]
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.CustomizableCombo.Id,
                request.Unit,
                request.ConversionValue,
                request.Code
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            // Create combo item products
            var itemProduct1 = Product.CreateProduct("IP101", "Item 1", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "IP101");
            typeof(Product).GetProperty("Id")!.SetValue(itemProduct1, 101L);
            
            var itemProduct2 = Product.CreateProduct("IP102", "Item 2", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "IP102");
            typeof(Product).GetProperty("Id")!.SetValue(itemProduct2, 102L);
            
            var itemProduct3 = Product.CreateProduct("IP201", "Item 3", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "IP201");
            typeof(Product).GetProperty("Id")!.SetValue(itemProduct3, 201L);
            
            var itemProduct4 = Product.CreateProduct("IP202", "Item 4", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "IP202");
            typeof(Product).GetProperty("Id")!.SetValue(itemProduct4, 202L);
            
            var comboProducts = new List<Product> { itemProduct1, itemProduct2, itemProduct3, itemProduct4 };
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = request.Code,
                Name = request.Name,
                CustomizableComboGroups = request.CustomizableComboGroups
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _productRepositoryMock.Setup(r => r.GetProductsByIdsAsync(It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(comboProducts);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);
            _mapperMock.Setup(m => m.Map<CategoryResponse>(category)).Returns(new CategoryResponse(1, "Test Category", null!, false));
            _mapperMock.Setup(m => m.Map<List<CustomizableComboGroupDto>>(It.IsAny<ICollection<CustomizableComboGroup>>()))
                .Returns(request.CustomizableComboGroups);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value!.CustomizableComboGroups);
            Assert.Equal(2, result.Value.CustomizableComboGroups.Count);
            
            _productRepositoryMock.Verify(
                r => r.AddAsync(
                    It.Is<Product>(p => 
                        p.ProductType == ProductTypes.CustomizableCombo.Id && 
                        p.ComboGroups.Count == 2 &&
                        p.ComboGroups.Any(g => g.Name == "Main Course" && g.Items.Count == 2) &&
                        p.ComboGroups.Any(g => g.Name == "Sides" && g.Items.Count == 2)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithInvalidToppingProducts_ShouldReturnFailure()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Code = "P001",
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id,
                ToppingProductIds = new List<long> { 101, 102 }
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var toppingProduct1 = Product.CreateProduct("T001", "Non-Topping 1", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "T001");
            typeof(Product).GetProperty("Id")!.SetValue(toppingProduct1, 101L);
            typeof(Product).GetProperty("IsTopping")!.SetValue(toppingProduct1, false); // Not a topping
            
            var toppingProduct2 = Product.CreateProduct("T002", "Topping 2", 1, (byte)ProductTypes.Normal.Id, "pcs", 1.0f, "T002");
            typeof(Product).GetProperty("Id")!.SetValue(toppingProduct2, 102L);
            typeof(Product).GetProperty("IsTopping")!.SetValue(toppingProduct2, true);
            
            var toppingProducts = new List<Product> { toppingProduct1, toppingProduct2 };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(request.Code, null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            _productRepositoryMock.Setup(r => r.GetProductsByIdsAsync(It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(toppingProducts);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);

            // Act
            var result = await _useCase.ExecuteAsync(request);
            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_invalid_topping, "101"), result.ErrorMessage);
        }
        
        [Fact]
        public async Task ExecuteAsync_WithCodeGeneration_ShouldGenerateCodeWhenNotProvided()
        {
            // Arrange
            var request = new CreateProductRequest
            {
                Name = "Test Product",
                CategoryId = 1,
                BasePrice = 10.0m,
                Unit = "pcs",
                ConversionValue = 1.0f,
                ProductTypeId = ProductTypes.Normal.Id
            };
            var validationResult = ValidationResult.Success();
            
            var category = Category.NewCategory("Test Category", null, null);
            typeof(Category).GetProperty("Id")!.SetValue(category, 1);
            
            var generatedCode = "SP0001";
            
            var product = Product.CreateProduct(
                generatedCode,
                request.Name,
                request.CategoryId,
                (byte)ProductTypes.Normal.Id,
                request.Unit,
                request.ConversionValue,
                generatedCode
            );
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var response = new CreateProductResponse
            {
                Id = 1,
                Code = generatedCode,
                Name = request.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.IsUniqueProductCodeAsync(It.IsAny<string>(), null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            _categoryRepositoryMock.Setup(r => r.GetAsync(request.CategoryId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(category);
            
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
            
            _codeGenerationServiceMock
                .Setup(s => s.GenerateCodeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .ReturnsAsync(generatedCode);
            
            _productRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<CreateProductResponse>(product)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(generatedCode, result.Value!.Code);
            _codeGenerationServiceMock.Verify(
                s => s.GenerateCodeAsync(
                    1, 
                    nameof(Product), 
                    "SP", 
                    null, 
                    6),
                Times.Once);
        }
    }
} 