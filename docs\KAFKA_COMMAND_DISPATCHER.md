# Kafka Command Dispatcher

## Overview

The Kafka Command Dispatcher is an implementation of the `ICommandDispatcher` interface that enables asynchronous command processing via Apache Kafka. It facilitates reliable, scalable communication between distributed system components through a publish-subscribe messaging pattern.

This component follows the hexagonal architecture principles, separating the core business logic from infrastructure concerns. The dispatcher publishes commands to Kafka topics where they can be consumed by various services.

## Architecture

The Command Dispatcher system follows the clean architecture design with the following components:

```
┌─────────────────┐     ┌─────────────────────┐     ┌───────────────────┐
│                 │     │                     │     │                   │
│  Application    │────▶│  CommandDispatcher  │────▶│  Kafka Cluster    │
│  Services       │     │  System             │     │                   │
│                 │     │                     │     │                   │
└─────────────────┘     └─────────────────────┘     └───────────────────┘
```

### Key Components

1. **ICommandDispatcher Interface**: Defines the contract for sending commands (located in Core.Abstractions)
2. **BaseCommand**: Base class for all commands (located in Core.Domain.Commands)
3. **KafkaCommandDispatcher**: Implementation that uses Confluent.Kafka (located in Shared.MessageQueueProvider.Kafka)
4. **CommandDto**: Data transfer object for command serialization (located in Shared.MessageQueueProvider.Kafka.Models)
5. **KafkaConfig**: Configuration class for Kafka settings (located in Shared.MessageQueueProvider.Kafka)

## Configuration

### KafkaConfig

The `KafkaCommandDispatcher` is configured via the `KafkaConfig` class:

```csharp
public class KafkaConfig
{
    public string BootstrapServers { get; set; }
    public string DefaultTopic { get; set; }
    public int RetryAttempts { get; set; } = 3;
    public string ClientId { get; set; }
    public int MessageMaxBytes { get; set; } = 104857600; // Default: 100MB
}
```

### appsettings.json Configuration

Add the following configuration to your `appsettings.json` file:

```json
{
  "Kafka": {
    "BootstrapServers": "kafka1:9092,kafka2:9092",
    "DefaultTopic": "default-topic",
    "RetryAttempts": 3,
    "ClientId": "kvfnb-api",
    "MessageMaxBytes": 104857600
  }
}
```

## Usage

### Registering the Command Dispatcher

In your `Program.cs` or `Startup.cs` file:

```csharp
using KvFnB.Shared.DependencyInjection;

// Register Kafka services
services.AddKafkaCommandDispatcher(Configuration);
```

### Creating Command Classes

Create your command classes by inheriting from `BaseCommand`:

```csharp
using KvFnB.Core.Domain.Commands;

public class CreateCustomerCommand : BaseCommand
{
    public string Name { get; }
    public string ContactNumber { get; }
    public string Email { get; }
    
    public CreateCustomerCommand(int tenantId, long userId, string name, string contactNumber, string email)
        : base(tenantId, userId)
    {
        Name = name;
        ContactNumber = contactNumber;
        Email = email;
    }
}
```

### Sending Commands

Inject the `ICommandDispatcher` interface into your service or controller:

```csharp
public class CustomerService
{
    private readonly ICommandDispatcher _commandDispatcher;
    
    public CustomerService(ICommandDispatcher commandDispatcher)
    {
        _commandDispatcher = commandDispatcher;
    }
    
    public async Task CreateCustomerAsync(CustomerDto customerDto)
    {
        // Process customer data
        
        // Create and send command
        var command = new CreateCustomerCommand(
            tenantId: 1001,
            userId: 5001,
            name: customerDto.Name,
            contactNumber: customerDto.ContactNumber,
            email: customerDto.Email
        );
        
        await _commandDispatcher.SendAsync(command, "customer-commands");
    }
}
```

## Implementation Details

### Command Serialization

Commands are serialized using System.Text.Json and wrapped in a CommandDto that includes metadata about the command:

```csharp
internal class CommandDto
{
    public Guid CommandId { get; set; }
    public DateTime CreatedAt { get; set; }
    public int TenantId { get; set; }
    public long UserId { get; set; }
    public string CommandType { get; set; }
    public string CommandData { get; set; }
    
    // Constructor and other members...
}
```

### Thread Safety

The implementation ensures thread safety through:

- Lazy initialization pattern for the Kafka producer
- Lock-based synchronization for producer creation
- Thread-safe Kafka client from Confluent.Kafka

### Resource Management

The dispatcher properly manages Kafka resources through the `IDisposable` pattern:

```csharp
protected virtual void Dispose(bool disposing)
{
    if (_disposed) return;

    if (disposing)
    {
        _producer?.Dispose();
    }

    _disposed = true;
}
```

## Best Practices

### Configuration

1. **Bootstrap Servers**
   - Use multiple broker addresses for redundancy
   - Example: `kafka1:9092,kafka2:9092,kafka3:9092`

2. **Client ID**
   - Use descriptive client IDs
   - Include application name, environment, and instance
   - Example: `kvfnb-api-prod-01`

### Topic Naming

Follow a consistent naming convention:
- Use kebab-case
- Include domain/feature prefix
- Include environment if necessary
- Examples: `retail-orders-completed`, `customer-registrations`

### Command Design

1. **Keep commands small**
   - Include only necessary data
   - Consider references instead of embedding large objects

2. **Use appropriate command names**
   - Name commands with verb-noun pattern
   - Examples: `CreateCustomerCommand`, `ProcessOrderCommand`

3. **Include relevant metadata**
   - Always include tenant ID and user ID for multi-tenant operations
   - Include timestamps for auditing purposes

## Error Handling

The dispatcher implements comprehensive error handling:

1. **Producer errors**: Captured via error handler callbacks
2. **Delivery errors**: Tracked through delivery reports
3. **Exception handling**: Try-catch blocks around publish operations

For robust error handling, the system:

- Logs detailed error information
- Throws exceptions for critical failures
- Uses async/await for proper exception propagation

## Security Considerations

For production environments, consider implementing:

1. **Authentication**
   - SASL/SCRAM or SSL client authentication

2. **Encryption**
   - SSL/TLS for transport encryption

3. **Authorization**
   - ACLs for topic-level access control

## Monitoring

Monitor your Kafka command dispatcher with:

1. **Producer metrics**
   - Message send rate
   - Error rate
   - Latency

2. **Log analysis**
   - Track error patterns
   - Identify problematic commands

3. **Integration with monitoring systems**
   - Prometheus metrics
   - Grafana dashboards

## Troubleshooting

| Issue | Cause | Solution |
|-------|-------|----------|
| Connection failure | Broker unreachable | Check network connections and broker status |
| Authentication errors | Missing/invalid credentials | Verify security configuration |
| Message too large | Exceeds MessageMaxBytes | Reduce message size or increase limit |
| Serialization errors | Invalid command structure | Validate command before sending |

## Conclusion

The `KafkaCommandDispatcher` provides a robust implementation for sending commands to Kafka topics. It integrates seamlessly with the KvFnB Core architecture, following the hexagonal architecture principles. It handles producer lifecycle management, error reporting, and resource cleanup, making it suitable for production use in distributed systems. 