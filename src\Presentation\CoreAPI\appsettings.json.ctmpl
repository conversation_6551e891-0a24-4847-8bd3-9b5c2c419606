{
  "ConnectionStrings": {
    "KiotVietFnBMaster": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_master/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/database") }};Persist security info={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/persist security info") }};User Id={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/user id") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/password") }}{{ .Data.data.password }}{{ end }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/multiple_active_result_sets") }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_master/max pool size") }}",
    "KiotVietFnBShard7": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard7/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard7/trust_server_certificate") }}",
    "KiotVietFnBShard15": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard15/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard15/trust_server_certificate") }}",
    "KiotVietFnBShard16": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard16/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard16/trust_server_certificate") }}",
    "KiotVietFnBShard22": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard22/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard22/trust_server_certificate") }}",
    "KiotVietFnBShard27": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard27/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard27/trust_server_certificate") }}",
    "KiotVietFnBShard31": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard31/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard31/trust_server_certificate") }}",
    "KiotVietFnBShard33": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard33/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard33/trust_server_certificate") }}",
    "KiotVietFnBShard34": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard34/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard34/trust_server_certificate") }}",
    "KiotVietFnBShard40": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard40/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard40/trust_server_certificate") }}",
    "KiotVietFnBShard41": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard41/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard41/trust_server_certificate") }}",
    "KiotVietFnBShard47": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/kiotviet_fnb_shard47/server") }};Database={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/database") }};User={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/user") }};Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/password") }}{{ .Data.data.password }}{{ end }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/max pool size") }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/multiple_active_result_sets") }};TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/api_core/connection_strings/kiotviet_fnb_shard47/trust_server_certificate") }}"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/default") }}",
      "Override": {
        "Microsoft": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/override/microsoft") }}",
        "System": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/override/system") }}"
      }
    },
    "WriteTo": [
      {
        "Name": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/name") }}",
        "Args": {
          "path": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/path") }}",
          "rollingInterval": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/rolling_interval") }}",
          "fileSizeLimitBytes": {{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/file_size_limit_bytes") }},
          "rollOnFileSizeLimit": true,
          "formatter": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/formatter") }}"
        }
      },
      {
        "Name": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/name") }}"
      }
    ],
    "Properties": {
      "ApplicationName": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/properties/application_name") }}"
    }
  },
  "ServiceStackJwtSettings": {
    "AuthKeyBase64": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/auth_key_base64") }}{{ .Data.data.auth_key_base64 }}{{ end }}",
    "AesIvBase64": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/aes_iv_base64") }}",
    "PrivateKeyXml": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/private_key_xml") }}{{ .Data.data.private_key_xml }}{{ end }}",
    "PublicKeyXml": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/public_key_xml") }}{{ .Data.data.public_key_xml }}{{ end }}",
    "ExpireTokensInDays": {{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/expire_tokens_in_days") }}{{ .Data.data.expire_tokens_in_days }}{{ end }},
    "ExpireTokensRefreshInDays": {{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/expire_tokens_refresh_in_days") }}{{ .Data.data.expire_tokens_refresh_in_days }}{{ end }},
    "Issuer": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/issuer") }}",
    "EncryptPayload": false,
    "RequireSecureConnection": false,
    "HashAlgorithm": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/hash_algorithm") }}",
    "RequireHashAlgorithm": false
  },
  "RedisCache": {
    "Servers": "{{ key (print "config/" (env "APP_ENV") "/general_config/redis_cache/servers") }}",
    "SentinelMasterName": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/sentinel_master_name") }}",
    "DbNumber": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/db_number") }},
    "MaxPoolSize": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/max_pool_size") }},
    "AuthPass": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/auth_pass") }}",
    "IsSentinel": true,
    "WaitBeforeForcingMasterFailover": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/wait_before_forcing_master_failover") }}
  },
  "AllowedHosts": "{{ key (print "config/" (env "APP_ENV") "/api_core/allowed_hosts") }}",
  "AmazonS3": {
    "BucketName": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/bucket_name") }}",
    "Region": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/region") }}",
    "AccessKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/access_key") }}{{ .Data.data.access_key }}{{ end }}",
    "SecretKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/secret_key") }}{{ .Data.data.secret_key }}{{ end }}",
    "UseInstanceProfile": false,
    "UseCompatibleMode": true,
    "CompatibleEndpoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/compatible_endpoint") }}",
    "CompatibleAccessKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/compatible_access_key") }}{{ .Data.data.compatible_access_key }}{{ end }}",
    "CompatibleSecretKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/compatible_secret_key") }}{{ .Data.data.compatible_secret_key }}{{ end }}",
    "ForcePathStyle": true,
    "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/proxy_url") }}",
    "BaseKeyPrefix": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/base_key_prefix") }}{{ .Data.data.base_key_prefix }}{{ end }}",
    "UploadPartSize": {{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/upload_part_size") }},
    "DefaultACL": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/default_acl") }}",
    "StorageClass": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/storage_class") }}",
    "EncryptionMethod": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/encryption_method") }}",
    "CacheControlHeader": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/cache_control_header") }}",
    "CloudfrontUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/cloudfront_url") }}"
  },
  "Kfin": {
    "InternalEndPoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/kfin/internal_end_point") }}",
    "InternalPrivateKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/kfin/internal_private_key") }}{{ .Data.data.internal_private_key }}{{ end }}",
    "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/kfin/proxy_url") }}"
  },
  "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/general_config/proxy_url") }}",
  "Kma": {
    "CmaOpenDomainEndpoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/kma/cma_open_domain_endpoint") }}",
    "ApiKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/kma/api_key") }}{{ .Data.data.api_key }}{{ end }}",
    "Timeout": {{ key (print "config/" (env "APP_ENV") "/api_core/kma/timeout") }},
    "RetryCount": {{ key (print "config/" (env "APP_ENV") "/api_core/kma/retry_count") }}
  },
  "OtpConfig": {
    "OtpSecret": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/otp_config/otp_secret") }}{{ .Data.data.otp_secret }}{{ end }}",
    "OtpIssuer": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_issuer") }}",
    "MaximumSmsRequestFromRetailerPerDay": {{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/maximum_sms_request_from_retailer_per_day") }},
    "SmsCountKeyPrefix": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/otp_config/sms_count_key_prefix") }}{{ .Data.data.sms_count_key_prefix }}{{ end }}",
    "OtpIssuser": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_issuser") }}",
    "OtpMessageTemplate": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_message_template") }}"
  },
  "KafkaProducer": {
    "BootstrapServers": "{{ key (print "config/" (env "APP_ENV") "/general_config/kafka_producer/bootstrap_servers") }}",
    "Topics": {
      "SmSTopic": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka_producer/topics/sm_s_topic") }}"
    }
  },
  "Kafka": {
    "BootstrapServers": "{{ key (print "config/" (env "APP_ENV") "/general_config/kafka/bootstrap_servers") }}",
    "DefaultTopic": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka/default_topic") }}",
    "ClientId": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka/client_id") }}",
    "MessageMaxBytes": {{ key (print "config/" (env "APP_ENV") "/api_core/kafka/message_max_bytes") }},
    "RetryAttempts": {{ key (print "config/" (env "APP_ENV") "/api_core/kafka/retry_attempts") }}
  },
  "TfaRedisMessagequeue": "{{ key (print "config/" (env "APP_ENV") "/api_core/tfa_redis_messagequeue") }}",
  "RedisMq": {
    "Servers": "{{ key (print "config/" (env "APP_ENV") "/general_config/redis_mq/servers") }}",
    "SentinelMasterName": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/sentinel_master_name") }}",
    "DbNumber": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/db_number") }},
    "AuthPass": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/auth_pass") }}",
    "IsSentinel": true,
    "IsStrictPool": true,
    "MaxPoolSize": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/max_pool_size") }},
    "MaxPoolTimeout": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/max_pool_timeout") }},
    "WaitBeforeForcingMasterFailover": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/wait_before_forcing_master_failover") }}
  }
}