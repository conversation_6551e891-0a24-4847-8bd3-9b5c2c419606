namespace KvFnB.Core.Domain
{
    /// <summary>
    /// Represents an entity that supports full auditing with creation and modification tracking
    /// </summary>
    public interface IAuditableEntity : ICreatedAt, ICreatedBy, IModifiedAt, IModifiedBy
    {
        // This interface combines ICreatedAt, ICreatedBy, IModifiedAt, and IModifiedBy
        // No additional members needed as it inherits all required members
    }

    /// <summary>
    /// Represents an entity that tracks when it was created
    /// </summary>
    public interface ICreatedAt
    {
        /// <summary>
        /// Gets or sets the date and time when the entity was created
        /// </summary>
        DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// Represents an entity that tracks who created it
    /// </summary>
    public interface ICreatedBy
    {
        /// <summary>
        /// Gets or sets the ID of the user who created the entity
        /// </summary>
        long CreatedBy { get; set; }
    }

    /// <summary>
    /// Represents an entity that tracks when it was last modified
    /// </summary>
    public interface IModifiedAt
    {
        /// <summary>
        /// Gets or sets the date and time when the entity was last modified
        /// </summary>
        DateTime? ModifiedAt { get; set; }
    }

    /// <summary>
    /// Represents an entity that tracks who last modified it
    /// </summary>
    public interface IModifiedBy
    {
        /// <summary>
        /// Gets or sets the ID of the user who last modified the entity
        /// </summary>
        long? ModifiedBy { get; set; }
    }
}