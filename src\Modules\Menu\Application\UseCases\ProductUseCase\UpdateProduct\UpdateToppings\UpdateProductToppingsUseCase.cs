using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings
{
    /// <summary>
    /// Implements the UpdateProductToppings use case
    /// </summary>
    public class UpdateProductToppingsUseCase
    {
        private readonly IValidator<UpdateProductToppingsRequest> _validator;
        private readonly ILocalizationProvider _multiLang;
        private readonly IProductRepository _productRepository;
        private readonly ITenantProvider _tenantProvider;
        private readonly IUnitOfWork _unitOfWork;
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductToppingsUseCase"/> class.
        /// </summary>
        public UpdateProductToppingsUseCase(
            IValidator<UpdateProductToppingsRequest> validator,
            IProductRepository productRepository,
            ILocalizationProvider multiLang,
            IUnitOfWork unitOfWork,
            ITenantProvider tenantProvider)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        }

        /// <summary>
        /// Executes the update product toppings use case.
        /// </summary>
        /// <param name="request">The request containing the product toppings to update.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the updated product toppings or error information.</returns>
        public async Task<Result<UpdateProductToppingsResponse>> ExecuteAsync(
            UpdateProductToppingsRequest request,
            CancellationToken cancellationToken = default)
        {
            // 1. Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<UpdateProductToppingsResponse>.Failure(validationResult.Errors);
            }

            // 2. Get existing product
            var product = await _productRepository.GetAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<UpdateProductToppingsResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            }

            // 3. Verify all topping products exist and are marked as toppings
            foreach (var toppingId in request.ToppingProductIds)
            {
                var toppingProduct = await _productRepository.GetAsync(toppingId, cancellationToken);
                if (toppingProduct == null)
                {
                    return Result<UpdateProductToppingsResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found, toppingId));
                }

                if (toppingProduct.IsTopping != true)
                {
                    return Result<UpdateProductToppingsResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_invalid_topping, toppingId));
                }
            }

            // 4. Update product toppings
            product.UpdateToppings(request.ToppingProductIds, _tenantProvider.GetTenantId());
            
            // For now, we'll update the product and assume the repository handles the topping IDs
            product = await _productRepository.UpdateAsync(product, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            // 5. Create response
            var response = new UpdateProductToppingsResponse
            {
                Id = product.Id,
                ToppingProductIds = request.ToppingProductIds, 
            };

            return Result<UpdateProductToppingsResponse>.Success(response);
        }
    }
} 