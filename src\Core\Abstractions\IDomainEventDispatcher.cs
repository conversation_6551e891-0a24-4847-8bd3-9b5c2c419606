using KvFnB.Core.Domain;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for dispatching domain events to handlers
    /// </summary>
    public interface IDomainEventDispatcher
    {
        /// <summary>
        /// Dispatches a domain event to all registered handlers
        /// </summary>
        /// <param name="domainEvent">The domain event to dispatch</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task DispatchAsync(DomainEvent domainEvent, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Dispatches all domain events from an aggregate root to their respective handlers
        /// </summary>
        /// <param name="aggregateRoot">The aggregate root containing domain events</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task DispatchEventsAsync(IAggregateRoot aggregateRoot, CancellationToken cancellationToken = default);
    }
} 