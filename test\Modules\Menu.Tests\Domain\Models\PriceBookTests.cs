using KvFnB.Core.Exceptions;
using KvFnB.Modules.Menu.Domain.Models;

namespace KvFnB.Modules.Menu.Domain.Tests.Models;

public class PriceBookTests
{
    [Fact]
    public void Create_ShouldCreatePriceBook_WhenValidParameters()
    {
        // Arrange
        var name = "Test Price Book";
        var startDate = DateTime.Now;
        var endDate = startDate.AddDays(30);

        // Act
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = name,
            StartDate = startDate,
            EndDate = endDate,
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });

        // Assert
        Assert.NotNull(priceBook);
        Assert.Equal(name, priceBook.Name);
        Assert.True(priceBook.IsActive);
        Assert.False(priceBook.IsGlobal);
        Assert.False(priceBook.ForAllUser);
        Assert.False(priceBook.ForAllCustomerGroup);
        Assert.False(priceBook.ForAllTableAndRoom);
        Assert.False(priceBook.ForTakeAwayTable);
        Assert.Null(priceBook.CustomTime);
        Assert.Null(priceBook.Type);
        Assert.Equal(startDate, priceBook.StartDate);
        Assert.Equal(endDate, priceBook.EndDate);
        Assert.Null(priceBook.IsDeleted);
    }

    [Fact]
    public void Create_ShouldCreatePriceBook_WithOptionalParameters()
    {
        // Arrange
        var name = "Test Price Book";
        var startDate = DateTime.Now;
        var endDate = startDate.AddDays(30);
        var isGlobal = true;
        var forAllUser = true;
        var forAllCustomerGroup = true;
        var forAllTableAndRoom = true;
        var forTakeAwayTable = true;
        var customTime = "9:00-17:00";
        var type = "Regular";

        // Act
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = name,
            StartDate = startDate,
            EndDate = endDate,
            IsGlobal = isGlobal,
            ForAllUser = forAllUser,
            ForAllCustomerGroup = forAllCustomerGroup,
            ForAllTableAndRoom = forAllTableAndRoom,
            ForTakeAwayTable = forTakeAwayTable,
            CustomTime = customTime,
            Type = type
        });

        // Assert
        Assert.Equal(name, priceBook.Name);
        Assert.True(priceBook.IsActive);
        Assert.True(priceBook.IsGlobal);
        Assert.True(priceBook.ForAllUser);
        Assert.True(priceBook.ForAllCustomerGroup);
        Assert.True(priceBook.ForAllTableAndRoom);
        Assert.True(priceBook.ForTakeAwayTable);
        Assert.Equal(customTime, priceBook.CustomTime);
        Assert.Equal(type, priceBook.Type);
    }

    [Fact]
    public void Create_ShouldThrowException_WhenNameIsEmpty()
    {
        // Arrange
        var startDate = DateTime.Now;
        var endDate = startDate.AddDays(30);

        // Act & Assert
        Assert.Throws<DomainException>(() => PriceBook.Create(new CreatePriceBookModel
        {
            Name = "",
            StartDate = startDate,
            EndDate = endDate,
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        }));
    }

    [Fact]
    public void Create_ShouldThrowException_WhenStartDateIsAfterEndDate()
    {
        // Arrange
        var startDate = DateTime.Now;
        var endDate = startDate.AddDays(-1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = startDate,
            EndDate = endDate,
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        }));
    }

    [Fact]
    public void Update_ShouldUpdateAllProperties()
    {
        // Arrange
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Original Name",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });

        var newName = "Updated Name";
        var newStartDate = DateTime.Now.AddDays(1);
        var newEndDate = newStartDate.AddDays(30);
        var newIsGlobal = true;
        var newForAllUser = true;
        var newForAllCustomerGroup = true;
        var newForAllTableAndRoom = true;
        var newForTakeAwayTable = true;
        var newCustomTime = "10:00-18:00";
        var newType = "Special";

        // Act
        priceBook.Update(new UpdatePriceBookModel
        {
            Name = newName,
            StartDate = newStartDate,
            EndDate = newEndDate,
            IsGlobal = newIsGlobal,
            ForAllUser = newForAllUser,
            ForAllCustomerGroup = newForAllCustomerGroup,
            ForAllTableAndRoom = newForAllTableAndRoom,
            ForTakeAwayTable = newForTakeAwayTable,
            CustomTime = newCustomTime,
            Type = newType
        });

        // Assert
        Assert.Equal(newName, priceBook.Name);
        Assert.Equal(newStartDate, priceBook.StartDate);
        Assert.Equal(newEndDate, priceBook.EndDate);
        Assert.Equal(newIsGlobal, priceBook.IsGlobal);
        Assert.Equal(newForAllUser, priceBook.ForAllUser);
        Assert.Equal(newForAllCustomerGroup, priceBook.ForAllCustomerGroup);
        Assert.Equal(newForAllTableAndRoom, priceBook.ForAllTableAndRoom);
        Assert.Equal(newForTakeAwayTable, priceBook.ForTakeAwayTable);
        Assert.Equal(newCustomTime, priceBook.CustomTime);
        Assert.Equal(newType, priceBook.Type);
    }

    [Fact]
    public void Update_ShouldThrowException_WhenNameIsEmpty()
    {
        // Arrange
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Original Name",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });

        // Act & Assert
        Assert.Throws<ArgumentException>(() => priceBook.Update(new UpdatePriceBookModel
        {
            Name = "",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            CustomTime = null,
            Type = null
        }));
    }

    [Fact]
    public void Update_ShouldThrowException_WhenStartDateIsAfterEndDate()
    {
        // Arrange
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Original Name",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });

        var startDate = DateTime.Now;
        var endDate = startDate.AddDays(-1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => priceBook.Update(new UpdatePriceBookModel
        {
            Name = "Updated Name",
            StartDate = startDate,
            EndDate = endDate,
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            CustomTime = null,
            Type = null
        }));
    }

    [Fact]
    public void Activate_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });
        priceBook.Deactivate();

        // Act
        priceBook.Activate();

        // Assert
        Assert.True(priceBook.IsActive);
    }

    [Fact]
    public void Deactivate_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = false,
            ForAllUser = false,
            ForAllCustomerGroup = false,
            ForAllTableAndRoom = false,
            ForTakeAwayTable = false,
            Type = null
        });

        // Act
        priceBook.Deactivate();

        // Assert
        Assert.False(priceBook.IsActive);
    }
} 