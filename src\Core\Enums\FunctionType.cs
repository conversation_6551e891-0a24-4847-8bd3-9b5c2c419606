namespace KvFnB.Core.Enums
{
    /// <summary>
    /// Represents the system function types for audit logging
    /// </summary>
    public enum FunctionType
    {
        /// <summary>
        /// Customer management functions
        /// </summary>
        Customer = 1,
        
        /// <summary>
        /// Product management functions
        /// </summary>
        Product = 2,
        
        /// <summary>
        /// Order management functions
        /// </summary>
        Order = 3,
        
        /// <summary>
        /// Payment functions
        /// </summary>
        Payment = 4,
        
        /// <summary>
        /// Inventory management functions
        /// </summary>
        Inventory = 5,
        
        /// <summary>
        /// User management functions
        /// </summary>
        User = 6,
        
        /// <summary>
        /// System configuration functions
        /// </summary>
        System = 7
    }
} 