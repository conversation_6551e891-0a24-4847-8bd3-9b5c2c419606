using KvFnB.Core.Contracts;
using System.Data;

namespace KvFnB.Core.Abstractions
{
    public interface IQueryService
    {
        /// <summary>
        /// Executes a query based on the provided query specification without paging.
        /// If T has a TenantId property and enforceTenantFilter is true,
        /// a default filter (RetailerId = @tenantId) will be appended.
        /// </summary>
        Task<IEnumerable<T>> QueryAsync<T>(IQueryBuilder querySpec, bool enforceTenantFilter = true);

        /// <summary>
        /// Executes a query based on the provided query specification without paging.
        /// If T has a TenantId property and enforceTenantFilter is true,
        /// a default filter (RetailerId = @tenantId) will be appended.
        /// </summary>
        Task<T> QueryFirstAsync<T>(IQueryBuilder querySpec, bool enforceTenantFilter = true);

        /// <summary>
        /// Executes a stored procedure and returns the results.
        /// This method automatically sets the command type to StoredProcedure.
        /// </summary>
        Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string storedProcedureName, object parameters);

        /// <summary>
        /// Executes a raw SQL query and returns the results.
        /// This method allows direct execution of plain text SQL queries with parameters.
        /// </summary>
        Task<IEnumerable<T>> QueryPlainTextAsync<T>(string rawQuery, object parameters);
        
        /// <summary>
        /// Executes a query based on the provided query specification without paging.
        /// If T has a TenantId property and enforceTenantFilter is true,
        /// a default filter (RetailerId = @tenantId) will be appended.
        /// </summary>
        Task<T?> QueryFirstOrDefaultAsync<T>(IQueryBuilder querySpec, bool enforceTenantFilter = true);

        /// <summary>
        /// Executes a query and returns a single scalar value of type T.
        /// If enforceTenantFilter is true, a default filter (RetailerId = @tenantId) will be appended.
        /// </summary>
        Task<T?> ExecuteScalarAsync<T>(IQueryBuilder querySpec, bool enforceTenantFilter = true);

        /// <summary>
        /// Executes a paged query based on the provided query specification.
        /// Returns a PagedResult containing the total count and the current page items.
        /// </summary>
        Task<PagingResponse<T>> QueryPagedAsync<T>(IQueryBuilder querySpec, int page, int pageSize, bool enforceTenantFilter = true);

        Task ExecutePlainTextAsync(string rawQuery, object parameters);

    }

    /// <summary>
    /// Interface for querying data from the master database without tenant filtering
    /// </summary>
    public interface IMasterQueryService 
    {
        /// <summary>
        /// Executes a query based on the provided query specification without paging.
        /// If T has a TenantId property and enforceTenantFilter is true,
        /// a default filter (RetailerId = @tenantId) will be appended.
        /// </summary>
        Task<T?> QueryFirstOrDefaultAsync<T>(IQueryBuilder querySpec);
    }
}
