using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Enums;
using Microsoft.Extensions.Options;
using KvFnB.Modules.DataManagement.Domain.Configurations;
using Microsoft.Extensions.Logging;
using KvFnB.Modules.DataManagement.Application.Dtos.CompleteProcessingJob;
using Newtonsoft.Json;
using KvFnB.Modules.DataManagement.Domain.Models;

using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Application.Dtos;
using Microsoft.Extensions.DependencyInjection;
using KvFnB.Core.Authentication;
using KvFnB.Shared.Persistence.ShardingDb;
using System.Text.Json;
using MongoDB.Driver.Linq;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob
{
    public class CompleteProcessingJobUseCase
    {
        private readonly IValidator<CompleteProcessingJobRequest> _validator;
        private readonly IDataManagementQueryService _queryService;
        private readonly IOptions<CompleteDataProcessConfiguration> _jobSettings;
        private readonly ILogger<CompleteProcessingJobUseCase> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        public CompleteProcessingJobUseCase(
            IValidator<CompleteProcessingJobRequest> validator,
            IDataManagementQueryService queryService,
            IOptions<CompleteDataProcessConfiguration> jobSettings,
            ILogger<CompleteProcessingJobUseCase> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _jobSettings = jobSettings ?? throw new ArgumentNullException(nameof(jobSettings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
        }

        public async Task<Core.Contracts.Result<CompleteProcessingJobResponse>> ExecuteAsync(
            CompleteProcessingJobRequest request,
            CancellationToken cancellationToken = default)
        {
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Core.Contracts.Result<CompleteProcessingJobResponse>.Failure(validationResult.Errors);
            }

            var response = new CompleteProcessingJobResponse();

            try
            {
                // Get jobs that have been processed but not marked as complete yet
                var jobsToComplete = await GetJobsToCompleteAsync(request.BatchSize);
                
                if (jobsToComplete.Count == 0)
                {
                    return Core.Contracts.Result<CompleteProcessingJobResponse>.Success(response);
                }
                response.UpdatedJobsCount = jobsToComplete.Count;

                foreach (var job in jobsToComplete)
                {
                    try
                    {
                        await InitScope(job, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        // only log error to continue to next job
                        _logger.LogError(ex, "Error Completed job status");
                    }
                }

                return Core.Contracts.Result<CompleteProcessingJobResponse>.Success(response);
            }
            catch (Exception)
            {
                return Core.Contracts.Result<CompleteProcessingJobResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }

        private async Task InitScope(CompleteProcessingDataJobDto job, CancellationToken cancellationToken)
        {
            var retailerInfo = await GetRetailerInfo(job.RetailerId, cancellationToken);
            if (retailerInfo == null) return;

            using var scope = _serviceScopeFactory.CreateScope();
            var tenantProvider = scope.ServiceProvider.GetRequiredService<ITenantProvider>();
            var adminUser = await GetAdminUserAsync(job.RetailerId, cancellationToken);

            var authUser = scope.ServiceProvider.GetRequiredService<IInitUserInfo>();
            authUser.InitUserInfo(adminUser.Value, job.RetailerId);

            tenantProvider.InitTenantInfo(retailerInfo.GroupId, retailerInfo.Id);
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            await HandleCompleteSingleJob(scope, job, cancellationToken);
            await unitOfWork.CommitAsync(cancellationToken);
        }

        private async Task<List<CompleteProcessingDataJobDto>> GetJobsToCompleteAsync(
            int batchSize)
        {
            string sql;
            object parameters;
            
            // Query for both completed processing jobs and stale jobs
            // Query only for completed processing jobs
            sql = @"
            SELECT TOP (@BatchSize) 
                        Id
                        ,[RequestId]
                        ,[RetailerId]
                        ,[BranchId]
                        ,[Status]
                        ,[CreatedDate]
            FROM [dbo].[ProcessingDataJob] p
            where p.[Status] < 2
            and NOT EXISTS (
                select 1 from DeleteDataDetail d
                where d.ProcessingDataJobId = p.Id
                and d.[Status] NOT IN (1,2)
            )";
            
            parameters = new
            {
                BatchSize = batchSize
            };
            
            return (await _queryService.QueryPlainTextAsync<CompleteProcessingDataJobDto>(sql, parameters)).ToList();
        }

        private async Task HandleCompleteSingleJob(IServiceScope scope, CompleteProcessingDataJobDto job, CancellationToken cancellationToken)
        {
            string sql = @"
            SELECT 
                Id,
                RequestId,
                BranchId,
                RetailerId,
                Status,
                FilterConditions
            FROM [dbo].[DeleteDataDetail]
            WHERE [ProcessingDataJobId] = @JobId";
            // update status to completed
            object parameters = new { JobId = job.Id };
            var deletedDetails = (await _queryService.QueryPlainTextAsync<CompleteDeleteDataDetailDto>(sql, parameters)).ToList();
            if(deletedDetails == null || deletedDetails.Count == 0)
            {
                await UpdateProcessingDataJobStatusAsync(scope, job.Id, ProcessingDataJobStatus.Failed, cancellationToken);
                return;
            }

            var requestId = deletedDetails.First().RequestId;
            var requestSql = @"
            SELECT TOP (1)
                Id,
                Email
            FROM [dbo].[DeleteDataRequest]
            WHERE [Id] = @RequestId";
            var request = (await _queryService.QueryPlainTextAsync<CompleteDeleteDataRequestDto>(requestSql, new { RequestId = requestId }, cancellationToken)).FirstOrDefault();   
            
            if(request == null)
            {
                throw new InvalidDataException("Không tìm thấy yêu cầu xoá dữ liệu");
            }

            var status = deletedDetails.All(d => d.Status == (int)DataDeletionDetailJobStatus.Completed) ? 
                            ProcessingDataJobStatus.Completed : 
                            ProcessingDataJobStatus.Failed;
            await UpdateProcessingDataJobStatusAsync(scope, job.Id, status, cancellationToken);

            await WriteDeleteDataHistoryAsync(scope, job, request.Email ?? "", status, deletedDetails, cancellationToken);

            if(status == ProcessingDataJobStatus.Completed && request.Email != null)
            {
                var retailer = await GetRetailerInfo(job.RetailerId, cancellationToken);
                    await SendCompletionNotificationAsync(scope, request.Email, retailer.CompanyName, job.CreatedDate, GetDeletedDataEndDate(deletedDetails.First().FilterConditions, GetOptions()), cancellationToken);
            }
        }

        private static JsonSerializerOptions GetOptions()
        {
            return new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        private static DateTime GetDeletedDataEndDate(string filterCondition, JsonSerializerOptions options) {
            var filterConditionObject = System.Text.Json.JsonSerializer.Deserialize<FilterCondition>(filterCondition, options)
                   ?? new FilterCondition();
            return filterConditionObject.ToDate ?? DateTime.Now;
        }

        private async Task<RetailerDto> GetRetailerInfo(int retailerId, CancellationToken cancellationToken) {
            string sql = @"
            SELECT TOP 1 [CompanyName], [GroupId], [Id]
            FROM [dbo].[Retailer]
            WHERE [Id] = @RetailerId";
            var parameters = new { RetailerId = retailerId };
            return (await _queryService.QueryPlainTextAsync<RetailerDto>(sql, parameters, cancellationToken)).FirstOrDefault() ?? new RetailerDto();
        }   

        private async Task WriteDeleteDataHistoryAsync(IServiceScope scope, CompleteProcessingDataJobDto job, string email, ProcessingDataJobStatus status, List<CompleteDeleteDataDetailDto> deletedDetails, CancellationToken cancellationToken)
        {
            var deleteDataHistoryRepository = scope.ServiceProvider.GetRequiredService<IDeleteDataHistoryRepository>();
            var summary = status == ProcessingDataJobStatus.Completed ? "Đã xoá thành công" : "Đã xoá thất bại";
            var systemUserId = await GetAdminUserAsync(job.RetailerId, cancellationToken);
            if(systemUserId == null)
            {
                throw new InvalidDataException("Không tìm thấy user admin");
            }
            var deleteDataHistory = new DeleteDataHistory
            {
                TenantId = job.RetailerId,
                RequestId = job.RequestId,
                BranchId = job.BranchId,
                Summary = summary.ToString(),
                ExecutedBy = systemUserId.Value,
                ExecutedDate = job.CreatedDate,
                FilterConditions = deletedDetails.First().FilterConditions,
                Status = (byte)status,
                Email = email,
                ProcessingDataJobId = job.Id
            };

            await deleteDataHistoryRepository.AddAsync(deleteDataHistory, cancellationToken);
        }
        private async Task<int?> GetAdminUserAsync(int retailerId, CancellationToken cancellationToken) {
            string sql = @"
            SELECT TOP 1 [Id]
            FROM [dbo].[User]
            WHERE [RetailerId] = @RetailerId
            AND [IsAdmin] = 1";
            var parameters = new { RetailerId = retailerId };
            return (await _queryService.QueryPlainTextAsync<int>(sql, parameters, cancellationToken)).FirstOrDefault();
        }

        private static async Task UpdateProcessingDataJobStatusAsync(IServiceScope scope, long jobId, ProcessingDataJobStatus status, CancellationToken cancellationToken)
        {
            var processingDataJobRepository = scope.ServiceProvider.GetRequiredService<IProcessingDataJobRepository>();
            var job = await processingDataJobRepository.GetAsync(jobId, cancellationToken);
            if(job == null)
            {
                throw new InvalidDataException("Không tìm thấy job");
            }
            job.UpdateStatus((byte)status);
            await processingDataJobRepository.UpdateAsync(job, cancellationToken);
        }   

        private async Task SendCompletionNotificationAsync(IServiceScope scope, string userEmail, string shopName, DateTime executionDate, DateTime? endDate, CancellationToken cancellationToken)
        {
            var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();
            if (string.IsNullOrEmpty(userEmail))
            {
                return;
            }

            // Get email configuration
            var templateId = _jobSettings.Value.EmailNotification.TemplateId;
            var fromEmail = _jobSettings.Value.EmailNotification.FromEmail;
            var fromName = _jobSettings.Value.EmailNotification.FromName;

            // Prepare template data
            var templateData = new
            {
                StoreName = shopName,
                DeletionDateTime = executionDate.ToString("dd/MM/yyyy"),
                EndDate = endDate?.ToString("dd/MM/yyyy"),
            };

            try
            {
                // Send email
                await emailService.SendTemplateEmailAsync(
                    userEmail,
                    templateId,
                    templateData,
                    fromEmail,
                    fromName,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send completion notification email");
            }
        }
    }
} 