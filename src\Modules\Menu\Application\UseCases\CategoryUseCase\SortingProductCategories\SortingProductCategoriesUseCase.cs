using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;

namespace KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.SortingProductCategories
{
    public class SortingProductCategoriesUseCase : UseCaseBase<SortingProductCategoriesRequest, SortingProductCategoriesResponse>
    {
        private readonly ICategoryDomainService _categoryDomainService;

        public SortingProductCategoriesUseCase(ICategoryDomainService categoryDomainService)
        {
            _categoryDomainService = categoryDomainService;
        }

        public async override Task<Result<SortingProductCategoriesResponse>> ExecuteAsync(SortingProductCategoriesRequest request, CancellationToken cancellationToken = default)
        {
            // Validate request
            if (request.CategoryRanks == null || request.CategoryRanks.Count == 0)
            {
                return Result<SortingProductCategoriesResponse>.Failure("Category ranks are required");
            }

            // Perform sorting logic, forwarding the cancellationToken parameter
            var result = await _categoryDomainService.UpdateCategoryRanksAsync(request.CategoryRanks, cancellationToken);

            if (result)
            {
                return Result<SortingProductCategoriesResponse>.Success(new SortingProductCategoriesResponse(true, "Categories sorted successfully."));
            }
            else
            {
                return Result<SortingProductCategoriesResponse>.Failure("Failed to sort categories.");
            }
        }
    }
}