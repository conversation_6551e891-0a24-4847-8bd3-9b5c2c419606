using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Tests.Helpers;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Shared.Localization;
using KvFnB.Shared.MultiTenancy;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity
{
    public class UpdateProductOnHandQuantityUseCaseTests
    {
        private readonly Mock<IValidator<UpdateProductOnHandQuantityRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly TenantConfiguration _tenantConfiguration;
        private readonly ILocalizationProvider _multiLang;
        private readonly UpdateProductOnHandQuantityUseCase _useCase;

        public UpdateProductOnHandQuantityUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<UpdateProductOnHandQuantityRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _tenantConfiguration = new TenantConfiguration();
            _multiLang = TestLocalizationHelper.CreateVietnameseLocalizationProvider();

            _useCase = new UpdateProductOnHandQuantityUseCase(
                _validatorMock.Object,
                _productRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _tenantProviderMock.Object,
                _tenantConfiguration,
                _multiLang);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1,
                OnHandQuantity = 10
            };
            
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.NotNull(result.ValidationErrors);
            Assert.Contains("Validation error", result.ValidationErrors);
            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenBranchNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1,
                OnHandQuantity = 10
            };
            
            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            
            // Return null for branch ID to simulate branch not found
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns((long?)null);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);
            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1,
                OnHandQuantity = 10
            };
            
            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1L);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            // Don't check exact error message content as it may vary based on language
            Assert.NotNull(result.ErrorMessage);
            Assert.NotEmpty(result.ErrorMessage);
        }


        [Fact]
        public async Task ExecuteAsync_WhenProductBranchNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1,
                OnHandQuantity = 10
            };

            long branchId = 2;
            
            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(branchId);
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            // No product branch is added for branch ID 2
            
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);

            // Act
            var exception = await Assert.ThrowsAsync<NullReferenceException>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldUpdateProductOnHandQuantity()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1, 
                OnHandQuantity = 10,
                MinQuantity = 5,
                MaxQuantity = 20,
                Cost = 8.99m
            };

            int branchId = 2;
            
            // Create a real Product instance
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            // Add a branch to the product
            var productBranch = ProductBranch.Create(_tenantProviderMock.Object.GetTenantId() ?? 0, branchId, 5, 15);
            product.AddInventoryOnBranch(productBranch);
            _tenantConfiguration.UseAvgCost = false;
            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns((long)branchId);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>())).ReturnsAsync(product);
            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            Assert.Equal(request.OnHandQuantity, result.Value.OnHandQuantity);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            Assert.Equal(request.MinQuantity, result.Value.MinQuantity);
            Assert.Equal(request.MaxQuantity, result.Value.MaxQuantity);
            Assert.Equal(request.Cost, result.Value.Cost);
            
            _productRepositoryMock.Verify(r => r.UpdateAsync(product, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductOnHandQuantityRequest
            {
                ProductId = 1,
                OnHandQuantity = 10
            };
            
            var validationResult = new ValidationResult(true, []);
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1L);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var exception = await Assert.ThrowsAsync<Exception>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
        }
    }
} 