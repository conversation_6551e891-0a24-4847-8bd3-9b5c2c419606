# Technical Implementation: Data Processing Service

## 1. Solution Architecture

### 1.1. Core Components

```
+-------------------------------+
|                               |
|     DataProcessingUseCase     |
|                               |
+---------------+---------------+
                |
                v
+-------------------------------+
|                               |
| IDataProcessingStrategyFactory|
|                               |
+---------------+---------------+
                |
                v
+-------------------------------+     +-------------------------------+
|                               |     |                               |
| AbstractDataProcessingStrategy|<----+ Concrete Strategy Implementations|
|                               |     |                               |
+-------------------------------+     +-------------------------------+
                |
                v
+-------------------------------+
|                               |
|     IDempotentService         |
|                               |
+-------------------------------+
```

### 1.2. Core Services

- **DataProcessingUseCase**: Main entry point for processing data requests
- **IDataProcessingStrategyFactory**: Factory to create appropriate strategy based on job type
- **AbstractDataProcessingStrategy**: Base class for all processing strategies
- **IDempotentService**: Ensures each message is processed exactly once

## 2. Implementation Details

### 2.1. Domain Models

#### 2.1.1. DataProcessingJob

```csharp
public class DataProcessingJob
{
    public long Id { get; set; }
    public long RequestId { get; set; }
    public int RetailerId { get; set; }
    public string Type { get; set; } // Delete, Anonymize, Archive, etc.
    public string Status { get; set; } // Pending, Processing, Completed, Failed
    public DateTime CreatedDate { get; set; }
    public DateTime? ProcessedDate { get; set; }
}
```

#### 2.1.2. DataProcessingDetail

```csharp
public class DataProcessingDetail
{
    public long Id { get; set; }
    public long JobId { get; set; }
    public long RequestId { get; set; }
    public string TableName { get; set; }
    public int RecordsProcessed { get; set; }
    public int Status { get; set; } // 0: Pending, 1: Processed, 2: Failed, 3: Restored
    public DateTime CreatedDate { get; set; }
    public DateTime? ProcessedDate { get; set; }
}
```

#### 2.1.3. DataProcessingMessage

```csharp
public class DataProcessingMessage
{
    public long Id { get; set; }
    public long RequestId { get; set; }
    public int RetailerId { get; set; }
    public string Type { get; set; }
    public Dictionary<string, object> Parameters { get; set; }
}
```

### 2.2. Strategy Pattern Implementation

#### 2.2.1. AbstractDataProcessingStrategy

```csharp
public abstract class AbstractDataProcessingStrategy
{
    protected readonly ILogger _logger;
    protected readonly DbContext _dbContext;
    
    public AbstractDataProcessingStrategy(ILogger logger, DbContext dbContext)
    {
        _logger = logger;
        _dbContext = dbContext;
    }
    
    public abstract Task<Result<ProcessingResult>> ProcessAsync(DataProcessingJob job, CancellationToken cancellationToken = default);
    
    protected async Task<int> LogDetailAsync(DataProcessingDetail detail)
    {
        _dbContext.Add(detail);
        return await _dbContext.SaveChangesAsync();
    }
    
    protected long CalculateNegativeRetailerId(int retailerId, long requestId)
    {
        return -1 * (Math.Abs(retailerId) * 10000 + (requestId % 10000));
    }
}
```

#### 2.2.2. DeleteDataStrategy

```csharp
public class DeleteDataStrategy : AbstractDataProcessingStrategy
{
    public DeleteDataStrategy(ILogger<DeleteDataStrategy> logger, DbContext dbContext) 
        : base(logger, dbContext)
    {
    }
    
    public override async Task<Result<ProcessingResult>> ProcessAsync(DataProcessingJob job, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting delete data processing for job {JobId}", job.Id);
            
            var result = new ProcessingResult
            {
                JobId = job.Id,
                ProcessedTables = new Dictionary<string, int>()
            };
            
            // Get list of tables to process
            var tables = GetTablesForRetailer(job.RetailerId);
            
            // Calculate negative retailerId for deletion
            long negativeRetailerId = CalculateNegativeRetailerId(job.RetailerId, job.RequestId);
            
            foreach (var table in tables)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var processedCount = await UpdateRetailerIdForTable(table, job.RetailerId, negativeRetailerId);
                
                if (processedCount > 0)
                {
                    result.ProcessedTables.Add(table, processedCount);
                    result.TotalRecordsProcessed += processedCount;
                    
                    // Log processing detail
                    await LogDetailAsync(new DataProcessingDetail
                    {
                        JobId = job.Id,
                        RequestId = job.RequestId,
                        TableName = table,
                        RecordsProcessed = processedCount,
                        Status = 1, // Processed
                        CreatedDate = DateTime.UtcNow,
                        ProcessedDate = DateTime.UtcNow
                    });
                }
            }
            
            _logger.LogInformation("Completed delete data processing for job {JobId}. Total records: {TotalRecords}", 
                job.Id, result.TotalRecordsProcessed);
                
            return Result<ProcessingResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing delete data for job {JobId}", job.Id);
            return Result<ProcessingResult>.Failure(ErrorMessages.DataProcessingFailed);
        }
    }
    
    private List<string> GetTablesForRetailer(int retailerId)
    {
        // This would be implemented to return list of tables that contain RetailerId column
        // In a real implementation, this could come from a configuration or schema analysis
        return new List<string>
        {
            "Customers",
            "Orders",
            "Invoices",
            "Transactions",
            "ProductInventory"
        };
    }
    
    private async Task<int> UpdateRetailerIdForTable(string tableName, int originalRetailerId, long negativeRetailerId)
    {
        string query = $@"
            UPDATE [{tableName}]
            SET RetailerId = @NegativeRetailerId
            WHERE RetailerId > 0
            AND RetailerId = @OriginalRetailerId";
            
        var parameters = new 
        {
            NegativeRetailerId = negativeRetailerId,
            OriginalRetailerId = originalRetailerId
        };
        
        return await _dbContext.Database.ExecuteSqlRawAsync(query, parameters);
    }
}
```

#### 2.2.3. Strategy Factory

```csharp
public interface IDataProcessingStrategyFactory
{
    AbstractDataProcessingStrategy CreateStrategy(string type);
}

public class DataProcessingStrategyFactory : IDataProcessingStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;
    
    public DataProcessingStrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }
    
    public AbstractDataProcessingStrategy CreateStrategy(string type)
    {
        return type.ToLowerInvariant() switch
        {
            "delete" => _serviceProvider.GetRequiredService<DeleteDataStrategy>(),
            "anonymize" => _serviceProvider.GetRequiredService<AnonymizeDataStrategy>(),
            "archive" => _serviceProvider.GetRequiredService<ArchiveDataStrategy>(),
            "transform" => _serviceProvider.GetRequiredService<TransformDataStrategy>(),
            "merge" => _serviceProvider.GetRequiredService<MergeDataStrategy>(),
            _ => throw new NotSupportedException($"Unsupported data processing type: {type}")
        };
    }
}
```

### 2.3. Idempotent Service

```csharp
public interface IIdempotentService
{
    Task<bool> IsProcessedAsync(int retailerId, long requestId, long id);
    Task MarkAsProcessedAsync(int retailerId, long requestId, long id);
}

public class IdempotentService : IIdempotentService
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<IdempotentService> _logger;
    
    public IdempotentService(IDistributedCache cache, ILogger<IdempotentService> logger)
    {
        _cache = cache;
        _logger = logger;
    }
    
    public async Task<bool> IsProcessedAsync(int retailerId, long requestId, long id)
    {
        string key = GenerateKey(retailerId, requestId, id);
        var result = await _cache.GetStringAsync(key);
        return result != null;
    }
    
    public async Task MarkAsProcessedAsync(int retailerId, long requestId, long id)
    {
        string key = GenerateKey(retailerId, requestId, id);
        await _cache.SetStringAsync(key, 
            DateTime.UtcNow.ToString("O"), 
            new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(30)
            });
    }
    
    private string GenerateKey(int retailerId, long requestId, long id)
    {
        return $"DataProcessing-{retailerId}-{requestId}-{id}";
    }
}
```

### 2.4. Data Processing UseCase

```csharp
public class DataProcessingUseCase
{
    private readonly ILogger<DataProcessingUseCase> _logger;
    private readonly DbContext _dbContext;
    private readonly IIdempotentService _idempotentService;
    private readonly IDataProcessingStrategyFactory _strategyFactory;
    
    public DataProcessingUseCase(
        ILogger<DataProcessingUseCase> logger,
        DbContext dbContext,
        IIdempotentService idempotentService,
        IDataProcessingStrategyFactory strategyFactory)
    {
        _logger = logger;
        _dbContext = dbContext;
        _idempotentService = idempotentService;
        _strategyFactory = strategyFactory;
    }
    
    public async Task<Result<ProcessingResult>> ProcessJobAsync(DataProcessingMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if already processed (idempotency)
            if (await _idempotentService.IsProcessedAsync(message.RetailerId, message.RequestId, message.Id))
            {
                _logger.LogInformation("Message already processed: {RetailerId}-{RequestId}-{Id}", 
                    message.RetailerId, message.RequestId, message.Id);
                    
                return Result<ProcessingResult>.Success(new ProcessingResult
                {
                    JobId = message.Id,
                    AlreadyProcessed = true
                });
            }
            
            // Create or get job
            var job = await GetOrCreateJobAsync(message);
            
            // Update job status to Processing
            job.Status = "Processing";
            await _dbContext.SaveChangesAsync();
            
            // Get appropriate strategy
            var strategy = _strategyFactory.CreateStrategy(message.Type);
            
            // Process job
            var result = await strategy.ProcessAsync(job, cancellationToken);
            
            if (result.IsSuccess)
            {
                // Update job status
                job.Status = "Completed";
                job.ProcessedDate = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync();
                
                // Mark as processed for idempotency
                await _idempotentService.MarkAsProcessedAsync(message.RetailerId, message.RequestId, message.Id);
                
                return result;
            }
            else
            {
                // Update job with error
                job.Status = "Failed";
                job.ErrorMessage = result.Error;
                job.FailedCount++;
                await _dbContext.SaveChangesAsync();
                
                return result;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing job {Id} for retailer {RetailerId}", 
                message.Id, message.RetailerId);
                
            return Result<ProcessingResult>.Failure(ErrorMessages.InternalServerError);
        }
    }
    
    private async Task<DataProcessingJob> GetOrCreateJobAsync(DataProcessingMessage message)
    {
        var existingJob = await _dbContext.Set<DataProcessingJob>()
            .FirstOrDefaultAsync(j => j.Id == message.Id);
            
        if (existingJob != null)
            return existingJob;
            
        var newJob = new DataProcessingJob
        {
            Id = message.Id,
            RequestId = message.RequestId,
            RetailerId = message.RetailerId,
            Type = message.Type,
            Status = "Pending",
            CreatedDate = DateTime.UtcNow,
            Parameters = message.Parameters
        };
        
        _dbContext.Add(newJob);
        await _dbContext.SaveChangesAsync();
        
        return newJob;
    }
}
```

### 2.5. Supporting Classes

```csharp
public class Result<T>
{
    public bool IsSuccess { get; private set; }
    public T Value { get; private set; }
    public string Error { get; private set; }

    private Result(bool isSuccess, T value, string error)
    {
        IsSuccess = isSuccess;
        Value = value;
        Error = error;
    }

    public static Result<T> Success(T value) => new Result<T>(true, value, null);
    public static Result<T> Failure(string error) => new Result<T>(false, default, error);
}

public class ProcessingResult
{
    public long JobId { get; set; }
    public bool AlreadyProcessed { get; set; }
    public int TotalRecordsProcessed { get; set; }
    public Dictionary<string, int> ProcessedTables { get; set; } = new Dictionary<string, int>();
}

public static class ErrorMessages
{
    public const string InternalServerError = "An internal server error occurred while processing the request.";
    public const string DataProcessingFailed = "Data processing operation failed.";
    public const string InvalidJobType = "Invalid or unsupported job type.";
    public const string JobNotFound = "The specified job was not found.";
}
```

## 3. Testing Strategy

### 3.1. Unit Tests for DataProcessingUseCase

```csharp
public class DataProcessingUseCaseTests
{
    private Mock<ILogger<DataProcessingUseCase>> _loggerMock;
    private Mock<DbContext> _dbContextMock;
    private Mock<IIdempotentService> _idempotentServiceMock;
    private Mock<IDataProcessingStrategyFactory> _strategyFactoryMock;
    private DataProcessingUseCase _useCase;
    
    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<DataProcessingUseCase>>();
        _dbContextMock = new Mock<DbContext>();
        _idempotentServiceMock = new Mock<IIdempotentService>();
        _strategyFactoryMock = new Mock<IDataProcessingStrategyFactory>();
        
        _useCase = new DataProcessingUseCase(
            _loggerMock.Object,
            _dbContextMock.Object,
            _idempotentServiceMock.Object,
            _strategyFactoryMock.Object);
    }
    
    [Test]
    public async Task ProcessJobAsync_AlreadyProcessed_ReturnsSuccessWithAlreadyProcessedFlag()
    {
        // Arrange
        var message = new DataProcessingMessage
        {
            Id = 1,
            RetailerId = 123,
            RequestId = 456,
            Type = "Delete"
        };
        
        _idempotentServiceMock.Setup(s => s.IsProcessedAsync(message.RetailerId, message.RequestId, message.Id))
            .ReturnsAsync(true);
            
        // Act
        var result = await _useCase.ProcessJobAsync(message);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsTrue(result.Value.AlreadyProcessed);
        Assert.AreEqual(message.Id, result.Value.JobId);
    }
    
    [Test]
    public async Task ProcessJobAsync_NewJob_CreatesJobAndProcesses()
    {
        // Arrange
        var message = new DataProcessingMessage
        {
            Id = 1,
            RetailerId = 123,
            RequestId = 456,
            Type = "Delete"
        };
        
        var job = new DataProcessingJob
        {
            Id = message.Id,
            RetailerId = message.RetailerId,
            RequestId = message.RequestId,
            Type = message.Type,
            Status = "Pending"
        };
        
        var dbSetMock = new Mock<DbSet<DataProcessingJob>>();
        dbSetMock.Setup(m => m.FirstOrDefaultAsync(It.IsAny<Expression<Func<DataProcessingJob, bool>>>(), default))
            .ReturnsAsync((DataProcessingJob)null);
            
        _dbContextMock.Setup(c => c.Set<DataProcessingJob>())
            .Returns(dbSetMock.Object);
            
        _dbContextMock.Setup(c => c.Add(It.IsAny<DataProcessingJob>()))
            .Callback<DataProcessingJob>(j => job = j);
            
        var strategyMock = new Mock<AbstractDataProcessingStrategy>();
        strategyMock.Setup(s => s.ProcessAsync(It.IsAny<DataProcessingJob>(), default))
            .ReturnsAsync(Result<ProcessingResult>.Success(new ProcessingResult
            {
                JobId = message.Id,
                TotalRecordsProcessed = 10
            }));
            
        _strategyFactoryMock.Setup(f => f.CreateStrategy(message.Type))
            .Returns(strategyMock.Object);
            
        _idempotentServiceMock.Setup(s => s.IsProcessedAsync(message.RetailerId, message.RequestId, message.Id))
            .ReturnsAsync(false);
            
        // Act
        var result = await _useCase.ProcessJobAsync(message);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.AreEqual(10, result.Value.TotalRecordsProcessed);
        Assert.AreEqual("Completed", job.Status);
        
        _idempotentServiceMock.Verify(s => s.MarkAsProcessedAsync(message.RetailerId, message.RequestId, message.Id), Times.Once);
    }
}
```

### 3.2. Unit Tests for DeleteDataStrategy

```csharp
public class DeleteDataStrategyTests
{
    private Mock<ILogger<DeleteDataStrategy>> _loggerMock;
    private Mock<DbContext> _dbContextMock;
    private DeleteDataStrategy _strategy;
    
    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<DeleteDataStrategy>>();
        _dbContextMock = new Mock<DbContext>();
        
        var databaseMock = new Mock<DatabaseFacade>(_dbContextMock.Object);
        _dbContextMock.Setup(c => c.Database).Returns(databaseMock.Object);
        
        _strategy = new DeleteDataStrategy(_loggerMock.Object, _dbContextMock.Object);
    }
    
    [Test]
    public async Task ProcessAsync_WhenCalled_UpdatesRetailerIdInAllTables()
    {
        // Arrange
        var job = new DataProcessingJob
        {
            Id = 1,
            RetailerId = 123,
            RequestId = 456,
            Type = "Delete"
        };
        
        _dbContextMock.Setup(c => c.Database.ExecuteSqlRawAsync(
            It.IsAny<string>(), 
            It.Is<object[]>(p => p.Length == 2)))
            .ReturnsAsync(5); // 5 records updated
            
        // Act
        var result = await _strategy.ProcessAsync(job);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.AreEqual(25, result.Value.TotalRecordsProcessed); // 5 tables * 5 records
        Assert.AreEqual(5, result.Value.ProcessedTables.Count);
        
        // Verify SQL was executed for each table
        _dbContextMock.Verify(c => c.Database.ExecuteSqlRawAsync(
            It.IsAny<string>(), 
            It.Is<object[]>(p => p.Length == 2)), 
            Times.Exactly(5));
    }
    
    [Test]
    public async Task ProcessAsync_WhenExceptionOccurs_ReturnsFailureResult()
    {
        // Arrange
        var job = new DataProcessingJob
        {
            Id = 1,
            RetailerId = 123,
            RequestId = 456,
            Type = "Delete"
        };
        
        _dbContextMock.Setup(c => c.Database.ExecuteSqlRawAsync(
            It.IsAny<string>(), 
            It.Is<object[]>(p => p.Length == 2)))
            .ThrowsAsync(new DbUpdateException("Database error"));
            
        // Act
        var result = await _strategy.ProcessAsync(job);
        
        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual(ErrorMessages.DataProcessingFailed, result.Error);
    }
}
```

### 3.3. Unit Tests for IdempotentService

```csharp
public class IdempotentServiceTests
{
    private Mock<IDistributedCache> _cacheMock;
    private Mock<ILogger<IdempotentService>> _loggerMock;
    private IdempotentService _service;
    
    [SetUp]
    public void Setup()
    {
        _cacheMock = new Mock<IDistributedCache>();
        _loggerMock = new Mock<ILogger<IdempotentService>>();
        _service = new IdempotentService(_cacheMock.Object, _loggerMock.Object);
    }
    
    [Test]
    public async Task IsProcessedAsync_WhenKeyExists_ReturnsTrue()
    {
        // Arrange
        int retailerId = 123;
        long requestId = 456;
        long id = 789;
        string expectedKey = $"DataProcessing-{retailerId}-{requestId}-{id}";
        
        _cacheMock.Setup(c => c.GetStringAsync(expectedKey, default))
            .ReturnsAsync("2023-01-01T00:00:00.0000000Z");
            
        // Act
        bool result = await _service.IsProcessedAsync(retailerId, requestId, id);
        
        // Assert
        Assert.IsTrue(result);
    }
    
    [Test]
    public async Task IsProcessedAsync_WhenKeyDoesNotExist_ReturnsFalse()
    {
        // Arrange
        int retailerId = 123;
        long requestId = 456;
        long id = 789;
        string expectedKey = $"DataProcessing-{retailerId}-{requestId}-{id}";
        
        _cacheMock.Setup(c => c.GetStringAsync(expectedKey, default))
            .ReturnsAsync((string)null);
            
        // Act
        bool result = await _service.IsProcessedAsync(retailerId, requestId, id);
        
        // Assert
        Assert.IsFalse(result);
    }
    
    [Test]
    public async Task MarkAsProcessedAsync_SetsValueInCache()
    {
        // Arrange
        int retailerId = 123;
        long requestId = 456;
        long id = 789;
        string expectedKey = $"DataProcessing-{retailerId}-{requestId}-{id}";
        
        // Act
        await _service.MarkAsProcessedAsync(retailerId, requestId, id);
        
        // Assert
        _cacheMock.Verify(c => c.SetStringAsync(
            expectedKey,
            It.IsAny<string>(),
            It.Is<DistributedCacheEntryOptions>(o => o.AbsoluteExpirationRelativeToNow.HasValue),
            default), 
            Times.Once);
    }
}
```

## 4. How to Implement

To implement this data processing service, follow these steps:

1. **Create Core Models and Interfaces**:
   - Create the domain models (DataProcessingJob, DataProcessingDetail, DataProcessingMessage)
   - Create the Result<T> and ProcessingResult classes for handling responses
   - Create ErrorMessages constants for standardized error handling

2. **Implement Idempotent Service**:
   - Create the IIdempotentService interface and implementation
   - Configure distributed cache for storage

3. **Implement Data Processing Strategies**:
   - Create the AbstractDataProcessingStrategy base class
   - Implement concrete strategies for different processing types (DeleteDataStrategy, etc.)
   - Create the strategy factory

4. **Implement Data Processing UseCase**:
   - Create the DataProcessingUseCase class to orchestrate processing
   - Implement job creation, processing, and status updates

5. **Configure Dependency Injection**:
   ```csharp
   services.AddScoped<IIdempotentService, IdempotentService>();
   services.AddScoped<IDataProcessingStrategyFactory, DataProcessingStrategyFactory>();
   services.AddScoped<DeleteDataStrategy>();
   services.AddScoped<AnonymizeDataStrategy>();
   services.AddScoped<ArchiveDataStrategy>();
   services.AddScoped<TransformDataStrategy>();
   services.AddScoped<MergeDataStrategy>();
   services.AddScoped<DataProcessingUseCase>();
   ```

6. **Create Message Consumer**:
   - Implement a Kafka consumer to receive processing messages
   - Connect to the DataProcessingUseCase for processing

7. **Implement Unit Tests**:
   - Create unit tests for each component
   - Use mocking to isolate each component for testing

## 5. Extensions and Future Improvements

1. **Batch Processing**:
   - Implement batch processing for large datasets
   - Add pagination and chunking to reduce memory usage

2. **Retry Mechanism**:
   - Implement RetryHandler for retrying failed operations
   - Add exponential backoff for retries

3. **Monitoring and Logging**:
   - Add structured logging for all operations
   - Implement metrics collection for monitoring

4. **Data Recovery**:
   - Implement data restoration functionality
   - Add transaction support for atomicity

5. **Performance Optimizations**:
   - Implement parallel processing for multiple tables
   - Use table partitioning for faster updates