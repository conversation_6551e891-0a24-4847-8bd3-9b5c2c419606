# S3 Amazon Service Technical Specification

## 1. Overview

This document outlines the technical specification for developing an Amazon S3 file upload service using .NET 8. The service will provide a robust, scalable solution for uploading files to Amazon S3 buckets, with support for various file types, sizes, and security configurations. It also includes support for S3-compatible storage services and proxy configurations.

## 2. System Requirements

### 2.1 Functional Requirements

- Upload files to specified S3 buckets
- Support for metadata attachment to uploaded files
- File validation (size, type, etc.)
- Support for folder structure within buckets
- Configurable file permissions and storage classes
- Support for encryption (server-side and client-side)
- Success/failure notification mechanism
- Support for S3-compatible storage services (e.g., MinIO, Wasabi, Ceph)
- Support for proxy configurations for environments with restricted internet access
- CloudFront URL support for CDN integration and object key extraction

### 2.2 Non-Functional Requirements

- **Performance**: Optimize for large file uploads (>100MB)
- **Security**: Enforce proper authentication and authorization
- **Reliability**: Handle network interruptions and provide retry mechanisms
- **Scalability**: Support concurrent uploads
- **Maintainability**: Follow SOLID principles and clean architecture

## 3. Architecture

### 3.1 High-Level Architecture

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Client/API     │──────│  S3 Service     │──────│  Amazon S3      │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
                                 │
                                 │
                                 ▼
                         ┌─────────────────┐
                         │                 │
                         │  CloudFront CDN │
                         │                 │
                         └─────────────────┘
```

### 3.2 Component Design

- **IS3Service Interface**: Defines the contract for S3 operations
- **S3Service Implementation**: Core implementation of S3 upload functionality
- **S3Configuration**: Settings and configuration for S3 connectivity
- **File Validators**: Components for validating file properties
- **S3 Response Models**: DTOs for standardized responses

## 4. Technical Design

### 4.1 Service Interface

```csharp
public interface IS3Service
{
    Task<S3UploadResult> UploadFileAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
    
    Task<S3UploadResult> UploadFileAsync(string filePath, string key, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
    
    Task<bool> DeleteFileAsync(string key, CancellationToken cancellationToken = default);
    
    Task<Stream> DownloadFileAsync(string key, CancellationToken cancellationToken = default);
    
    string GetObjectKey(string objectUrl, string cloudfrontUrl);
    
    string GetObjectKey(string objectUrl);
}
```

### 4.2 Implementation Details

#### 4.2.1 Required NuGet Packages

- `AWSSDK.S3` - Core S3 SDK
- `AWSSDK.Extensions.NETCore.Setup` - For .NET configuration integration
- `Microsoft.Extensions.Options` - For options pattern

#### 4.2.2 S3 Configuration Model

```csharp
public class S3Configuration
{
    public string BucketName { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string AccessKey { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public bool UseInstanceProfile { get; set; } = false;
    
    // S3-compatible storage settings
    public bool UseCompatibleMode { get; set; } = false;
    public string? CompatibleEndpoint { get; set; }
    public string? CompatibleAccessKey { get; set; }
    public string? CompatibleSecretKey { get; set; }
    public bool ForcePathStyle { get; set; } = false;
    
    // Proxy configuration
    public string? ProxyUrl { get; set; }
    
    public string? BaseKeyPrefix { get; set; }
    public int UploadPartSize { get; set; } = 5 * 1024 * 1024; // Default: 5MB
    public S3CannedACL DefaultACL { get; set; } = S3CannedACL.Private;
    public S3StorageClass StorageClass { get; set; } = S3StorageClass.Standard;
    public ServerSideEncryptionMethod EncryptionMethod { get; set; } = ServerSideEncryptionMethod.None;
    public string? CacheControlHeader { get; set; }
    
    // CloudFront configuration
    public string? CloudfrontUrl { get; set; }
}
```

#### 4.2.3 Response Models

```csharp
public class S3UploadResult
{
    public bool Success { get; set; }
    public string? Key { get; set; }
    public string? BucketName { get; set; }
    public string? ETag { get; set; }
    public string? VersionId { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
    public string? PublicUrl { get; set; }
}
```

### 4.3 Implementation Class

```csharp
public class S3Service : IS3Service
{
    private readonly S3Configuration _config;
    private readonly ILogger<S3Service> _logger;
    private IAmazonS3? _s3Client;

    public S3Service(
        IOptions<S3Configuration> config,
        ILogger<S3Service> logger)
    {
        _config = config.Value;
        _logger = logger;
        
        if (string.IsNullOrEmpty(_config.BucketName))
        {
            _logger.LogError("S3 bucket name is not configured");
            return;
        }

        try
        {
            _s3Client = GetS3Client();
            _logger.LogDebug("S3 client initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize S3 client");
            // We allow the S3Client to be null and handle it in each method
        }
    }
    
    protected virtual IAmazonS3 GetS3Client()
    {
        // Initialize S3 client based on configuration
        var s3Config = new AmazonS3Config
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(_config.Region)
        };
        
        // Configure for S3-compatible storage
        if (_config.UseCompatibleMode)
        {
            if (string.IsNullOrEmpty(_config.CompatibleEndpoint))
            {
                throw new InvalidOperationException("CompatibleEndpoint must be set when UseCompatibleMode is true");
            }

            // Configure S3 client for compatibility mode
            s3Config.ServiceURL = _config.CompatibleEndpoint;
            s3Config.ForcePathStyle = _config.ForcePathStyle;
        }

        // Configure proxy if specified
        if (!string.IsNullOrEmpty(_config.ProxyUrl))
        {
            try
            {
                // Parse the proxy URL to extract host and port
                var proxyUri = new Uri(_config.ProxyUrl);
                s3Config.ProxyHost = proxyUri.Host;
                s3Config.ProxyPort = proxyUri.Port > 0 ? proxyUri.Port : 80;
            }
            catch (UriFormatException ex)
            {
                throw new InvalidOperationException($"Invalid proxy URL format: {_config.ProxyUrl}", ex);
            }
        }
        
        // Create the S3 client with the proper credentials
        if (_config.UseInstanceProfile)
        {
            return new AmazonS3Client(s3Config);
        }
        else if (_config.UseCompatibleMode)
        {
            if (string.IsNullOrEmpty(_config.CompatibleAccessKey) || string.IsNullOrEmpty(_config.CompatibleSecretKey))
            {
                throw new InvalidOperationException("CompatibleAccessKey and CompatibleSecretKey must be set when UseCompatibleMode is true");
            }

            return new AmazonS3Client(
                _config.CompatibleAccessKey,
                _config.CompatibleSecretKey,
                s3Config);
        }
        else
        {
            return new AmazonS3Client(
                _config.AccessKey,
                _config.SecretKey,
                s3Config);
        }
    }
    
    /// <summary>
    /// Extracts the object key from a CloudFront URL
    /// </summary>
    /// <param name="objectUrl">The full URL of the object, including the CloudFront domain</param>
    /// <param name="cloudfrontUrl">The base CloudFront URL</param>
    /// <returns>The object key or empty string if the URL doesn't start with the CloudFront URL</returns>
    public string GetObjectKey(string objectUrl, string cloudfrontUrl)
    {
        return !objectUrl.StartsWith(cloudfrontUrl) ? string.Empty : objectUrl.Replace(cloudfrontUrl, string.Empty);
    }
    
    /// <summary>
    /// Extracts the object key from a CloudFront URL using the configured CloudFront URL
    /// </summary>
    /// <param name="objectUrl">The full URL of the object, including the CloudFront domain</param>
    /// <returns>The object key or empty string if the URL doesn't match the configured CloudFront URL</returns>
    public string GetObjectKey(string objectUrl)
    {
        if (string.IsNullOrEmpty(_config.CloudfrontUrl))
        {
            _logger.LogError("CloudFront URL is not configured");
            return string.Empty;
        }
        
        return GetObjectKey(objectUrl, _config.CloudfrontUrl);
    }
}
```

## 5. Security Considerations

### 5.1 Authentication and Authorization

- Use IAM roles and policies for S3 access
- Implement least privilege principle
- Rotate access keys regularly
- Consider using AWS Security Token Service (STS) for temporary credentials

### 5.2 Data Protection

- Implement server-side encryption (SSE-S3, SSE-KMS, or SSE-C)
- Enable bucket versioning for critical data
- Configure appropriate bucket policies
- Implement access logging

### 5.3 Proxy and Compatibility Mode Considerations

- When using proxy configuration, ensure the proxy is secure and trusted
- For S3-compatible storage services, validate the endpoint is using HTTPS
- Use credentials specific to S3-compatible services rather than reusing AWS credentials
- When using ForcePathStyle, be aware that bucket names are exposed in the URL path
- Test the compatibility mode with the specific S3-compatible service to ensure proper functionality
- Consider using AWS PrivateLink for secure connections to S3 instead of internet-based proxies when possible
- When `UseCompatibleMode` is set to true, files are automatically uploaded with `S3CannedACL.PublicRead` permissions to ensure accessibility with S3-compatible storage services

### 5.4 CloudFront Considerations

- Properly configure CloudFront distribution with appropriate security settings
- Use Origin Access Identity (OAI) or Origin Access Control (OAC) to restrict direct S3 bucket access
- Consider implementing signed URLs for private content
- Implement appropriate cache control headers based on content type
- Ensure the CloudfrontUrl configuration property ends with a trailing slash for proper object key extraction

## 6. Integration Guide

### 6.1 Dependency Injection

```csharp
public static class S3ServiceExtensions
{
    public static IServiceCollection AddS3Service(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<S3Configuration>(configuration.GetSection("S3"));
        services.AddSingleton<IS3Service, S3Service>();
        return services;
    }
}
```

### 6.2 Configuration Example

```json
{
  "S3": {
    "BucketName": "my-application-bucket",
    "Region": "us-east-1",
    "AccessKey": "", 
    "SecretKey": "",
    "UseInstanceProfile": true,
    "UseCompatibleMode": false,
    "CompatibleEndpoint": null,
    "CompatibleAccessKey": null,
    "CompatibleSecretKey": null,
    "ForcePathStyle": false,
    "ProxyUrl": null,
    "BaseKeyPrefix": "uploads/",
    "UploadPartSize": 5242880,
    "DefaultACL": "Private",
    "StorageClass": "Standard",
    "EncryptionMethod": "AES256",
    "CacheControlHeader": "max-age=86400",
    "CloudfrontUrl": "https://cdn.example.com/"
  }
}
```

For S3-compatible storage services:

```json
{
  "S3": {
    "BucketName": "my-application-bucket",
    "Region": "us-east-1",
    "UseInstanceProfile": false,
    "UseCompatibleMode": true,
    "CompatibleEndpoint": "https://minio.example.com",
    "CompatibleAccessKey": "minio-access-key",
    "CompatibleSecretKey": "minio-secret-key",
    "ForcePathStyle": true,
    "BaseKeyPrefix": "uploads/",
    "CacheControlHeader": "no-cache",
    "CloudfrontUrl": "https://cdn.example.com/"
    // Note: When UseCompatibleMode is true, files are automatically uploaded with PublicRead permissions
  }
}
```

For environments requiring proxy:

```json
{
  "S3": {
    "BucketName": "my-application-bucket",
    "Region": "us-east-1",
    "AccessKey": "aws-access-key", 
    "SecretKey": "aws-secret-key",
    "ProxyUrl": "http://proxy.example.com:8080",
    "BaseKeyPrefix": "uploads/",
    "CacheControlHeader": "public, max-age=31536000",
    "CloudfrontUrl": "https://cdn.example.com/"
  }
}
```

## 7. Usage Examples

### 7.1 Simple File Upload

```csharp
public class FileService
{
    private readonly IS3Service _s3Service;
    
    public FileService(IS3Service s3Service)
    {
        _s3Service = s3Service;
    }
    
    public async Task<string> UploadUserFileAsync(IFormFile file, string userId)
    {
        // Generate unique file key
        string fileKey = $"users/{userId}/{Guid.NewGuid()}/{file.FileName}";
        
        // Add metadata
        var metadata = new Dictionary<string, string>
        {
            { "UploadedBy", userId },
            { "OriginalName", file.FileName },
            { "ContentType", file.ContentType }
        };
        
        using (var stream = file.OpenReadStream())
        {
            var result = await _s3Service.UploadFileAsync(stream, fileKey, file.ContentType, metadata);
            
            if (!result.Success)
            {
                throw new Exception($"Failed to upload file: {result.ErrorMessage}");
            }
            
            // Note: If S3Configuration.UseCompatibleMode is true, the file will automatically 
            // have public read access (S3CannedACL.PublicRead)
            
            return result.Key!;
        }
    }
}
```

### 7.2 CloudFront URL to Object Key Conversion

```csharp
public class AssetService
{
    private readonly IS3Service _s3Service;
    
    public AssetService(IS3Service s3Service)
    {
        _s3Service = s3Service;
    }
    
    public async Task<bool> DeleteAssetByUrlAsync(string assetUrl)
    {
        // Extract the object key from the CloudFront URL
        string objectKey = _s3Service.GetObjectKey(assetUrl);
        
        if (string.IsNullOrEmpty(objectKey))
        {
            // Not a valid CloudFront URL
            return false;
        }
        
        // Delete the asset using the extracted key
        return await _s3Service.DeleteFileAsync(objectKey);
    }
    
    public async Task<Stream> DownloadAssetByUrlAsync(string assetUrl)
    {
        // Extract the object key from the CloudFront URL
        string objectKey = _s3Service.GetObjectKey(assetUrl);
        
        if (string.IsNullOrEmpty(objectKey))
        {
            // Not a valid CloudFront URL
            throw new ArgumentException("Invalid CloudFront URL", nameof(assetUrl));
        }
        
        // Download the asset using the extracted key
        return await _s3Service.DownloadFileAsync(objectKey);
    }
}
```

## 8. Error Handling

- Implement global error handling middleware
- Log S3 errors with appropriate detail
- Map S3 exceptions to user-friendly messages
- Implement retry policies for transient failures

## 9. Monitoring and Logging

- Log all S3 operations with correlation IDs
- Track upload/download metrics
- Monitor S3 costs and usage
- Set up alerts for error thresholds

## 10. Testing Strategy

### 10.1 Unit Testing

- Test service methods using mocked S3 client
- Validate error handling paths
- Test validation logic
- Test CloudFront URL to object key conversion

### 10.2 Integration Testing

- Test against a real S3 bucket (dev/test environment)
- Verify upload/download functionality
- Test with actual CloudFront distribution

## 11. Performance Considerations

- Implement concurrent uploads for multiple files
- Monitor and optimize data transfer costs
- Configure appropriate cache settings for CloudFront distribution

## 12. Deployment Considerations

- Configure appropriate IAM roles for the application
- Set up proper bucket policies and CORS configuration
- Consider regional deployment for improved latency
- Implement infrastructure as code for S3 resources
- Configure CloudFront distribution with appropriate settings
- Set up proper DNS records for CloudFront URLs

## 13. Implementation Plan

1. Set up S3 bucket and IAM roles
2. Implement core S3 service
3. Add file validation
4. Configure CloudFront distribution
5. Implement CloudFront URL to object key conversion
6. Implement proper error handling and logging
7. Create unit and integration tests
8. Document usage examples

## 14. References

- [AWS SDK for .NET Documentation](https://docs.aws.amazon.com/sdk-for-net/index.html)
- [S3 Best Practices](https://docs.aws.amazon.com/AmazonS3/latest/userguide/optimizing-performance.html)
- [S3 Security Best Practices](https://docs.aws.amazon.com/AmazonS3/latest/userguide/security-best-practices.html)
- [CloudFront Developer Guide](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/Introduction.html)
