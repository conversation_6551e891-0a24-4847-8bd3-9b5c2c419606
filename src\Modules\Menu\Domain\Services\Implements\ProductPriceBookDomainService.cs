using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;

namespace KvFnB.Modules.Menu.Domain.Services.Implements
{
    /// <summary>
    /// Domain service that handles the relationship between products and price books
    /// </summary>
    public class ProductPriceBookDomainService : IProductPriceBookDomainService
    {
        /// <summary>
        /// Updates prices for a product across multiple price books
        /// </summary>
        /// <param name="product">The product being updated</param>
        /// <param name="priceBooks">The collection of price books</param>
        /// <param name="priceDetails">The collection of price details to update</param>
        /// <param name="removeOthers">If true, removes any price details not included in the priceDetails collection</param>
        /// <returns>The collection of updated price books</returns>
        public IEnumerable<PriceBook> UpdateProductPrices(
            Product product, 
            IEnumerable<PriceBook> priceBooks, 
            IEnumerable<ProductPriceDetail> priceDetails,
            bool removeOthers = false)
        {
            var priceBookMap = priceBooks.ToDictionary(pb => pb.Id);
            var updatedPriceBooks = new HashSet<PriceBook>();
            var requestedPriceBookIds = priceDetails.Select(d => d.PriceBookId).ToHashSet();
            
            // First, update or add price details specified in the request
            foreach (var detail in priceDetails)
            {
                if (!priceBookMap.TryGetValue(detail.PriceBookId, out var priceBook))
                {
                    throw new InvalidOperationException($"Price book with ID {detail.PriceBookId} not found.");
                }
                
                if (priceBook.HasProductPrice(product.Id))
                {
                    priceBook.UpdateProductPrice(product.Id, detail.Price);
                }
                else
                {
                    priceBook.AddProductPrice(product.Id, detail.Price);
                }
                
                updatedPriceBooks.Add(priceBook);
            }
            
            // If removeOthers is true, remove price details not in the request
            if (removeOthers)
            {
                foreach (var priceBook in priceBooks)
                {
                    // Skip price books that were already updated`
                    if (requestedPriceBookIds.Contains(priceBook.Id))
                        continue;
                    
                    // If this price book has a price for the product but wasn't in the request, remove it
                    if (priceBook.HasProductPrice(product.Id))
                    {
                        priceBook.RemoveProductPrice(product.Id);
                        updatedPriceBooks.Add(priceBook);
                    }
                }
            }
            
            return updatedPriceBooks;
        }
    }
    
    /// <summary>
    /// Data transfer object for product price details
    /// </summary>
    public class ProductPriceDetail
    {
        /// <summary>
        /// The ID of the price book
        /// </summary>
        public long PriceBookId { get; set; }
        
        /// <summary>
        /// The price of the product in this price book
        /// </summary>
        public decimal Price { get; set; }
    }
} 