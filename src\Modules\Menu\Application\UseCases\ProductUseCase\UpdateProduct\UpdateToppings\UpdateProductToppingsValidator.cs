using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings
{
    /// <summary>
    /// Validates the UpdateProductToppings request
    /// </summary>
    public class UpdateProductToppingsValidator : Validator<UpdateProductToppingsRequest>
    {
        public UpdateProductToppingsValidator(ILocalizationProvider multiLang)
        {
            // Rule for Id: Must not be null and greater than 0
            RuleFor(x => x.ProductId)
                .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_required))
                .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_more_than_zero));

            // Rule for ToppingProductIds: Must not be null
            RuleFor(x => x.ToppingProductIds)
                .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_toppings_required))
                .Must(x => x != null && x.Count != 0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_toppings_min_items));
        }
    }
} 