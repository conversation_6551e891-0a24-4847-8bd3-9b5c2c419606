# Kafka Background Service Implementation Guide

## Prerequisites

- Add KiotVietFnB.Kafka package reference to your project:
```xml
<PackageReference Include="KiotVietFnB.Kafka" Version="6.9.0" />
```

## 1. Create Kafka Consumer Handler

Create a class that implements `IConsumeHandler<TKey, TValue>`:

```csharp
using Confluent.Kafka;
using KiotVietFnB.Kafka.AsyncConsumer.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace YourNamespace.ConsumerWorkers
{
    public class YourEventConsumerHandler : IConsumeHandler<string, string>
    {
        private readonly ILogger<YourEventConsumerHandler> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        
        public YourEventConsumerHandler(
            ILogger<YourEventConsumerHandler> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
        }

        public async Task HandleAsync(ConsumeResult<string, string> message, CancellationToken cancellationToken)
        {
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                try
                {
                    // Process message here
                    _logger.LogInformation($"Processing message: {message.Message.Value}");
                    
                    // Your business logic
                    await Task.CompletedTask;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing message");
                    throw; // Rethrow to let the consumer handle retry/DLQ
                }
            }
        }
    }
}
```

## 2. Create Background Service

Create a class that extends `BackgroundService` to run the Kafka consumer:

```csharp
using KiotVietFnB.Kafka.AsyncConsumer.Interfaces;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace YourNamespace.ConsumerWorkers
{
    public class YourConsumerBackground : BackgroundService
    {
        private readonly ILogger<YourConsumerBackground> _logger;
        private readonly IKafkaConsumer<string, string> _kafkaConsumer;
        
        public YourConsumerBackground(
            IKafkaConsumer<string, string> kafkaConsumer,
            ILogger<YourConsumerBackground> logger)
        {
            _kafkaConsumer = kafkaConsumer ?? throw new ArgumentNullException(nameof(kafkaConsumer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Starting Kafka consumer background service");
                await _kafkaConsumer.RunAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Kafka consumer background service");
            }
        }
    }
}
```

## 3. Configuration Setup

Add Kafka configuration in your `appsettings.json`:

```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      { "Name": "Console" }
    ]
  },
  "Kafka": {
    "Consumer": {
      "BootstrapServers": "kafka-broker1:9092,kafka-broker2:9092",
      "GroupId": "your-consumer-group",
      "AutoOffsetReset": "earliest",
      "EnableAutoCommit": false,
      "SessionTimeoutMs": 30000,
      "MaxPollIntervalMs": 300000,
      "EnablePartitionEof": false,
      "StatisticsIntervalMs": 5000
    },
    "Topics": {
      "YourEventTopic": "your-event-topic-name",
      "YourEventTopicDlq": "your-event-topic-name-dlq"
    },
    "RetryPolicy": {
      "MaxRetryCount": 3,
      "RetryIntervalInMs": 1000
    }
  },
  "YourService": {
    "MaxTimeConsumEventInMs": 30000
  }
}
```

## 4. Register Services

Update your `Program.cs` to register the Kafka consumer services:

```csharp
using KiotVietFnB.Kafka.AsyncConsumer.Extensions;
using KiotVietFnB.Kafka.AsyncConsumer.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using YourNamespace.ConsumerWorkers;
using YourNamespace.Configuration;

// In your ConfigureServices method:
public static IHostBuilder CreateHostBuilder(IConfiguration configuration, string[] args) =>
    Host.CreateDefaultBuilder(args)
        .ConfigureAppConfiguration(x => x.AddConfiguration(configuration))
        .ConfigureServices((hostContext, services) =>
        {
            // Add Kafka consumer services
            services.AddKafKaEventConsumerConfig(configuration);
            
            // Register your handler
            services.AddSingleton<IConsumeHandler<string, string>, YourEventConsumerHandler>();
            
            // Register background service
            services.AddHostedService<YourConsumerBackground>();
            
            // Optional: Add your service configuration
            services.AddYourServiceConfiguration(configuration);
        });
```

## 5. Adding Service Configuration (Optional)

Create a configuration interface and implementation:

```csharp
// IYourBackgroundServiceConfiguration.cs
namespace YourNamespace.Configuration
{
    public interface IYourBackgroundServiceConfiguration
    {
        int MaxTimeConsumEventInMs { get; }
    }
}

// YourBackgroundServiceConfiguration.cs
using Microsoft.Extensions.Configuration;

namespace YourNamespace.Configuration
{
    public class YourBackgroundServiceConfiguration : IYourBackgroundServiceConfiguration
    {
        public int MaxTimeConsumEventInMs { get; }

        public YourBackgroundServiceConfiguration(IConfiguration configuration)
        {
            var config = configuration.GetSection("YourService");
            MaxTimeConsumEventInMs = config.GetValue<int>("MaxTimeConsumEventInMs");
        }
    }
}
```

Register your configuration:

```csharp
// ServiceCollectionExtensions.cs
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using YourNamespace.Configuration;

namespace YourNamespace.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddYourServiceConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IYourBackgroundServiceConfiguration>(new YourBackgroundServiceConfiguration(configuration));
            return services;
        }
    }
}
```

## 6. Advanced Usage: Processing Events with Projection

For more complex scenarios, you might want to use projections to process events:

```csharp
// In your consumer handler
public async Task HandleAsync(ConsumeResult<string, string> message, CancellationToken cancellationToken)
{
    using (var scope = _serviceScopeFactory.CreateScope())
    {
        var eventMessage = JsonConvert.DeserializeObject<EventMessage>(message.Message.Value);
        // Process event
        
        using (var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(_configuration.MaxTimeConsumEventInMs)))
        {
            try
            {
                // Set context information
                SetupAuthContext(scope, eventMessage);
                
                // Get projector and process event
                var projector = scope.ServiceProvider.GetRequiredService<IProjector<YourProjectionModel>>();
                await projector.ProjectAsync(eventMessage.GetEvent(), cts.Token);
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
                throw; // Rethrow for retry/DLQ handling
            }
        }
    }
}
``` 