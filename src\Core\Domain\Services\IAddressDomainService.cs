using KvFnB.Core.Domain.Models;

namespace KvFnB.Core.Domain.Services
{
    /// <summary>
    /// Domain service interface for address-related operations
    /// </summary>
    public interface IAddressDomainService
    {
        /// <summary>
        /// Saves an address location by either updating an existing address or creating a new one
        /// </summary>
        /// <param name="address">The address entity to save</param>
        /// <param name="cancellationToken">A token to cancel the asynchronous operation</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SaveAddressLocation(Address address, CancellationToken cancellationToken = default);
    }
} 