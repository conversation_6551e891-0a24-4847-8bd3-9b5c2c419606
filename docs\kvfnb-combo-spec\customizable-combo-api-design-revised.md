# Customizable Combo ProductType: API Design

## Document Information
| Document Type | API Design |
| ------------- | ---------- |
| Version       | 1.2        |
| Status        | Updated    |
| Author        | Development Team |
| Last Updated  | April 18, 2024 |

## 1. Introduction

### 1.1 Purpose
This document details the API design for implementing the new customizable combo product type in the KiotViet Product Management System. It outlines the endpoints, data structures, and interactions necessary to support the creation, management, and ordering of customizable combo products.

### 1.2 Scope
This design includes:
- All required API endpoints for managing customizable combos
- Request/response data structures
- Validation requirements
- Integration with existing product management and ordering APIs

## 2. API Design Principles

### 2.1 RESTful Design
The API follows RESTful principles with consistent resource naming, proper HTTP method usage, and standard response formats.

### 2.2 Consistency with Existing APIs
The design maintains consistency with existing KiotViet API patterns for familiarity and ease of integration.

### 2.3 Backward Compatibility
All new endpoints and changes maintain compatibility with existing clients and implementations.

## 3. Base Configuration

### 3.1 Base URL
```
https://[environment].kiotviet.vn/api
```

### 3.2 Authentication
All endpoints require standard KiotViet authentication:
- API Key in the request header
- Retailer identifier in the request header or URL path

### 3.3 Common Headers
```
X-API-Key: [API_KEY]
X-Retailer-ID: [RETAILER_ID]
Content-Type: application/json
Accept: application/json
```

### 3.4 Common Response Format
```json
{
  "data": { /* Response data */ },
  "metadata": {
    "timestamp": "2023-07-15T10:25:43.511Z",
    "requestId": "abcd1234-5678-efgh-9012-ijklmnopqrst"
  },
  "errors": [] // Only present if errors occurred
}
```

## 4. API Endpoints

### 4.1 Get Product Details (Enhanced)

Retrieve a product including customizable combo information when applicable.

#### Request
```
GET /api/products/{id}
```

#### Response (for a customizable combo)
```json
{
  "data": {
    "id": 12345,
    "code": "COMBO-123",
    "name": "Deluxe Meal",
    "productType": 4,
    "basePrice": 120000,
    "isActive": true,
    // ... other standard product fields ...
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.2 Get Customizable Combo Structure

Retrieve the full structure of a customizable combo with all groups and options.

#### Request
```
GET /api/products/{id}/combo
```

#### Response
```json
{
  "product_combo": {
    "id": 12345,
    "code": "COMBO-123",
    "name": "Deluxe Meal",
    "product_type": 4,
    "base_price": 120000,
    "retailer_id": 5000,
    "combo_groups": [
      {
        "id": 101,
        "combo_product_id": 12345,
        "name": "Main Dish",
        "max_quantity": 1,
        "items": [
          {
            "id": 1001,
            "group_id": 101,
            "product_id": 501,
            "product": {
              "id": 501,
              "code": "CHICKEN-01",
              "name": "Grilled Chicken"
            },
            "additional_price": 0
          },
          {
            "id": 1002,
            "group_id": 101,
            "product_id": 502,
            "product": {
              "id": 502,
              "code": "BEEF-01",
              "name": "Beef Steak"
            },
            "additional_price": 30000
          }
        ]
      },
      {
        "id": 102,
        "combo_product_id": 12345,
        "name": "Side Dishes",
        "max_quantity": 2,
        "items": [
          // Side dish items here
        ]
      }
    ]
  }
}
```

### 4.3 Create Customizable Combo Product

Create a new product as a customizable combo. This extends the existing product creation endpoint.

#### Request
```
POST /api/products
```

#### Request Body
```json
{
  "code": "COMBO-NEW",
  "name": "New Customizable Combo",
  "productTypeId": 4,
  "basePrice": 150000,
  "categoryId": 25,
  // ... other standard product fields ...
  "customizableComboGroups": [
    {
      "name": "Main Dish",
      "description": "Choose your main course",
      "max_quantity": 1,
      "sort_order": 1,
      "items": [
        {
          "productId": 501,
          "additionalPrice": 0,
          "sortOrder": 1
        },
        {
          "productId": 502,
          "additionalPrice": 30000,
          "sortOrder": 2
        }
      ]
    }
  ]
}
```

#### Response
```json
{
  "data": {
    "id": 12346,
    "code": "COMBO-NEW",
    "name": "New Customizable Combo",
    "productType": 4,
    "basePrice": 150000,
    // ... other standard product fields ...
  }
}
```

### 4.4 Update Product Combo

Update the combo details of an existing product.

#### Request
```
PUT /api/products/{id}/combo
```

#### Request Body
```json
{
  "combo_groups": [
    {
      "id": 101, // Optional - if provided, updates existing group
      "name": "Main Dish",
      "description": "Choose your main course",
      "max_quantity": 1,
      "sort_order": 1,
      "items": [
        {
          "id": 1001, // Optional - if provided, updates existing item
          "product_id": 501,
          "additional_price": 0,
          "sort_order": 1
        },
        {
          "product_id": 502,
          "additional_price": 30000,
          "sort_order": 2
        }
      ]
    },
    // More groups...
  ]
}
```

#### Response
```json
{
  "product_combo": {
    "id": 12345,
    "code": "COMBO-123",
    "name": "Deluxe Meal",
    "product_type": 4,
    "base_price": 120000,
    "retailer_id": 5000,
    "combo_groups": [
      // Updated combo groups and items
    ]
  }
}
```

### 4.5 Update All Product Details

Update all aspects of a product including basic info and combo details in a single transaction.

#### Request
```
PUT /api/products/{id}
```

#### Request Body
```json
{
  "basic_info": {
    "code": "COMBO-UPD",
    "name": "Updated Combo",
    "basePrice": 160000,
    // ... other basic fields
  },
  "combo": {
    "combo_groups": [
      {
        "id": 101,
        "name": "Main Dish",
        "max_quantity": 1,
        "items": [
          // Items
        ]
      }
    ]
  },
  "images": {
    // Image updates
  },
  "on_hand_quantity": {
    // Quantity updates
  },
  "price_books": {
    // Price book updates
  },
  "toppings": {
    // Topping updates
  },
  "not_allow_sale_branch": [
    // Branch IDs where sales are not allowed
  ]
}
```

#### Response
```json
{
  "data": {
    "id": 12345,
    // ... updated product fields
  }
}
```

### 4.6 Manage Combo Groups

#### 4.6.1 Create Combo Group

#### Request
```
POST /api/products/{productId}/combo-groups
```

#### Request Body
```json
{
  "name": "Main Dish",
  "description": "Choose your main course",
  "max_quantity": 1,
  "sort_order": 1
}
```

#### Response
```json
{
  "data": {
    "id": 104,
    "combo_product_id": 12346,
    "name": "Main Dish",
    "description": "Choose your main course",
    "max_quantity": 1,
    "sort_order": 1,
    "created_at": "2024-03-20T10:30:00.000Z"
  },
  "metadata": { /* standard metadata */ }
}
```

#### 4.6.2 Update Combo Group

#### Request
```
PUT /api/products/{productId}/combo-groups/{groupId}
```

#### Request Body
```json
{
  "name": "Main Dish Selection",
  "description": "Choose your main course",
  "minQuantity": 1,
  "maxQuantity": 1,
  "isRequired": true,
  "sortOrder": 1
}
```

#### Response
```json
{
  "data": {
    "id": 104,
    "productId": 12346,
    "name": "Main Dish Selection",
    "description": "Choose your main course",
    "minQuantity": 1,
    "maxQuantity": 1,
    "isRequired": true,
    "sortOrder": 1,
    "modifiedDate": "2023-07-15T11:15:00.000Z"
  },
  "metadata": { /* standard metadata */ }
}
```

#### 4.6.3 Delete Combo Group

#### Request
```
DELETE /api/products/{productId}/combo-groups/{groupId}
```

#### Response
```json
{
  "data": {
    "success": true,
    "message": "Group successfully deleted"
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.7 Manage Combo Group Items

#### 4.7.1 Add Item to Group

#### Request
```
POST /api/products/{productId}/combo-groups/{groupId}/items
```

#### Request Body
```json
{
  "componentProductId": 501,
  "additionalPrice": 0,
  "defaultQuantity": 0,
  "maxQuantity": null,
  "sortOrder": 1
}
```

#### Response
```json
{
  "data": {
    "id": 1005,
    "groupId": 104,
    "componentProductId": 501,
    "componentProductName": "Grilled Chicken",
    "componentProductCode": "CHICKEN-01",
    "additionalPrice": 0,
    "defaultQuantity": 0,
    "maxQuantity": null,
    "sortOrder": 1,
    "createdDate": "2023-07-15T11:30:00.000Z"
  },
  "metadata": { /* standard metadata */ }
}
```

#### 4.7.2 Update Group Item

#### Request
```
PUT /api/products/{productId}/combo-groups/{groupId}/items/{itemId}
```

#### Request Body
```json
{
  "additionalPrice": 15000,
  "defaultQuantity": 1,
  "maxQuantity": 2,
  "sortOrder": 1
}
```

#### Response
```json
{
  "data": {
    "id": 1005,
    "groupId": 104,
    "componentProductId": 501,
    "componentProductName": "Grilled Chicken",
    "componentProductCode": "CHICKEN-01",
    "additionalPrice": 15000,
    "defaultQuantity": 1,
    "maxQuantity": 2,
    "sortOrder": 1,
    "modifiedDate": "2023-07-15T11:45:00.000Z"
  },
  "metadata": { /* standard metadata */ }
}
```

#### 4.7.3 Delete Group Item

#### Request
```
DELETE /api/products/{productId}/combo-groups/{groupId}/items/{itemId}
```

#### Response
```json
{
  "data": {
    "success": true,
    "message": "Item successfully deleted"
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.8 Batch Update Combo Structure

Update an entire combo structure in one call.

#### Request
```
PUT /api/products/{productId}/combo-structure
```

#### Request Body
```json
{
  "groups": [
    {
      "id": 101, // Optional - if provided, updates existing group
      "name": "Main Dish",
      "description": "Choose your main course",
      "minQuantity": 1,
      "maxQuantity": 1,
      "isRequired": true,
      "sortOrder": 1,
      "items": [
        {
          "id": 1001, // Optional - if provided, updates existing item
          "componentProductId": 501,
          "additionalPrice": 0,
          "defaultQuantity": 0,
          "maxQuantity": null,
          "sortOrder": 1
        },
        {
          "componentProductId": 502,
          "additionalPrice": 30000,
          "defaultQuantity": 0,
          "maxQuantity": null,
          "sortOrder": 2
        }
      ]
    },
    // More groups...
  ]
}
```

#### Response
```json
{
  "data": {
    "productId": 12345,
    "message": "Combo structure updated successfully",
    "updatedGroups": 3,
    "updatedItems": 12
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.9 Get All Customizable Combos

Retrieve all customizable combo products.

#### Request
```
GET /api/products?productType=1&isCustomizableCombo=true
```

#### Response
```json
{
  "data": {
    "products": [
      {
        "id": 12345,
        "code": "COMBO-123",
        "name": "Deluxe Meal",
        "productType": 1,
        "basePrice": 120000,
        "isCustomizableCombo": true
      },
      // More products...
    ],
    "pagination": {
      "pageSize": 20,
      "pageNumber": 1,
      "totalItems": 45,
      "totalPages": 3
    }
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.10 Validate Customer Selections

Validate customer selections against combo structure rules.

#### Request
```
POST /api/products/{productId}/validate-selections
```

#### Request Body
```json
{
  "selections": [
    {
      "groupId": 101,
      "items": [
        {
          "itemId": 1001,
          "quantity": 1
        }
      ]
    },
    {
      "groupId": 102,
      "items": [
        {
          "itemId": 1005,
          "quantity": 1
        },
        {
          "itemId": 1006,
          "quantity": 1
        }
      ]
    },
    {
      "groupId": 103,
      "items": [
        {
          "itemId": 1010,
          "quantity": 1
        }
      ]
    }
  ]
}
```

#### Response
```json
{
  "data": {
    "isValid": true,
    "totalPrice": 150000,
    "basePrice": 120000,
    "additionalPrice": 30000,
    "validationMessages": []
  },
  "metadata": { /* standard metadata */ }
}
```

If invalid:
```json
{
  "data": {
    "isValid": false,
    "validationMessages": [
      {
        "groupId": 101,
        "message": "Selection required for 'Main Dish'"
      }
    ]
  },
  "metadata": { /* standard metadata */ }
}
```

### 4.11 Order API Enhancements

The existing order creation API will be enhanced to support customizable combos.

#### Request
```
POST /api/orders
```

#### Request Body (with customizable combo)
```json
{
  "customerId": 5001,
  "items": [
    {
      "productId": 12345,
      "quantity": 1,
      "price": 150000,
      "discount": 0,
      "note": "No spicy",
      "isCustomizableCombo": true,
      "selections": [
        {
          "groupId": 101,
          "items": [
            {
              "productId": 502,
              "quantity": 1,
              "additionalPrice": 30000
            }
          ]
        },
        {
          "groupId": 102,
          "items": [
            {
              "productId": 505,
              "quantity": 1,
              "additionalPrice": 0
            },
            {
              "productId": 507,
              "quantity": 1,
              "additionalPrice": 0
            }
          ]
        },
        {
          "groupId": 103,
          "items": [
            {
              "productId": 510,
              "quantity": 1,
              "additionalPrice": 0
            }
          ]
        }
      ]
    },
    // Other order items...
  ],
  // Other order fields...
}
```

## 5. Data Models

### 5.1 Request/Response Models

#### 5.1.1 ProductComboDto
```csharp
public record ProductComboDto
{
    [JsonPropertyName("id"), Description("The unique identifier of the product.")]
    public long Id { get; init; }

    [JsonPropertyName("code"), Description("The product code.")]
    public string Code { get; init; } = string.Empty;

    [JsonPropertyName("name"), Description("The product name.")]
    public string Name { get; init; } = string.Empty;

    [JsonPropertyName("product_type"), Description("The product type (4 for customizable combos).")]
    public byte ProductType { get; init; } = 4;

    [JsonPropertyName("base_price"), Description("The base price of the combo.")]
    public decimal BasePrice { get; init; }

    [JsonPropertyName("retailer_id"), Description("The retailer ID.")]
    public int RetailerId { get; init; }

    [JsonPropertyName("combo_groups"), Description("Collection of combo groups.")]
    public ICollection<CustomizableComboGroupDto> ComboGroups { get; init; } = new List<CustomizableComboGroupDto>();
}
```

#### 5.1.2 CustomizableComboGroupDto
```csharp
public record CustomizableComboGroupDto
{
    [JsonPropertyName("id"), Description("The unique identifier of the combo group.")]
    public long Id { get; init; }

    [JsonPropertyName("combo_product_id"), Description("The parent product ID of the combo.")]
    public long ComboProductId { get; init; }

    [JsonPropertyName("name"), Description("The name of the combo group.")]
    public string Name { get; init; } = string.Empty;

    [JsonPropertyName("description"), Description("Optional description of the combo group.")]
    public string? Description { get; init; }

    [JsonPropertyName("max_quantity"), Description("Maximum quantity of items that can be selected from this group.")]
    public int? MaxQuantity { get; init; }

    [JsonPropertyName("sort_order"), Description("Sort order for display.")]
    public int SortOrder { get; init; }
    
    [JsonPropertyName("items"), Description("Items in this combo group.")]
    public ICollection<CustomizableComboGroupItemDto> Items { get; init; } = new List<CustomizableComboGroupItemDto>();
}
```

#### 5.1.3 CustomizableComboGroupItemDto
```csharp
public record CustomizableComboGroupItemDto
{
    [JsonPropertyName("id"), Description("The unique identifier of the combo group item.")]
    public long Id { get; init; }

    [JsonPropertyName("group_id"), Description("The group ID this item belongs to.")]
    public long GroupId { get; init; }

    [JsonPropertyName("product_id"), Description("The product ID of the component item.")]
    public long ProductId { get; init; }
    
    [JsonPropertyName("product"), Description("The product info.")]
    public ProductObjectDto Product { get; init; } = new ProductObjectDto();

    [JsonPropertyName("additional_price"), Description("Additional price for this item.")]
    public decimal AdditionalPrice { get; init; }

    [JsonPropertyName("sort_order"), Description("Sort order for display.")]
    public int SortOrder { get; init; }
    
    public record ProductObjectDto
    {
        [JsonPropertyName("id"), Description("The unique identifier of the product.")]
        public long Id { get; init; }
        
        [JsonPropertyName("code"), Description("The product code.")]
        public string Code { get; init; } = string.Empty;
        
        [JsonPropertyName("name"), Description("The product name.")]
        public string Name { get; init; } = string.Empty;
    }
}
```

#### 5.1.4 GetProductComboResponse
```csharp
public record GetProductComboResponse
{
    [JsonPropertyName("product_combo")]
    public ProductComboDto ProductCombo { get; init; } = new ProductComboDto();
}
```

#### 5.1.5 UpdateProductComboRequest
```csharp
public record UpdateProductComboRequest
{
    [JsonIgnore]
    public long ProductId { get; init; }

    [JsonPropertyName("combo_groups"), Description("Collection of combo groups")]
    public ICollection<CustomizableComboGroupDto> ComboGroups { get; init; } = [];
}
```

#### 5.1.6 UpdateProductComboResponse
```csharp
public record UpdateProductComboResponse
{
    [JsonPropertyName("product_combo")]
    public ProductComboDto ProductCombo { get; init; } = new ProductComboDto();
}
```

## 6. Validation Rules

### 6.1 Group Validation
- Name is required and must be between 1-120 characters
- MinQuantity must be a non-negative integer
- MaxQuantity must be a positive integer
- MinQuantity cannot exceed MaxQuantity
- SortOrder must be a non-negative integer

### 6.2 Item Validation
- ComponentProductId must reference an existing Product
- AdditionalPrice must be a non-negative decimal
- DefaultQuantity must be a non-negative integer
- MaxQuantity, if specified, must be a positive integer
- SortOrder must be a non-negative integer

### 6.3 Customer Selection Validation
- All required groups must have at least MinQuantity selections
- No group can have more than MaxQuantity selections
- Selected products must be valid options within their groups
- Total price calculation must be correct

## 7. Error Handling

### 7.1 Common Error Responses

#### 7.1.1 Validation Error
```json
{
  "errors": [
    {
      "code": "VALIDATION_ERROR",
      "message": "Validation failed",
      "details": [
        {
          "field": "groups[0].maxQuantity",
          "message": "Max quantity cannot be less than min quantity"
        }
      ]
    }
  ],
  "metadata": { /* standard metadata */ }
}
```

#### 7.1.2 Resource Not Found
```json
{
  "errors": [
    {
      "code": "RESOURCE_NOT_FOUND",
      "message": "Combo group not found",
      "details": {
        "resourceType": "ComboGroup",
        "resourceId": "104"
      }
    }
  ],
  "metadata": { /* standard metadata */ }
}
```

#### 7.1.3 Permission Error
```json
{
  "errors": [
    {
      "code": "PERMISSION_DENIED",
      "message": "You don't have permission to modify this product"
    }
  ],
  "metadata": { /* standard metadata */ }
}
```

## 8. Versioning Strategy

### 8.1 API Versioning
All new endpoints will be incorporated into the existing API structure without requiring a version change.

### 8.2 Feature Flag
The customizable combo functionality will be controlled by a feature flag to allow gradual rollout.

## 9. API Documentation

### 9.1 Swagger Documentation
All new endpoints will be documented in Swagger with full request/response examples.

### 9.2 Integration Guides
Integration guides will be provided for:
- Creating and managing customizable combos
- Implementing the ordering flow for customizable combos
- Handling inventory for customizable combos

## 10. Transaction Processing

When an order containing a customizable combo is created, the API will:

1. Validate all customer selections against combo structure rules
2. Calculate the total price including any additional charges
3. Create the main order detail record for the combo
4. Create individual order detail records for each selected component
5. Link component records to the parent combo via ToppingParentId
6. Apply inventory adjustments only for the selected components

## 11. Testing Considerations

### 11.1 API Test Cases
- Creating a customizable combo product
- Adding groups and items to a combo
- Retrieving and verifying combo structure
- Placing orders with various selection combinations
- Validating selection rules (min/max quantities)
- Verifying price calculations
- Testing inventory impacts

### 11.2 Performance Tests
- Loading combo structure for large combos with many options
- Processing orders with multiple customizable combos
- Concurrent access to combo definitions

## 12. Implementation Timeline

| Phase | Description | Timeline |
|-------|-------------|----------|
| 1 | API Design and Review | Week 1 |
| 2 | Core API Implementation | Weeks 2-3 |
| 3 | Integration with Ordering System | Week 4 |
| 4 | Testing and Optimization | Week 5 |
| 5 | Documentation and Deployment | Week 6 |

## 13. Conclusion

This API design provides a comprehensive framework for implementing the customizable combo product type in the KiotViet system. The design leverages existing patterns and structures while introducing new capabilities for flexible product configuration and customer selection.

By following this design, developers will be able to build a robust, consistent, and user-friendly API for managing and ordering customizable combo products. 