namespace KvFnB.Core.Abstractions
{
    public interface IDistributedLockProvider
    {
        /// <summary>
        /// Run function in params with lock by lockKey
        /// </summary>
        /// <typeparam name="T1">Param 1</typeparam>
        /// <typeparam name="TResult">Result of action</typeparam>
        /// <param name="lockKey">Lock key</param>
        /// <param name="timeout">Timeout of this lock</param>
        /// <param name="p1"></param>
        /// <param name="action">Func which want lock</param>
        /// <returns></returns>
        Task LockAsync<T1, TResult>(string lockKey, TimeSpan timeout, T1 p1, Func<T1, Task<TResult>> action);

        /// <summary>
        /// Run function in params with lock by lockKey
        /// </summary>
        /// <typeparam name="T1">Param 1</typeparam>
        /// <typeparam name="T2">Param 2</typeparam>
        /// <typeparam name="TResult">Result of action</typeparam>
        /// <param name="lockKey">Lock key</param>
        /// <param name="timeout">Timeout of this lock</param>
        /// <param name="p2"></param>
        /// <param name="action">Func which want lock</param>
        /// <param name="p1"></param>
        /// <returns></returns>
        Task LockAsync<T1, T2, TResult>(string lockKey, TimeSpan timeout, T1 p1, T2 p2, Func<T1, T2, Task<TResult>> action);

        /// <summary>
        /// Run function in params with lock by lockKey
        /// </summary>
        /// <param name="lockKey">Lock key</param>
        /// <param name="timeout">Timeout of this lock</param>
        /// <param name="action">Func which want lock</param>
        /// <returns></returns>
        Task LockAsync(string lockKey, TimeSpan timeout, Action action);

    }
}