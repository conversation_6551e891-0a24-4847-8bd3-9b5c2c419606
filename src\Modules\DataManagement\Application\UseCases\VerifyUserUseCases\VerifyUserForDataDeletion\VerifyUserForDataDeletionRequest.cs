using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.DataManagement.Application.UseCases.VerifyUserUseCases.VerifyUserForDataDeletion
{
    /// <summary>
    /// Request model for user verification for data deletion
    /// </summary>
    public class VerifyUserForDataDeletionRequest
    {
        /// <summary>
        /// The user's password for verification
        /// </summary>
        [JsonPropertyName("password"), Description("The user's password for verification.")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// The user's phone number for verification
        /// </summary>
        [JsonPropertyName("phone_number"), Description("The user's phone number for verification.")]
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// The fingerprint key for device identification
        /// </summary>
        [JsonPropertyName("fingerprint_key"), Description("The fingerprint key for device identification.")]
        public string <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; } = string.Empty;
    }
} 