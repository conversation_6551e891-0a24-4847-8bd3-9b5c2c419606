using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Infrastructure.Persistence;
using KvFnB.Shared.Persistence.ShardingDb;
using KvFnB.Shared.Tests.Persistence.ShardingDb;
using KvFnB.TestUtilities;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Menu.Infrastructure.Tests.Persistence;

[Trait("Type", "Integration")]
[Collection("Sequential")]
public class ProductRepositoryTests : IClassFixture<MsSqlContainerFixtureProvider>
{
    private readonly DbContextOptions<ShardingDbContext>? _dbContextOptions;

    public ProductRepositoryTests(MsSqlContainerFixtureProvider fixtureProvider)
    {
        _dbContextOptions = fixtureProvider.DbContextOptions;
    }

    private static Product CreateDefaultProduct(int categoryId)
    {
        return Product.CreateProduct(
            "P001",
            "Product 1",
            categoryId,
            (byte)ProductTypes.Normal.Id,
            "pcs",
            1.0f,
            "P001");
    }

    [Fact]
    public async Task GetByCategoryIdAsync_ShouldReturnEmptyList_WhenProductCategoryDoesNotExist()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCategoryIdAsync(1000, false, CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByCategoryIdAsync_ShouldReturnProducts_WhenProductCategoryExists()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var category = new Category { Name = "Category 1", ParentId = null };
        context.Set<Category>().Add(category);
        await context.SaveChangesAsync();

        var product1 = Product.CreateProduct(
            "P001",
            "Product 1",
            category.Id,
            (byte)ProductTypes.Normal.Id,
            "pcs",
            1.0f,
            "P001");

        var product2 = Product.CreateProduct(
            "P002",
            "Product 2",
            category.Id,
            (byte)ProductTypes.Normal.Id,
            "pcs",
            1.0f,
            "P002");

        context.Set<Product>().AddRange(product1, product2);
        await context.SaveChangesAsync();

        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCategoryIdAsync(category.Id, false, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count());
        Assert.Contains(result, p => p.Name == "Product 1");
        Assert.Contains(result, p => p.Name == "Product 2");
    }

    [Fact]
    public async Task GetByCategoryIdAsync_ShouldReturnProducts_WithDeletedCategory()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var category = new Category { Name = "Category 1", ParentId = null };
        context.Set<Category>().Add(category);
        await context.SaveChangesAsync();

        var product = Product.CreateProduct(
            "P001",
            "Product 1",
            category.Id,
            (byte)ProductTypes.Normal.Id,
            "pcs",
            1.0f,
            "P001");

        context.Set<Product>().Add(product);
        await context.SaveChangesAsync();

        category.MarkAsDeleted();
        context.Set<Category>().Update(category);
        await context.SaveChangesAsync();

        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCategoryIdAsync(category.Id, false, CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal("Product 1", result.First().Name);
    }

    [Fact]
    public async Task GetByCategoryIdAsync_ShouldReturnEmpty_WithDeletedProducts()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var category = new Category { Name = "Category 1", ParentId = null };
        context.Set<Category>().Add(category);
        await context.SaveChangesAsync();

        var product = CreateDefaultProduct(category.Id);
        product.Delete();

        context.Set<Product>().Add(product);
        await context.SaveChangesAsync();

        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCategoryIdAsync(category.Id, false, CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByCodeAsync_ShouldReturnProduct_WhenProductExists()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var product = CreateDefaultProduct(1);

        context.Set<Product>().Add(product);
        await context.SaveChangesAsync();

        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCodeAsync("P001", CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Product 1", result.Name);
    }

    [Fact]
    public async Task GetByCodeAsync_ShouldReturnNull_WhenProductDoesNotExist()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new ProductRepository(context);

        // Act
        var result = await repository.GetByCodeAsync("NonExistentCode", CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsUniqueProductCodeAsync_ShouldReturnTrue_WhenCodeIsUnique()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new ProductRepository(context);

        // Act
        var result = await repository.IsUniqueProductCodeAsync("UniqueCode", null, CancellationToken.None);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsUniqueProductCodeAsync_ShouldReturnFalse_WhenCodeIsNotUnique()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var category = new Category { Name = "Category 1", ParentId = null };
        context.Set<Category>().Add(category);
        await context.SaveChangesAsync();

        var product = Product.CreateProduct(
            "ExistingCode",
            "Product 1",
            category.Id,
            (byte)ProductTypes.Normal.Id,
            "pcs",
            1.0f,
            "ExistingCode");

        context.Set<Product>().Add(product);
        await context.SaveChangesAsync();

        var repository = new ProductRepository(context);

        // Act
        var result = await repository.IsUniqueProductCodeAsync("ExistingCode", null, CancellationToken.None);

        // Assert
        Assert.False(result);
    }
}