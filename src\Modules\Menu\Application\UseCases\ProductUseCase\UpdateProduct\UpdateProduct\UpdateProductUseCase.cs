using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductDetail;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateImages;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductSaleBranches;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProduct
{
    /// <summary>
    /// Implements the update product use case for updating all aspects of a product within a transaction
    /// </summary>
    public class UpdateProductUseCase : UseCaseBase<UpdateProductRequest, UpdateProductResponse>
    {
        private readonly ILocalizationProvider _multiLang;
        private readonly IValidator<UpdateProductRequest> _validator;
        private readonly IValidator<UpdateProductBasicInfoRequest> _basicInfoValidator;
        private readonly IValidator<UpdateProductComboRequest> _comboValidator;
        private readonly IValidator<UpdateProductImagesRequest> _imagesValidator;
        private readonly IValidator<UpdateProductOnHandQuantityRequest> _onHandQuantityValidator;
        private readonly IValidator<UpdateProductPriceBooksRequest> _priceBooksValidator;
        private readonly IValidator<UpdateProductToppingsRequest> _toppingsValidator;
        private readonly IValidator<UpdateProductSaleBranchesRequest> _saleBranchesValidator;
        private readonly IProductRepository _productRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly UpdateProductBasicInfoUseCase _basicInfoUseCase;
        private readonly UpdateProductComboUseCase _comboUseCase;
        private readonly UpdateProductImagesUseCase _imagesUseCase;
        private readonly UpdateProductOnHandQuantityUseCase _onHandQuantityUseCase;
        private readonly UpdateProductPriceBooksUseCase _priceBooksUseCase;
        private readonly UpdateProductToppingsUseCase _toppingsUseCase;
        private readonly UpdateProductSaleBranchesUseCase _saleBranchesUseCase;
        private readonly GetProductDetailUseCase _getProductDetailUseCase;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductUseCase"/> class.
        /// </summary>
        #pragma warning disable S107 // Methods should not have too many parameters
        public UpdateProductUseCase(
            IValidator<UpdateProductRequest> validator,
            IValidator<UpdateProductBasicInfoRequest> basicInfoValidator,
            IValidator<UpdateProductComboRequest> comboValidator,
            IValidator<UpdateProductImagesRequest> imagesValidator,
            IValidator<UpdateProductOnHandQuantityRequest> onHandQuantityValidator,
            IValidator<UpdateProductPriceBooksRequest> priceBooksValidator,
            IValidator<UpdateProductToppingsRequest> toppingsValidator,
            IValidator<UpdateProductSaleBranchesRequest> saleBranchesValidator,
            IProductRepository productRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            UpdateProductBasicInfoUseCase basicInfoUseCase,
            UpdateProductComboUseCase comboUseCase,
            UpdateProductImagesUseCase imagesUseCase,
            UpdateProductOnHandQuantityUseCase onHandQuantityUseCase,
            UpdateProductPriceBooksUseCase priceBooksUseCase,
            UpdateProductToppingsUseCase toppingsUseCase,
            UpdateProductSaleBranchesUseCase saleBranchesUseCase,
            GetProductDetailUseCase getProductDetailUseCase,
            ILocalizationProvider multiLang,
            ILogger logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _basicInfoValidator = basicInfoValidator ?? throw new ArgumentNullException(nameof(basicInfoValidator));
            _comboValidator = comboValidator ?? throw new ArgumentNullException(nameof(comboValidator));
            _imagesValidator = imagesValidator ?? throw new ArgumentNullException(nameof(imagesValidator));
            _onHandQuantityValidator = onHandQuantityValidator ?? throw new ArgumentNullException(nameof(onHandQuantityValidator));
            _priceBooksValidator = priceBooksValidator ?? throw new ArgumentNullException(nameof(priceBooksValidator));
            _toppingsValidator = toppingsValidator ?? throw new ArgumentNullException(nameof(toppingsValidator));
            _saleBranchesValidator = saleBranchesValidator ?? throw new ArgumentNullException(nameof(saleBranchesValidator));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _basicInfoUseCase = basicInfoUseCase ?? throw new ArgumentNullException(nameof(basicInfoUseCase));
            _comboUseCase = comboUseCase ?? throw new ArgumentNullException(nameof(comboUseCase));
            _imagesUseCase = imagesUseCase ?? throw new ArgumentNullException(nameof(imagesUseCase));
            _onHandQuantityUseCase = onHandQuantityUseCase ?? throw new ArgumentNullException(nameof(onHandQuantityUseCase));
            _priceBooksUseCase = priceBooksUseCase ?? throw new ArgumentNullException(nameof(priceBooksUseCase));
            _toppingsUseCase = toppingsUseCase ?? throw new ArgumentNullException(nameof(toppingsUseCase));
            _saleBranchesUseCase = saleBranchesUseCase ?? throw new ArgumentNullException(nameof(saleBranchesUseCase));
            _getProductDetailUseCase = getProductDetailUseCase ?? throw new ArgumentNullException(nameof(getProductDetailUseCase));
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        #pragma warning restore S107 // Methods should not have too many parameters

        /// <summary>
        /// Executes the update product use case.
        /// </summary>
        /// <param name="request">The request containing all product details to update.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the updated product or error information.</returns>
        #pragma warning disable S3776 // Cognitive Complexity of methods should not be too high
        public override async Task<Result<UpdateProductResponse>> ExecuteAsync(
            UpdateProductRequest request,
            CancellationToken cancellationToken = default)
        {
            // 1. Validate main request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<UpdateProductResponse>.Failure(validationResult.Errors);
            }

            // 2. Get existing product to verify it exists
            var product = await _productRepository.GetAsync(request.Id, cancellationToken);
            if (product == null)
            {
                return Result<UpdateProductResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            }
            
            // 3. Prepare and validate child requests
            var (validationErrors, requestData) = PrepareAndValidateChildRequests(request);
            
            // Return validation errors if any component failed validation
            if (validationErrors.Count != 0)
            {
                _logger.Warning($"Validation errors occurred: {string.Join("; ", validationErrors)}");
                return Result<UpdateProductResponse>.Failure(validationErrors);
            }

            // 4. Begin transaction after all validations have passed
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // 5. Execute updates for all validated components
            var updateResult = await UpdateProductComponentsAsync(request, product, requestData, cancellationToken);
            if (!updateResult.IsSuccess)
            {
                await _unitOfWork.RollbackTransactionAsync(cancellationToken);
                return Result<UpdateProductResponse>.Failure(updateResult.ValidationErrors ??
                    [updateResult.ErrorMessage ?? "Cập nhật sản phẩm không thành công"]);
            }

            // 6. Commit the transaction 
            await _unitOfWork.CommitTransactionAsync(cancellationToken);
            // 7. Get updated product details
            var detailRequest = new GetProductDetailRequest { ProductId = request.Id };
            var detailResult = await _getProductDetailUseCase.ExecuteAsync(detailRequest, cancellationToken);

            if (!detailResult.IsSuccess || detailResult.Value == null)
            {
                _logger.Warning($"Failed to get updated product details: {string.Join(", ", detailResult.ValidationErrors ?? [detailResult.ErrorMessage ?? "Failed to get updated product details"])}");
                return Result<UpdateProductResponse>.Failure(detailResult.ValidationErrors ??
                    [detailResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật sản phẩm"]);
            }

            // 8. Map to response
            var response = _mapper.Map<UpdateProductResponse>(detailResult.Value);

            return Result<UpdateProductResponse>.Success(response);
        }
        
        private sealed record ProductUpdateRequests(
            UpdateProductBasicInfoRequest? BasicInfo,
            UpdateProductComboRequest? Combo,
            UpdateProductImagesRequest? Images,
            UpdateProductOnHandQuantityRequest? OnHandQuantity,
            UpdateProductPriceBooksRequest? PriceBooks,
            UpdateProductToppingsRequest? Toppings,
            UpdateProductSaleBranchesRequest? SaleBranches
        );
        
        private (List<string> ValidationErrors, ProductUpdateRequests Requests) PrepareAndValidateChildRequests(UpdateProductRequest request)
        {
            List<string> validationErrors = new();
            
            // Prepare and validate BasicInfo request
            var basicInfoRequest = PrepareBasicInfoRequest(request, validationErrors);
            
            // Prepare and validate Combo request
            var comboRequest = PrepareComboRequest(request, validationErrors);
            
            // Prepare and validate Images request
            var imagesRequest = PrepareImagesRequest(request, validationErrors);
            
            // Prepare and validate OnHandQuantity request
            var onHandQuantityRequest = PrepareOnHandQuantityRequest(request, validationErrors);
            
            // Prepare and validate PriceBooks request
            var priceBooksRequest = PreparePriceBooksRequest(request, validationErrors);
            
            // Prepare and validate Toppings request
            var toppingsRequest = PrepareToppingsRequest(request, validationErrors);
            
            // Prepare and validate SaleBranches request
            var saleBranchesRequest = PrepareSaleBranchesRequest(request, validationErrors);
            
            var requests = new ProductUpdateRequests(
                basicInfoRequest,
                comboRequest,
                imagesRequest,
                onHandQuantityRequest,
                priceBooksRequest,
                toppingsRequest,
                saleBranchesRequest
            );
            
            return (validationErrors, requests);
        }
        
        private UpdateProductBasicInfoRequest? PrepareBasicInfoRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.BasicInfo == null)
            {
                return null;
            }
            
            var basicInfoRequest = new UpdateProductBasicInfoRequest
            {
                ProductId = request.Id,
                Code = request.BasicInfo.Code,
                Name = request.BasicInfo.Name,
                CategoryId = request.BasicInfo.CategoryId,
                BasePrice = request.BasicInfo.BasePrice,
                Description = request.BasicInfo.Description,
                AllowSale = request.BasicInfo.AllowSale,
                Weight = request.BasicInfo.Weight,
                TaxId = request.BasicInfo.TaxId,
                ProductGroupId = request.BasicInfo.ProductGroupId,
                Unit = request.BasicInfo.Unit,
                ConversionValue = request.BasicInfo.ConversionValue,
                IsFavourite = request.BasicInfo.IsFavourite,
                OrderTemplate = request.BasicInfo.OrderTemplate,
                IsRewardPoint = request.BasicInfo.IsRewardPoint
            };
            
            var basicInfoValidation = _basicInfoValidator.Validate(basicInfoRequest);
            if (!basicInfoValidation.IsValid)
            {
                validationErrors.AddRange(basicInfoValidation.Errors);
            }
            
            return basicInfoRequest;
        }
        
        private UpdateProductComboRequest? PrepareComboRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.Combo == null)
            {
                return null;
            }
            
            var comboRequest = new UpdateProductComboRequest
            {
                ProductId = request.Id,
                ComboGroups = request.Combo.ComboGroups
            };
            
            var comboValidation = _comboValidator.Validate(comboRequest);
            if (!comboValidation.IsValid)
            {
                validationErrors.AddRange(comboValidation.Errors);
            }
            
            return comboRequest;
        }
        
        private UpdateProductImagesRequest? PrepareImagesRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.Images == null)
            {
                return null;
            }
            
            var imagesRequest = new UpdateProductImagesRequest
            {
                ProductId = request.Id,
                Images = request.Images.Images ?? []
            };
            
            var imagesValidation = _imagesValidator.Validate(imagesRequest);
            if (!imagesValidation.IsValid)
            {
                validationErrors.AddRange(imagesValidation.Errors);
            }
            
            return imagesRequest;
        }
        
        private UpdateProductOnHandQuantityRequest? PrepareOnHandQuantityRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.OnHandQuantity == null)
            {
                return null;
            }
            
            var onHandQuantityRequest = new UpdateProductOnHandQuantityRequest
            {
                ProductId = request.Id,
                OnHandQuantity = request.OnHandQuantity.OnHandQuantity,
                MinQuantity = request.OnHandQuantity.MinQuantity,
                MaxQuantity = request.OnHandQuantity.MaxQuantity,
                Cost = request.OnHandQuantity.Cost,
                InventoryTrackingIgnore = request.OnHandQuantity.InventoryTrackingIgnore,
                IsFavourite = request.BasicInfo?.IsFavourite
            };
            
            var onHandQuantityValidation = _onHandQuantityValidator.Validate(onHandQuantityRequest);
            if (!onHandQuantityValidation.IsValid)
            {
                validationErrors.AddRange(onHandQuantityValidation.Errors);
            }
            
            return onHandQuantityRequest;
        }
        
        private UpdateProductPriceBooksRequest? PreparePriceBooksRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.PriceBooks == null)
            {
                return null;
            }
            
            var priceBooksRequest = new UpdateProductPriceBooksRequest
            {
                ProductId = request.Id,
                PriceBookDetails = request.PriceBooks.PriceBookDetails
            };
            
            var priceBooksValidation = _priceBooksValidator.Validate(priceBooksRequest);
            if (!priceBooksValidation.IsValid)
            {
                validationErrors.AddRange(priceBooksValidation.Errors);
            }
            
            return priceBooksRequest;
        }
        
        private UpdateProductToppingsRequest? PrepareToppingsRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.Toppings == null)
            {
                return null;
            }
            
            var toppingsRequest = new UpdateProductToppingsRequest
            {
                ProductId = request.Id,
                ToppingProductIds = request.Toppings.ToppingProductIds
            };
            
            var toppingsValidation = _toppingsValidator.Validate(toppingsRequest);
            if (!toppingsValidation.IsValid)
            {
                validationErrors.AddRange(toppingsValidation.Errors);
            }
            
            return toppingsRequest;
        }
        
        private UpdateProductSaleBranchesRequest? PrepareSaleBranchesRequest(UpdateProductRequest request, List<string> validationErrors)
        {
            if (request.NotAllowSaleBranch == null)
            {
                return null;
            }
            
            var saleBranchesRequest = new UpdateProductSaleBranchesRequest
            {
                ProductId = request.Id,
                NotAllowSaleBranch = request.NotAllowSaleBranch,
                AllBranchIds = request.AllBranchIds,
                BranchesWithCreatePermission = request.BranchesWithCreatePermission
            };
            
            var saleBranchesValidation = _saleBranchesValidator.Validate(saleBranchesRequest);
            if (!saleBranchesValidation.IsValid)
            {
                validationErrors.AddRange(saleBranchesValidation.Errors);
            }
            
            return saleBranchesRequest;
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateProductComponentsAsync(
            UpdateProductRequest request,
            Product product,
            ProductUpdateRequests requests,
            CancellationToken cancellationToken)
        {
            // Update basic info if provided
            var basicInfoResult = await UpdateBasicInfoAsync(requests.BasicInfo, cancellationToken);
            if (!basicInfoResult.IsSuccess)
            {
                return basicInfoResult;
            }
            
            // Update combo if provided
            var comboResult = await UpdateComboAsync(requests.Combo, cancellationToken);
            if (!comboResult.IsSuccess)
            {
                return comboResult;
            }
            
            // Update images
            var imagesResult = await UpdateImagesAsync(request, product, requests.Images, cancellationToken);
            if (!imagesResult.IsSuccess)
            {
                return imagesResult;
            }
           
            // Update on-hand quantity if provided
            var onHandQuantityResult = await UpdateOnHandQuantityAsync(requests.OnHandQuantity, cancellationToken);
            if (!onHandQuantityResult.IsSuccess)
            {
                return onHandQuantityResult;
            }
            
            // Update price books if provided
            var priceBooksResult = await UpdatePriceBooksAsync(requests.PriceBooks, cancellationToken);
            if (!priceBooksResult.IsSuccess)
            {
                return priceBooksResult;
            }
            
            // Update toppings if provided
            var toppingsResult = await UpdateToppingsAsync(requests.Toppings, cancellationToken);
            if (!toppingsResult.IsSuccess)
            {
                return toppingsResult;
            }
            
            // Update sale branches if provided
            var saleBranchesResult = await UpdateSaleBranchesAsync(requests.SaleBranches, cancellationToken);
            if (!saleBranchesResult.IsSuccess)
            {
                return saleBranchesResult;
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateBasicInfoAsync(
            UpdateProductBasicInfoRequest? basicInfoRequest,
            CancellationToken cancellationToken)
        {
            if (basicInfoRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var basicInfoResult = await _basicInfoUseCase.ExecuteAsync(basicInfoRequest, cancellationToken);
            if (!basicInfoResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product basic info: {string.Join(", ", basicInfoResult.ValidationErrors ?? [basicInfoResult.ErrorMessage ?? "Failed to update product basic info"])}");
                return Result<UpdateProductResponse>.Failure(basicInfoResult.ValidationErrors ??
                    [basicInfoResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật thông tin sản phẩm"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateComboAsync(
            UpdateProductComboRequest? comboRequest,
            CancellationToken cancellationToken)
        {
            if (comboRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var comboResult = await _comboUseCase.ExecuteAsync(comboRequest, cancellationToken);
            if (!comboResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product combo: {string.Join(", ", comboResult.ValidationErrors ?? [comboResult.ErrorMessage ?? "Failed to update product combo"])}");
                return Result<UpdateProductResponse>.Failure(comboResult.ValidationErrors ??
                    [comboResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật sản phẩm combo"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateImagesAsync(
            UpdateProductRequest request,
            Product product,
            UpdateProductImagesRequest? imagesRequest,
            CancellationToken cancellationToken)
        {
            // Update images if provided
            if (imagesRequest != null)
            {
                var imagesResult = await _imagesUseCase.ExecuteAsync(imagesRequest, cancellationToken);
                if (!imagesResult.IsSuccess)
                {
                    _logger.Warning($"Failed to update product images: {string.Join(", ", imagesResult.ValidationErrors ?? [imagesResult.ErrorMessage ?? "Failed to update product images"])}");
                    return Result<UpdateProductResponse>.Failure(imagesResult.ValidationErrors ??
                        [imagesResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật hình ảnh sản phẩm"]);
                }
            }
            // If Images property is explicitly null, it means we should remove all images
            else if (request.Images == null && product.ProductImages.Any())
            {
                
                // Create an empty images request to clear all images
                var removeImagesRequest = new UpdateProductImagesRequest
                {
                    ProductId = request.Id,
                    Images = []
                };
                
                var removeImagesResult = await _imagesUseCase.ExecuteAsync(removeImagesRequest, cancellationToken);
                if (!removeImagesResult.IsSuccess)
                {
                    _logger.Warning($"Failed to remove product images: {string.Join(", ", removeImagesResult.ValidationErrors ?? [removeImagesResult.ErrorMessage ?? "Failed to remove product images"])}");
                    return Result<UpdateProductResponse>.Failure(removeImagesResult.ValidationErrors ??
                        [removeImagesResult.ErrorMessage ?? "Có lỗi xảy ra khi xóa hình ảnh sản phẩm"]);
                }
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateOnHandQuantityAsync(
            UpdateProductOnHandQuantityRequest? onHandQuantityRequest,
            CancellationToken cancellationToken)
        {
            if (onHandQuantityRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var onHandQuantityResult = await _onHandQuantityUseCase.ExecuteAsync(onHandQuantityRequest, cancellationToken);
            if (!onHandQuantityResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product on-hand quantity: {string.Join(", ", onHandQuantityResult.ValidationErrors ?? [onHandQuantityResult.ErrorMessage ?? "Failed to update product on-hand quantity"])}");
                return Result<UpdateProductResponse>.Failure(onHandQuantityResult.ValidationErrors ??
                    [onHandQuantityResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật số lượng tồn sản phẩm"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdatePriceBooksAsync(
            UpdateProductPriceBooksRequest? priceBooksRequest,
            CancellationToken cancellationToken)
        {
            if (priceBooksRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var priceBooksResult = await _priceBooksUseCase.ExecuteAsync(priceBooksRequest, cancellationToken);
            if (!priceBooksResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product price books: {string.Join(", ", priceBooksResult.ValidationErrors ?? [priceBooksResult.ErrorMessage ?? "Failed to update product price books"])}");
                return Result<UpdateProductResponse>.Failure(priceBooksResult.ValidationErrors ??
                    [priceBooksResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật sản phẩm giá sản phẩm"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateToppingsAsync(
            UpdateProductToppingsRequest? toppingsRequest,
            CancellationToken cancellationToken)
        {
            if (toppingsRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var toppingsResult = await _toppingsUseCase.ExecuteAsync(toppingsRequest, cancellationToken);
            if (!toppingsResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product toppings: {string.Join(", ", toppingsResult.ValidationErrors ?? [toppingsResult.ErrorMessage ?? "Failed to update product toppings"])}");
                return Result<UpdateProductResponse>.Failure(toppingsResult.ValidationErrors ??
                    [toppingsResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật sản phẩm topping"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
        
        private async Task<Result<UpdateProductResponse>> UpdateSaleBranchesAsync(
            UpdateProductSaleBranchesRequest? saleBranchesRequest,
            CancellationToken cancellationToken)
        {
            if (saleBranchesRequest == null)
            {
                return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
            }
            
            var saleBranchesResult = await _saleBranchesUseCase.ExecuteAsync(saleBranchesRequest, cancellationToken);
            if (!saleBranchesResult.IsSuccess)
            {
                _logger.Warning($"Failed to update product sale branches: {string.Join(", ", saleBranchesResult.ValidationErrors ?? [saleBranchesResult.ErrorMessage ?? "Failed to update product sale branches"])}");
                return Result<UpdateProductResponse>.Failure(saleBranchesResult.ValidationErrors ??
                    [saleBranchesResult.ErrorMessage ?? "Có lỗi xảy ra khi cập nhật chi nhánh bán sản phẩm"]);
            }
            
            return Result<UpdateProductResponse>.Success(new UpdateProductResponse());
        }
    }
} 