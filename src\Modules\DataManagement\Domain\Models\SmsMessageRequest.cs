using System;

namespace KvFnB.Modules.DataManagement.Domain.Models
{
    /// <summary>
    /// Represents an SMS message request
    /// </summary>
    public class SmsMessageRequest
    {
        /// <summary>
        /// Gets or sets the phone number to send the SMS to
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the SMS message content
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the retailer code
        /// </summary>
        public string RetailerCode { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the retailer ID
        /// </summary>
        public long RetailerId { get; set; } = 0;

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the user ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// Gets or sets the session ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the expiration time
        /// </summary>
        public DateTime ExpiredAt { get; set; }

        /// <summary>
        /// Gets or sets the device type
        /// </summary>
        public int FromDevice { get; set; }

        /// <summary>
        /// Gets or sets the fingerprint key
        /// </summary>
        public string FingerPrintKey { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the function ID
        /// </summary>
        public int FunctionId { get; set; }

        /// <summary>
        /// Gets or sets the request ID
        /// </summary>
        public string RequestId { get; set; } = string.Empty;
    }
}