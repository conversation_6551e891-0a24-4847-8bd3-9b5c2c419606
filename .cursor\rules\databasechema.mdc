---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---

Whenever the AI needs to retrieve or verify table schemas (or other database structures) to complete its tasks, it must first do so via mcp-dbs. If that fails, it should then use the command line. The following credentials and database details apply.

Server: dc2d-fnb-mssql-01.citigo.io
Username: sa
Password: mssql#C1t1g0@sa
Database: HydraKiotVietShard1