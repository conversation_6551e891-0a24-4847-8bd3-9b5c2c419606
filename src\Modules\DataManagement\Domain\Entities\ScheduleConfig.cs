using System.Text.Json.Serialization;
using KvFnB.Modules.DataManagement.Domain.Enums;
namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    public class ScheduleConfig
    {
        /// <summary>
        /// Type of schedule: "daily", "weekly", "monthly", "quarterly", "yearly"
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public ScheduleType ScheduleType { get; set; }
        
        /// <summary>
        /// Day of week (0-6), only applicable for weekly schedules
        /// </summary>
        [JsonPropertyName("day_of_week")]
        public int? DayOfWeek { get; set; }
        
        /// <summary>
        /// Day of month (1-31), applicable for monthly, quarterly, and yearly schedules
        /// </summary>
        [JsonPropertyName("day_of_month")]
        public int? DayOfMonth { get; set; }
        
        /// <summary>
        /// Month (1-12), only applicable for yearly schedules
        /// </summary>
        [JsonPropertyName("month")]
        public int? Month { get; set; }
        
        /// <summary>
        /// Quarter month (1-3), only applicable for quarterly schedules
        /// </summary>
        [JsonPropertyName("quarter_month")]
        public int? QuarterMonth { get; set; }
        
        /// <summary>
        /// Time of day for execution in format "HH:MM:SS"
        /// </summary>
        [JsonPropertyName("execution_time")]
        public string ExecutionTime { get; set; }
        
        /// <summary>
        /// Number of months to retain data
        /// </summary>
        [JsonPropertyName("retention_months")]
        public int? RetentionMonths { get; set; }
        
        /// <summary>
        /// Number of months to retain data
        /// </summary>
        [JsonPropertyName("next_execution_date")]
        public DateTime? NextExecutionDate { get; set; }

    }
}
