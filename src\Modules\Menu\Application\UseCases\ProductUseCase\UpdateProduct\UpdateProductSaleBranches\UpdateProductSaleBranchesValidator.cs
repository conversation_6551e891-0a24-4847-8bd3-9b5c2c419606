using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductSaleBranches
{
    /// <summary>
    /// Validates the UpdateProductSaleBranches request
    /// </summary>
    public class UpdateProductSaleBranchesValidator : Validator<UpdateProductSaleBranchesRequest>
    {
        public UpdateProductSaleBranchesValidator(ILocalizationProvider multiLang)
        {
            RuleFor(x => x.ProductId)
                .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_more_than_zero));
        }
    }
} 