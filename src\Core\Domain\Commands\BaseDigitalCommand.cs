using System;
using System.Text.Json.Serialization;

namespace KvFnB.Core.Domain.Commands
{
    /// <summary>
    /// Base class for all digital commands in the system.
    /// Used for command dispatching to digital marketing systems.
    /// </summary>
    public abstract class BaseDigitalCommand
    {
        /// <summary>
        /// Retailer code identifier.
        /// </summary>
        [JsonPropertyName("retailerCode")]
        public string RetailerCode { get; protected set; } = string.Empty;

        /// <summary>
        /// Retailer identifier.
        /// </summary>
        [JsonPropertyName("retailerId")]
        public int RetailerId { get; protected set; }

        /// <summary>
        /// Branch identifier.
        /// </summary>
        [JsonPropertyName("branchId")]
        public long BranchId { get; protected set; }

        /// <summary>
        /// Group identifier.
        /// </summary>
        [JsonPropertyName("groupId")]
        public int GroupId { get; protected set; }

        /// <summary>
        /// User who issued the command.
        /// </summary>
        [JsonPropertyName("userId")]
        public long UserId { get; protected set; }

        /// <summary>
        /// Identifier of the system component that issued the command.
        /// </summary>
        [JsonPropertyName("commandIssuer")]
        public string CommandIssuer { get; protected set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the event.
        /// </summary>
        [JsonPropertyName("eventId")]
        public Guid EventId { get; protected set; }

        /// <summary>
        /// Type of the event, derived from the command class name.
        /// </summary>
        [JsonPropertyName("eventType")]
        public string EventType { get; protected set; } = string.Empty;

        /// <summary>
        /// Default constructor for serialization.
        /// </summary>
        protected BaseDigitalCommand()
        {
        }

        /// <summary>
        /// Creates a new digital command with the specified parameters.
        /// </summary>
        /// <param name="commandIssuer">The system component that issued the command</param>
        /// <param name="retailerCode">The retailer code</param>
        /// <param name="retailerId">The retailer ID</param>
        /// <param name="branchId">The branch ID</param>
        /// <param name="groupId">The group ID</param>
        /// <param name="userId">The user ID</param>
        protected BaseDigitalCommand(string commandIssuer, string retailerCode, int retailerId, long branchId, int groupId, long userId)
        {
            CommandIssuer = commandIssuer;
            EventId = Guid.NewGuid();
            EventType = GetType().Name;
            RetailerCode = retailerCode;
            RetailerId = retailerId;
            BranchId = branchId;
            GroupId = groupId;
            UserId = userId;
        }
    }
} 