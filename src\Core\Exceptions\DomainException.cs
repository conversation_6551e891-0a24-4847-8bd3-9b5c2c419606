using System.Runtime.Serialization;

namespace KvFnB.Core.Exceptions
{
    [Serializable]
    public class DomainException : Exception
    {
        public DomainException(string message) : base(message) { }

        public DomainException(string message, Exception innerException) : base(message, innerException) { }

        public DomainException() { }

        /// <summary>
        /// Initializes a new instance of the DomainException class with serialized data.
        /// </summary>
        /// <param name="info">The SerializationInfo that holds the serialized object data about the exception being thrown.</param>
        /// <param name="context">The StreamingContext that contains contextual information about the source or destination.</param>
        [Obsolete("This constructor is obsolete. Use the constructor that does not take a SerializationInfo and StreamingContext as parameters.")]
        protected DomainException(SerializationInfo info, StreamingContext context) : base(info, context) { }
    }
}