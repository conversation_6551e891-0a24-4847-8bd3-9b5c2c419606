using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Application.Dtos.Request;
using System.Text.Json;
using KvFnB.Modules.Menu.Application.Enums;
using KvFnB.Shared.MultiTenancy;
using KvFnB.Modules.Menu.Application.Helpers;

namespace KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.CreateProductPriceBook
{
    /// <summary>
    /// Implements the CreateProductPriceBook use case.
    /// This class handles the creation of a new price book for products.
    /// </summary>
    public class CreateProductPriceBookUseCase : UseCaseBase<CreateProductPriceBookRequest, CreateProductPriceBookResponse>
    {
        private readonly IPriceBookRepository _priceBookRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IValidator<CreateProductPriceBookRequest> _validator;
        private readonly IMapper _mapper;
        private readonly TenantInfo _tenantInfo;

        /// <summary>
        /// Initializes a new instance of the <see cref="CreateProductPriceBookUseCase"/> class.
        /// </summary>
        /// <param name="priceBookRepository">The repository for price books.</param>
        /// <param name="unitOfWork">The unit of work for transaction management.</param>
        /// <param name="validator">The validator for the request.</param>
        /// <param name="mapper">The mapper for mapping between entities and dtos.</param>
        /// <param name="tenantProvider">The tenant provider.</param>
        public CreateProductPriceBookUseCase(
            IPriceBookRepository priceBookRepository,
            IUnitOfWork unitOfWork,
            IValidator<CreateProductPriceBookRequest> validator,
            IMapper mapper,
            TenantInfo tenantInfo)
        {
            _priceBookRepository = priceBookRepository ?? throw new ArgumentNullException(nameof(priceBookRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantInfo = tenantInfo ?? throw new ArgumentNullException(nameof(tenantInfo));
        }

        /// <summary>
        /// Executes the use case to create a new price book.
        /// </summary>
        /// <param name="request">The request containing the price book details.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the response or error messages.</returns>
        public override async Task<Result<CreateProductPriceBookResponse>> ExecuteAsync(CreateProductPriceBookRequest request, CancellationToken cancellationToken = default)
        {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<CreateProductPriceBookResponse>.Failure(validationResult.Errors);
            }

            // Validate custom time request
            var customTimeErrors = CustomTimeValidator.ValidateCustomTimeRequest(request.CustomTime);
            if (customTimeErrors.Count != 0)
            {
                return Result<CreateProductPriceBookResponse>.Failure(customTimeErrors);
            }

            // Create price book
            var priceBook = PriceBook.Create(new CreatePriceBookModel
            {
                Name = request.Name,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IsGlobal = request.IsGlobal,
                CustomTime = JsonSerializer.Serialize(request.CustomTime),
                ForAllUser = request.ForAllUser,
                ForAllCustomerGroup = request.ForAllCustomerGroup,
                ForAllTableAndRoom = request.ForAllTableAndRoom,
                ForTakeAwayTable = request.ForTakeAwayTable,
                Type = request.Type 
            });

            // Add child entities
            await AddChildEntitiesAsync(priceBook, request);

            // Validate permission for priceBookBranch // Implement this

            var isUniquePriceBookName = await _priceBookRepository.IsUniquePriceBookNameAsync(
                request.Name,
                null,
                cancellationToken);

            if (!isUniquePriceBookName)
            {
                return Result<CreateProductPriceBookResponse>.Failure("Price book name is already exists.");
            }

            // Save price book
            await _priceBookRepository.AddAsync(priceBook, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            // Map to response
            var response = _mapper.Map<CreateProductPriceBookResponse>(priceBook);
            return Result<CreateProductPriceBookResponse>.Success(response);
        }

        private Task AddChildEntitiesAsync(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            AddPriceBookBranches(priceBook, request);
            AddPriceBookCustomerGroups(priceBook, request);
            AddPriceBookTableAndRooms(priceBook, request);
            AddPriceBookGroups(priceBook, request);
            AddPriceBookDiningOptions(priceBook, request);
            AddPriceBookUsers(priceBook, request);

            return Task.CompletedTask;
        }

        private static void AddPriceBookBranches(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.IsGlobal) return;

            if (request.PriceBookBranchIds?.Count > 0)
            {
                foreach (var branchId in request.PriceBookBranchIds)
                {
                    priceBook.AddPriceBookBranch(branchId);
                }
            }
        }

        private static void AddPriceBookCustomerGroups(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.ForAllCustomerGroup) return;

            if (request.PriceBookCustomerGroupIds?.Count > 0)
            {
                foreach (var customerGroupId in request.PriceBookCustomerGroupIds)
                {
                    priceBook.AddPriceBookCustomerGroup(customerGroupId);
                }
            }
        }

        private static void AddPriceBookTableAndRooms(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.ForAllTableAndRoom) return;

            if (request.PriceBookTableAndRoomIds?.Count > 0)
            {
                foreach (var tableAndRoomId in request.PriceBookTableAndRoomIds)
                {
                    priceBook.AddPriceBookTableAndRoom(tableAndRoomId);
                }
            }
        }

        private static void AddPriceBookGroups(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.Type != null && priceBook.Type.Equals("SC")) return;

            if (request.PriceBookGroups?.Count > 0)
            {
                foreach (var groupId in request.PriceBookGroups)
                {
                    priceBook.AddPriceBookGroup(groupId.GroupId, groupId.Status, groupId.Type);
                }
            }
        }

        private void AddPriceBookDiningOptions(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.ForAllTableAndRoom) return;

            if (request.PriceBookDiningOptions?.Count > 0)
            {
                foreach (var diningOption in request.PriceBookDiningOptions)
                {
                    if (diningOption.DiningOption == (int)DiningOptions.Delivery || diningOption.DiningOption == (int)DiningOptions.TakeAway)
                    {
                        priceBook.AddPriceBookDiningOption(diningOption.DiningOption, diningOption.BranchId, _tenantInfo.Id);
                    }
                }
            }
        }

        private static void AddPriceBookUsers(PriceBook priceBook, CreateProductPriceBookRequest request)
        {
            if (priceBook.ForAllUser) return;

            if (request.PriceBookUserIds?.Count > 0)
            {
                foreach (var userId in request.PriceBookUserIds)
                {
                    priceBook.AddPriceBookUser(userId);
                }
            }
        }
    }
}