# Technical Specification: Data Processing Service

## 1. Tổng Quan

Data Processing Service là thành phần trọng yếu trong kiến trúc hệ thống xử lý dữ liệu, chịu trách nhiệm thực hiện việc xử lý dữ liệu theo các yêu cầu đã được phê duyệt. Service này đảm bảo việc xử lý dữ liệu được thực hiện an toàn, đúng đắn và có khả năng khôi phục khi cần thiết.

### 1.1. <PERSON><PERSON><PERSON>i<PERSON>h

- Thực hiện xử lý dữ liệu dựa trên yêu cầu và loại xử lý
- Đảm bảo tính toàn vẹn dữ liệu sau khi xử lý
- Xử lý batches lớn hiệu quả thông qua cơ chế phân mảnh và phân tán
- <PERSON><PERSON> cấp cơ chế retry và khả năng phục hồi từ lỗi
- <PERSON><PERSON> nhận đầy đủ chi tiết quá trình xử lý để phục vụ báo cáo và khôi phục

## 2. Kiến Trúc

### 2.1. Sơ Đồ Tổng Thể

```
+------------------------+
|                        | 
| Scheduler Service      |
|                        | 
+------------------------+      
              |                           
              |                           
              v                           
+------------------------+      +------------------------+
|                        |      |                        |
| Message Queue          +----->+ Data Processing Service|
|                        |      |                        |
+------------------------+      +-----------+------------+
                                            |
                                            v
+------------------------+      +------------------------+
|                        |      |                        |
| Database               |<-----+ Notification Service   |
|                        |      |                        |
+------------------------+      +------------------------+
```

### 2.2. Các Thành Phần Chính của Data Processing Service

#### 2.2.1. Job Consumer
- Lấy các job từ Message Queue (Kafka)
- Kiểm tra trạng thái job và tiến hành xử lý
- Ưu tiên xử lý job theo thứ tự nhận được

#### 2.2.2. IDempotent Service
- Đảm bảo mỗi message chỉ được xử lý đúng một lần
- Sử dụng key dạng: RetailerId-RequestId-Id để nhận diện message đã xử lý
- Ngăn chặn việc xử lý trùng lặp khi nhận được message nhiều lần
- Lưu trữ trạng thái xử lý của mỗi message để tham chiếu

## 3. Quy Trình Xử Lý Chính

### 3.1. Quy Trình Xử Lý Job

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Job Consumer   │────>│ IDempotent      │────>│ Data Processing Job │
│                 │     │   Service       │     │    (Update)     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

**Các bước thực hiện:**

1. **Job Consumer:** 
   - Lấy job từ message queue (DataProcessingMessage)
   - Cập nhật trạng thái job thành "Processing"
   - Khởi tạo tiến trình xử lý

2. **IDempotent Service:**
   - Kiểm tra xem message đã được xử lý hay chưa dựa trên key: RetailerId-RequestId-Id
   - Nếu đã xử lý, bỏ qua và ghi log
   - Nếu chưa xử lý, đánh dấu và chuyển sang bước tiếp theo

3. **Data Processing Job:**
   - Dựa vào loại job (Type) để xác định cách xử lý phù hợp
   - Thực hiện các câu truy vấn cập nhật dữ liệu theo yêu cầu
   - Ghi nhận thông tin chi tiết về các bản ghi đã xử lý
   - Cập nhật trạng thái job thành "Completed"
   - Gửi thông báo đến Notification Service

### 3.2. Chiến Lược Xử Lý Theo Loại Job

#### 3.2.1. Ví Dụ: Xóa Dữ Liệu (Cập Nhật RetailerId)

```sql
-- RetailerId ban đầu: 123
-- RequestId (ID yêu cầu xóa): 456
-- Giá trị RetailerId mới = -@RequestId
-- Kết quả: -1230456

UPDATE [TableName]
SET RetailerId = @RequestId, ModifiedDate = GETDATE()
WHERE RetailerId > 0
AND RetailerId = @OriginalRetailerId 
AND [Các điều kiện lọc khác]
```

Cách này đảm bảo:
- Giá trị âm (để ẩn khỏi queries thông thường)
- Có thể truy ngược RequestId (từ 4 chữ số cuối)
- Giữ được giá trị RetailerId gốc (phần nguyên khi chia cho 10000)

#### 3.2.2. Mở Rộng Cho Các Loại Job Khác

Service được thiết kế để dễ dàng mở rộng cho nhiều loại job xử lý dữ liệu khác nhau:

- **Anonymize**: Thay thế thông tin nhạy cảm bằng dữ liệu ngẫu nhiên
- **Archive**: Di chuyển dữ liệu vào các bảng lưu trữ
- **Transform**: Thay đổi cấu trúc hoặc định dạng dữ liệu
- **Merge**: Kết hợp dữ liệu từ nhiều nguồn

Mỗi loại job sẽ được triển khai qua các strategy pattern riêng biệt.

## 4. Cơ Chế Xử Lý Lỗi và Retry

### 4.1. Phân Loại Lỗi

Data Processing Service phân loại lỗi thành các nhóm để xử lý phù hợp:

1. **Lỗi Tạm Thời (Transient):**
   - Mất kết nối cơ sở dữ liệu
   - Timeout khi thực thi query
   - Deadlock hoặc lock conflict

2. **Lỗi Xử Lý (Processing):**
   - Lỗi logic khi xây dựng query
   - Ràng buộc dữ liệu không thỏa mãn
   - Dữ liệu không tồn tại hoặc đã bị thay đổi

3. **Lỗi Hệ Thống (System):**
   - Lỗi phần cứng
   - Out of memory
   - Lỗi service dependency

### 4.2. Chiến Lược Retry

```csharp
public class RetryHandler
{
    private readonly ILogger<RetryHandler> _logger;
    private readonly RetryPolicy _retryPolicy;
    
    public RetryHandler(ILogger<RetryHandler> logger)
    {
        _logger = logger;
        _retryPolicy = new RetryPolicy
        {
            MaxRetries = 5,
            RetryIntervals = new[] { 30, 60, 120, 300, 600 } // seconds
        };
    }
    
    public async Task<T> ExecuteWithRetry<T>(Func<Task<T>> operation, string operationName)
    {
        int retryCount = 0;
        
        while (true)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                if (!IsRetryable(ex) || retryCount >= _retryPolicy.MaxRetries)
                {
                    _logger.LogError(ex, "Operation {OperationName} failed after {RetryCount} retries", 
                        operationName, retryCount);
                    throw;
                }
                
                int delay = _retryPolicy.RetryIntervals[retryCount];
                retryCount++;
                
                _logger.LogWarning("Operation {OperationName} failed, retrying in {Delay}s (Retry {RetryCount}/{MaxRetries})", 
                    operationName, delay, retryCount, _retryPolicy.MaxRetries);
                    
                await Task.Delay(TimeSpan.FromSeconds(delay));
            }
        }
    }
    
    private bool IsRetryable(Exception ex)
    {
        // Xác định các loại exception có thể retry
        return ex is DbUpdateException ||
               ex is DbConnectionException ||
               ex is TimeoutException ||
               (ex.InnerException != null && IsRetryable(ex.InnerException));
    }
}
```

### 4.3. Cơ Chế Quy Trình Xử Lý Lỗi

1. **Circuit Breaker:**
   - Phát hiện lỗi liên tục và tạm ngừng xử lý để tránh quá tải
   - Tự động khôi phục sau một khoảng thời gian
   - Giám sát tỷ lệ lỗi để quyết định đóng/mở circuit

2. **Dead-Letter Queue:**
   - Chuyển các job thất bại quá số lần retry vào queue riêng
   - Admin có thể xem xét và xử lý thủ công
   - Cung cấp công cụ để phân tích nguyên nhân lỗi

```csharp
public async Task HandleFailedJob(DataProcessingJob job, Exception exception)
{
    // Cập nhật trạng thái job
    job.Status = JobStatus.Failed;
    job.ErrorMessage = exception.Message;
    job.FailedCount++;
    job.LastUpdateTime = DateTime.UtcNow;
    
    await dbContext.SaveChangesAsync();
    
    // Kiểm tra số lần thất bại
    if (job.FailedCount >= _maxRetries)
    {
        // Chuyển vào dead-letter queue
        await _messageBroker.SendToDeadLetterQueueAsync(job);
        
        // Gửi thông báo cho admin
        await _notificationService.NotifyJobFailureAsync(job, exception);
        
        _logger.LogError("Job {JobId} failed permanently after {RetryCount} retries", 
            job.Id, job.FailedCount);
    }
    else
    {
        // Lên lịch retry
        int delay = _retryPolicy.GetRetryDelay(job.FailedCount);
        await _messageBroker.ScheduleRetryAsync(job, TimeSpan.FromSeconds(delay));
        
        _logger.LogWarning("Job {JobId} scheduled for retry in {Delay}s (Attempt {RetryCount}/{MaxRetries})", 
            job.Id, delay, job.FailedCount, _maxRetries);
    }
}
```

## 5. Khôi Phục Dữ Liệu

### 5.1. Quy Trình Khôi Phục

1. **Xác Định Dữ Liệu Cần Khôi Phục:**
   - Admin chọn yêu cầu xóa cụ thể để khôi phục
   - Hệ thống hiển thị thông tin chi tiết về dữ liệu đã bị xóa

2. **Thực Hiện Khôi Phục:**
   - Đảo ngược logic cập nhật RetailerId (từ âm sang dương)
   - Cập nhật trạng thái trong bảng DataProcessingDetail
   - Ghi log chi tiết quá trình khôi phục

```csharp
public async Task<int> RestoreData(long requestId)
{
    int restoredCount = 0;
    
    // Lấy thông tin chi tiết về yêu cầu xóa
    var details = await dbContext.DataProcessingDetails
        .Where(d => d.RequestId == requestId && d.Status == 1) // Processed
        .ToListAsync();
    
    foreach (var detail in details)
    {
        // Khôi phục RetailerId cho từng bảng
        string query = $@"
            UPDATE [{detail.TableName}]
            SET RetailerId = ABS(RetailerId) / 10000
            WHERE RetailerId < 0
            AND (ABS(RetailerId) % 10000) = @RequestIdMod";
        
        var parameters = new { RequestIdMod = requestId % 10000 };
        
        int affected = await dbContext.Database.ExecuteSqlRawAsync(query, parameters);
        restoredCount += affected;
        
        // Cập nhật trạng thái
        detail.Status = 3; // Restored
        detail.ProcessedDate = DateTime.UtcNow;
    }
    
    // Cập nhật yêu cầu khôi phục
    var request = await dbContext.DataProcessingRequests.FindAsync(requestId);
    request.Status = 4; // Restored
    request.ModifiedDate = DateTime.UtcNow;
    
    await dbContext.SaveChangesAsync();
    
    return restoredCount;
}
```

## 6. Tối Ưu Hiệu Năng

### 6.1. Xử Lý Phân Mảnh

Đối với các yêu cầu xóa lớn, Data Processing Service sẽ tự động phân mảnh thành các batch nhỏ:

```csharp
public IEnumerable<Batch> CreateBatches(DataProcessingRequest request, int batchSize = 1000)
{
    // Dựa vào thời gian để phân mảnh
    if (request.FromDate.HasValue && request.ToDate.HasValue)
    {
        var fromDate = request.FromDate.Value;
        var toDate = request.ToDate.Value;
        var currentDate = fromDate;
        
        while (currentDate < toDate)
        {
            var batchEndDate = currentDate.AddDays(30); // Batch 30 ngày
            if (batchEndDate > toDate)
                batchEndDate = toDate;
                
            yield return new Batch
            {
                RequestId = request.Id,
                FromDate = currentDate,
                ToDate = batchEndDate,
                CreatedDate = DateTime.UtcNow
            };
            
            currentDate = batchEndDate.AddDays(1);
        }
    }
    else
    {
        // Phân mảnh theo số lượng bản ghi ước tính
        var estimatedCount = EstimateRecordCount(request);
        var batchCount = Math.Ceiling((double)estimatedCount / batchSize);
        
        for (int i = 0; i < batchCount; i++)
        {
            yield return new Batch
            {
                RequestId = request.Id,
                BatchNumber = i + 1,
                BatchSize = batchSize,
                CreatedDate = DateTime.UtcNow
            };
        }
    }
}
```

### 6.2. Chiến Lược Cập Nhật Hiệu Quả

1. **Sử Dụng Bảng Tạm:**
   - Tạo bảng tạm chứa ID các bản ghi cần cập nhật
   - JOIN với bảng tạm để cập nhật, tránh full table scan

```sql
-- Tạo bảng tạm
SELECT Id INTO #TempIds
FROM [TableName]
WHERE RetailerId = @RetailerId
AND CreatedDate BETWEEN @FromDate AND @ToDate
AND [Các điều kiện lọc]

-- Cập nhật sử dụng JOIN
UPDATE t
SET RetailerId = @NegativeRetailerId
FROM [TableName] t
JOIN #TempIds tmp ON t.Id = tmp.Id
```

2. **Batch Processing:**
   - Cập nhật theo batch nhỏ (500-1000 bản ghi/lần)
   - Sử dụng row_number() để phân trang kết quả

```sql
WITH CTE AS (
    SELECT Id, ROW_NUMBER() OVER (ORDER BY Id) AS RowNum
    FROM [TableName]
    WHERE RetailerId = @RetailerId
    AND [Các điều kiện lọc]
)
UPDATE t
SET RetailerId = @NegativeRetailerId
FROM [TableName] t
JOIN CTE c ON t.Id = c.Id
WHERE c.RowNum BETWEEN @BatchStart AND @BatchEnd
```

3. **Parallel Processing:**
   - Xử lý nhiều bảng độc lập song song
   - Sử dụng thread pool để quản lý số lượng tác vụ đồng thời

```csharp
public async Task ProcessTablesInParallel(DataProcessingJob job, List<string> tables)
{
    var tasks = new List<Task>();
    var semaphore = new SemaphoreSlim(_maxConcurrentTasks);
    
    foreach (var table in tables)
    {
        await semaphore.WaitAsync();
        
        tasks.Add(Task.Run(async () => {
            try
            {
                await UpdateRetailerIdForTable(job, table);
            }
            finally
            {
                semaphore.Release();
            }
        }));
    }
    
    await Task.WhenAll(tasks);
}
```

### 6.3. Cân Bằng Tải và Auto-Scaling

1. **Worker Pool:**
   - Triển khai nhiều worker instances trên Kubernetes
   - Mỗi worker xử lý các job độc lập

2. **Resource Limits:**
   - Giới hạn tài nguyên cho mỗi worker (CPU, memory)
   - Horizontal scaling dựa trên tải hệ thống

```yaml
# worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-processing-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: data-processing-worker
  template:
    metadata:
      labels:
        app: data-processing-worker
    spec:
      containers:
      - name: data-processing-worker
        image: your-registry/data-processing-worker:latest
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
```

3. **Adaptive Batch Size:**
   - Điều chỉnh kích thước batch dựa vào tải hệ thống
   - Giảm kích thước batch khi hệ thống có tải cao

## 7. Tích Hợp Với Các Service Khác

### 7.1. Tích Hợp Với Data Processing Service

- Nhận thông tin về các yêu cầu xử lý được phê duyệt
- Cập nhật trạng thái xử lý và kết quả về Data Processing Service

### 7.2. Tích Hợp Với Scheduler Service

- Nhận job từ scheduler thông qua message queue
- Báo cáo kết quả xử lý để scheduler cập nhật lịch trình

### 7.3. Tích Hợp Với Notification Service

- Gửi thông báo khi bắt đầu và kết thúc xử lý
- Thông báo về các lỗi nghiêm trọng cần can thiệp
- Gửi báo cáo tổng kết sau khi hoàn thành quá trình xử lý dữ liệu

## 8. Monitoring và Logging

### 8.1. Metrics Thu Thập

- Số lượng bản ghi đã cập nhật RetailerId
- Thời gian xử lý trung bình cho mỗi bảng
- Tỷ lệ thành công/thất bại của các job
- Tài nguyên sử dụng (CPU, memory, disk I/O)

### 8.2. Structured Logging

```csharp
public void LogTableUpdate(string tableName, int retailerId, int updatedCount, TimeSpan duration)
{
    _logger.LogInformation(
        "Updated RetailerId for table {TableName}, retailer {RetailerId}, records: {UpdatedCount}, duration: {Duration}ms",
        tableName, retailerId, updatedCount, duration.TotalMilliseconds);
}
```

### 8.3. Alerting

- Cảnh báo khi tỷ lệ lỗi vượt ngưỡng (>5%)
- Cảnh báo khi thời gian xử lý vượt quá dự kiến
- Cảnh báo khi job bị retry quá nhiều lần

## 9. Xử Lý Tình Huống Đặc Biệt

### 9.1. Xử Lý Dữ Liệu EInvoice

Đối với hóa đơn điện tử đã phát hành, cần xử lý đặc biệt:

```csharp
public async Task<bool> CanUpdateInvoice(long invoiceId)
{
    // Kiểm tra trạng thái EInvoice
    var eInvoice = await dbContext.EInvoices
        .Where(e => e.InvoiceId == invoiceId)
        .FirstOrDefaultAsync();
    
    if (eInvoice != null)
    {
        // Chỉ cho phép cập nhật khi hóa đơn chưa đồng bộ với thuế
        return eInvoice.Status != 1; // 1: Đã phát hành
    }
    
    return true; // Không có EInvoice, cho phép cập nhật
}
```

### 9.2. Graceful Shutdown

```csharp
public async Task GracefulShutdown(CancellationToken cancellationToken)
{
    _logger.LogInformation("Graceful shutdown initiated for Data Processing Service");
    
    // Đánh dấu không nhận job mới
    _isShuttingDown = true;
    
    try
    {
        // Đợi các job đang xử lý hoàn tất
        using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
        
        await Task.WhenAll(_activeJobs.Values).WaitAsync(linkedCts.Token);
        _logger.LogInformation("All active jobs completed successfully during shutdown");
    }
    catch (OperationCanceledException)
    {
        _logger.LogWarning("Some jobs did not complete within shutdown timeout");
        
        // Lưu trạng thái job đang xử lý để có thể tiếp tục sau khi khởi động lại
        await SaveIncompleteJobStates();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error occurred during graceful shutdown");
    }
    
    _logger.LogInformation("Data Processing Service shutdown completed");
}
```

## 10. Kết Luận

Data Processing Service là thành phần trọng yếu trong kiến trúc hệ thống xử lý dữ liệu, chịu trách nhiệm thực hiện việc xử lý dữ liệu theo các yêu cầu đã được phê duyệt. Service này đảm bảo việc xử lý dữ liệu được thực hiện an toàn, đúng đắn và có khả năng khôi phục khi cần thiết.

Key features của Data Processing Service bao gồm:
- Xử lý dữ liệu theo công thức đặc biệt dựa vào loại dữ liệu
- IDempotent Service đảm bảo mỗi message chỉ được xử lý một lần duy nhất
- Xử lý dữ liệu từ MessageQueue với cấu trúc DataProcessingMessage
- Phân mảnh job lớn để tối ưu hiệu năng
- Retry thông minh với exponential backoff
- Khả năng khôi phục dữ liệu

Với những đặc điểm này, Data Processing Service đáp ứng đầy đủ yêu cầu về một hệ thống xử lý dữ liệu đáng tin cậy, hiệu quả và có khả năng mở rộng cho nhiều loại tác vụ xử lý dữ liệu khác nhau.