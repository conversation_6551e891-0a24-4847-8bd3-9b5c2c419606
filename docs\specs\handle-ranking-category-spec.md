# Integer-based Gap Ranking Specification

## Table of Contents
- [Overview](#overview)
- [Problem Statement](#problem-statement)
- [Objectives](#objectives)
- [Solution](#solution-integer-based-gap-ranking-algorithm)
  - [Key Concepts](#key-concepts)
  - [Algorithm Steps](#algorithm-steps)
  - [Implementation Examples](#implementation-examples)
  - [Validation Process](#validation-process)
- [Benefits](#benefits)
- [Implementation Considerations](#implementation-considerations)
- [Conclusion](#conclusion)

## Overview
The Integer-based Gap Ranking Algorithm provides a method for managing ordered items in a database using integer rank values. By strategically spacing initial ranks, the algorithm allows for inserting new items without requiring immediate reIndexing of the entire list, optimizing system performance for frequently changing data.

## Problem Statement
When storing item order using integer ranks, inserting a new item between consecutive ranks (e.g., between 100 and 101) is problematic since fractional ranks aren't possible with integer storage. Without a solution, each insertion between consecutive ranks would force a computationally expensive reIndexing of the entire list, negatively impacting performance with large datasets or frequent changes.

## Objectives
- Minimize reIndexing operations when inserting new items
- Support efficient insertion at any position while maintaining integer ranks
- Ensure consistent and predictable ordering
- Maintain stable performance with large datasets and frequent modifications

## Solution: Integer-based Gap Ranking Algorithm
This algorithm uses deliberate gaps between rank values and smart reIndexing to efficiently handle insertions while maintaining integer ranks.

### Key Concepts

#### Initial Rank Assignment
- Assign initial ranks with consistent gaps (e.g., increments of 100: 100, 200, 300)
- Gap size determines how many insertions can occur before reIndexing becomes necessary
- Default initial rank for first item is 100
- Default gap between items is 100
- Minimum acceptable gap is 1 (gaps ≤ 1 trigger reIndexing)

#### Insertion Mechanism
- When inserting between existing items, calculate the new rank as the integer average of surrounding ranks
- If the gap is too small (≤ 1) or calculated rank isn't unique, trigger reIndexing

#### Smart reIndexing
- Reindex affected items to restore consistent gaps (typically 100 between each item)
- Example after reIndexing: 100, 200, 300, etc.

#### Four Insertion Scenarios
1. **Between two existing items**: Calculate average of surrounding ranks if gap > 1
2. **After the last item**: Add default gap (100) to the last rank
3. **Before the first item**: Calculate (0 + firstItemRank) / 2
4. **First item in empty list**: Assign default rank (100)

### Algorithm Steps

1. **Initialization**:
   - If all rank values are null:
     - Retrieve all items and sort by default field (e.g., Name)
     - Assign initial ranks with gaps of 100
   - If some or all ranks are defined, proceed with existing ranks

2. **Validation and Position Check**:
   - Verify the calculated rank doesn't conflict with existing ranks
   - Perform position validation based on insertion context
   - See Validation Process section for detailed validation rules

3. **Insert New Item**:
   - Identify insertion position relative to existing items
   - Calculate rank based on the four scenarios described above
   - If gap is insufficient (≤ 1), trigger reIndexing
   - For scenario 3 (inserting before first item), also check if integer division leads to a rank of 0 or creates a gap ≤ 1, which triggers reIndexing

4. **reIndexing Process**:
   - Retrieve all items ordered by current rank, with a secondary sort by Name when ranks are equal or null
   - Remove the item being repositioned from the sorted list
   - Determine the insertion index based on the nextCategoryId value
   - Insert the item at the determined position
   - Reassign ranks with consistent gaps of 100 starting from DefaultFirstRank (100)
   - Update each item individually, with all changes committed in a single transaction via UnitOfWork pattern

### Implementation Examples

**Scenario 1: Items with adequate gaps**

**Initial State**:
- Item A: rank = 100
- Item B: rank = 200

**Insert Item C between A and B**:
- Calculated rank: (100 + 200) / 2 = 150
- Result:
  - Item A: rank = 100
  - Item C: rank = 150
  - Item B: rank = 200

**Scenario 2: Items with narrow gaps**

**Initial State**:
- Item A: rank = 100
- Item B: rank = 101

**Insert Item C between A and B**:
- Calculated rank: (100 + 101) / 2 = 100 (integer division)
- Gap is ≤ 1, triggering reIndexing:
  - Item A: rank = 100
  - Item C: rank = 200
  - Item B: rank = 300

**Scenario 3: Inserting before the first item**

**Initial State**:
- Item A: rank = 100

**Insert Item B before A**:
- Calculated rank: (0 + 100) / 2 = 50
- Result:
  - Item B: rank = 50
  - Item A: rank = 100

**Scenario 4: Integer division limitations**

Repeatedly inserting items at the beginning demonstrates how integer division eventually leads to reIndexing:
- First item: rank = 100
- Insert before first: (0 + 100) / 2 = 50
- Insert before first again: (0 + 50) / 2 = 25
- Insert before first again: (0 + 25) / 2 = 12
- Insert before first again: (0 + 12) / 2 = 6
- Insert before first again: (0 + 6) / 2 = 3
- Insert before first again: (0 + 3) / 2 = 1
- Insert before first again: (0 + 1) / 2 = 0 (collision, triggers reIndexing)

Note: The implementation specifically checks for two conditions when inserting before the first item:
1. If the calculated rank equals 0 (which would cause a collision with rank 0)
2. If the gap between the next category's rank and the calculated rank is ≤ MinimumAcceptableGap (1)

Either condition will trigger reIndexing, as implemented in the `CalculateRankBeforeFirstItem` method.

**Scenario 5: Initial reIndexing with null ranks**

**Initial State**:
- Item A (Name = "Apple", rank = null)
- Item B (Name = "Banana", rank = null)
- Item C (Name = "Cherry", rank = null)

**Perform initial reIndexing**:
- Sort by rank (null values last), then by Name: A, B, C
- Assign ranks:
  - Item A: rank = 100
  - Item B: rank = 200
  - Item C: rank = 300

**Insert Item D between B and C**:
- Calculated rank: (200 + 300) / 2 = 250
- Result:
  - Item A: rank = 100
  - Item B: rank = 200
  - Item D: rank = 250
  - Item C: rank = 300

### Validation Process

The implementation performs a series of validations to ensure data integrity before any rank operation:

#### 0. Basic Entity Checks (Preliminary)
- **Category to be Ranked**: Verify the category specified by `CategoryId` exists and is not deleted. If not found or deleted: error "Category not found".
- **Pre-Category**: If `PreCategoryId` is provided, verify the corresponding category exists and is not deleted. If not found or deleted: error "PreCategory not found".
- **Next-Category**: If `NextCategoryId` is provided, verify the corresponding category exists and is not deleted. If not found or deleted: error "NextCategory not found".

#### 1. Validating When No Position Is Specified
- When neither PreCategoryId nor NextCategoryId is provided:
  - Check if categories exist (other than the one being modified)
  - If categories exist: error "Cannot insert without specifying position when other categories exist. Please provide PreCategoryId or NextCategoryId."
  - If no categories exist: allow insertion as first item

#### 2. Validating When Inserting Before a Category
- When only NextCategoryId is provided:
  - Verify next category exists; if not: error "NextCategory not found"
  - If next category has a positive rank, check for items with ranks between 0 and nextRank
  - If such items exist: error "Cannot insert before the specified NextCategory due to existing items with lower positive ranks. Please provide PreCategoryId."

#### 3. Validating When Inserting After a Category
- When only PreCategoryId is provided:
  - Verify previous category exists; if not: error "PreCategory not found"
  - If previous category has a positive rank, check for items with higher ranks or default (null/0) ranks
  - If such items exist: error "Cannot insert after the specified PreCategory due to existing items with higher ranks or default ranks. Please provide NextCategoryId."

#### 4. Validating When Inserting Between Two Categories
- When both PreCategoryId and NextCategoryId are provided:
  - If nextCategory has a positive rank, verify that preCategory.Rank < nextCategory.Rank (where ranks are treated as 0 if null).
  - If not: error "Invalid position: PreCategory rank must be less than NextCategory rank when NextCategory has a positive rank."
  - Check for intermediate ranks between `preRankValue` (preCategory.Rank ?? 0) and `nextRankValue` (nextCategory.Rank ?? 0). The specific checks are:
    - If `preRankValue > 0` and `nextRankValue > 0`: Check for existing ranks `r` such that `preRankValue < r < nextRankValue`.
    - If `preRankValue == 0` and `nextRankValue > 0`: Check for existing ranks `r` such that `0 < r < nextRankValue`.
    - If `preRankValue > 0` and `nextRankValue == 0`: Check for existing ranks `r` such that `r > preRankValue`.
    - If `preRankValue == 0` and `nextRankValue == 0`: No intermediate rank check is performed in this specific sub-case as it implies inserting between two default-ranked items or at an edge.
  - If such intermediate ranks exist based on the conditions above: error "Invalid PreRank or NextRank: There are existing ranks between the provided PreRank and NextRank. Please provide correct PreRank and NextRank."

#### 5. Validating Rank Conflicts
- Before applying a new rank, verify it doesn't conflict with existing ranks
- Implemented in `ValidateRankConflicts` method to ensure no other category (except the one being modified) has the same rank
- If conflict exists: error "Invalid position: A category with rank [X] already exists"

## Benefits
- **Reduced reIndexing**: Minimizes expensive reIndexing operations
- **Scalability**: Efficiently handles large datasets with frequent insertions
- **Database Compatibility**: Works with integer-only rank fields without schema modifications
- **Predictable Performance**: Maintains consistent performance characteristics

## Implementation Considerations
- **Null Rank Handling**:
  - **For Calculation**: The implementation often treats `null` ranks as `0` when determining `preRank` or `nextRank` for rank calculation purposes (e.g., `preCategory.Rank ?? 0`).
  - **For Sorting (Re-indexing)**: During the re-indexing process, specifically in the `GetSortedCategoriesWithoutItem` method, categories are sorted using `OrderBy(c => c.Rank ?? int.MinValue).ThenBy(c => c.Name)`. This has the following effect:
    - Categories with `null` ranks are assigned `int.MinValue` as their primary sort key. In an ascending sort, this places them at the *beginning* of the list relative to items with actual zero or positive ranks.
    - Categories with a rank of `0` are placed after `null`-ranked items (which evaluate to `int.MinValue`) and before positive-ranked items.
    - All items, within their respective rank groups (nulls, zeros, positives), are then secondarily sorted by `Name`.
- **Gap Size Selection**: The implementation uses a constant `DefaultGap` of 100 between items, which has proven effective for most scenarios.
- **Transaction Management**: The implementation uses the Unit of Work pattern to ensure all database changes are committed in a single transaction.
- **Integer Division Awareness**: The implementation specifically checks for integer division edge cases when inserting before the first item to prevent rank collisions at 0.
- **Comprehensive Validation**: The implementation includes detailed validation methods for different insertion scenarios, with specialized handling for null ranks and boundary conditions.
- **Secondary Sorting**: When ranks are equal or null, items are sorted by Name as a secondary criterion to ensure consistent ordering.
- **Distinct Methods**: The implementation separates logic for each scenario into distinct methods (`CalculateRankBetweenItems`, `CalculateRankAfterLastItem`, `CalculateRankBeforeFirstItem`) for better maintainability.

## Conclusion
The Integer-based Gap Ranking Algorithm provides an efficient solution for maintaining ordered items using integer ranks. By strategically managing rank gaps and implementing smart reIndexing, the system minimizes performance impacts while ensuring data integrity. This approach is particularly valuable for systems that require integer-based ranking with frequent insertions or reordering operations.
