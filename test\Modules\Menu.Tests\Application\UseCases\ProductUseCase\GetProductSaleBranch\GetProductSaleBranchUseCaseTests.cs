using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductSaleBranch;
using Microsoft.Extensions.Logging;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase
{
    public class GetProductSaleBranchUseCaseTests
    {
        private readonly Mock<IValidator<GetProductSaleBranchRequest>> _validatorMock;
        private readonly Mock<IQueryService> _queryServiceMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly Mock<ILogger<GetProductSaleBranchUseCase>> _loggerMock;
        private readonly GetProductSaleBranchUseCase _useCase;

        public GetProductSaleBranchUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<GetProductSaleBranchRequest>>();
            _queryServiceMock = new Mock<IQueryService>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _loggerMock = new Mock<ILogger<GetProductSaleBranchUseCase>>();
            
            _useCase = new GetProductSaleBranchUseCase(
                _validatorMock.Object,
                _queryServiceMock.Object,
                _tenantProviderMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new GetProductSaleBranchRequest { ProductId = 1 };
            var validationResult = new ValidationResult(false, new List<string> { "Error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Error", result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenEntityNotFound_ShouldReturnEmpty()
        {
            // Arrange
            var request = new GetProductSaleBranchRequest { ProductId = 1 };
            var validationResult = new ValidationResult(true, new List<string>());
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _queryServiceMock
                .Setup(q => q.QueryFirstOrDefaultAsync<ProductSaleBranchResponse>(It.IsAny<IQueryBuilder>(), It.IsAny<bool>()))
                .ReturnsAsync((ProductSaleBranchResponse)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            // check value empty list
            Assert.Empty(result.Value!.NotAllowSaleBranch);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldReturnSuccess()
        {
            // Arrange
            var request = new GetProductSaleBranchRequest { ProductId = 1 };
            var validationResult = new ValidationResult(true, new List<string>());
            var productSaleBranch = new List<ProductSaleBranchResponse> { new ProductSaleBranchResponse() };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _queryServiceMock
                .Setup(q => q.QueryAsync<ProductSaleBranchResponse>(It.IsAny<IQueryBuilder>(), It.IsAny<bool>()))
                .ReturnsAsync(productSaleBranch);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value);
            Assert.Equal(productSaleBranch, result.Value.NotAllowSaleBranch);
        }
    }
} 