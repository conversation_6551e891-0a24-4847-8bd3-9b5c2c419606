---
description: 
globs: 
alwaysApply: false
---
# Code Generation Service

## Overview
The code generation service is responsible for creating sequential, unique codes for entities in the system. It ensures that codes follow a specified format and are unique within a tenant's scope.

## Architecture

### Interface Location
The service interface `ICodeGenerationService` is defined in the Core module:
- Location: `src/Core/Domain/Services/ICodeGenerationService.cs`

### Implementation Location
The implementation `AdvancedCodeGenerationService` is in the Shared module:
- Location: `src/Shared/Persistence/ShardingDb/Services/AdvancedCodeGenerationService.cs`

### Database Table
Codes are stored in the `AutoGeneratedCode` table in the ShardingDb:
- Entity: `src/Shared/Persistence/ShardingDb/Entities/AutoGeneratedCode.cs`
- Configuration: `src/Shared/Persistence/ShardingDb/EntityTypeConfigurations/AutoGeneratedCodeEntityTypeConfiguration.cs`

### DI Registration
The service is registered via the extension method:
- `src/Shared/DependencyInjection/CodeGenerationServiceCollectionExtensions.cs`

## Interface

```csharp
public interface ICodeGenerationService
{
    Task<string> GenerateCodeAsync(int tenantId, string typeOf, string prefix, string customCode = null, int minPadding = 6);
    
    Task<IReadOnlyList<string>> GenerateBatchCodesAsync(int tenantId, string typeOf, string prefix, int batchSize, int minPadding = 6);
    
    Task<T> GenerateCodeAndCreateEntityAsync<T>(
        int tenantId, 
        string entityTypeOf, 
        string prefix, 
        Func<string, Task<T>> createEntityFunc, 
        string customCode = null, 
        int minPadding = 6) where T : class, ICode;
        
    Task<T> UpdateEntityCodeAsync<T>(
        int tenantId, 
        string entityTypeOf, 
        string prefix, 
        int entityId,
        Func<T, Task> updateEntityFunc,
        string customCode = null,
        int minPadding = 6) where T : class, ICode;
}
```

## Usage Guidelines

### Code Parameters

1. **tenantId**: The tenant ID to scope the code to (required)
2. **typeOf**: The entity type (e.g., "Product", "Order")
3. **prefix**: A short string that prefixes the code (e.g., "PRD", "ORD")
4. **customCode**: Optional custom code provided by user
5. **minPadding**: Minimum padding for the numeric part (default: 6)
6. **entityId**: For updates, the ID of the entity to be updated
7. **updateEntityFunc**: For updates, the function to update the entity with the new code

### Best Practices

1. **Consistent Naming**: Use consistent entity type names across all services
2. **Tenant Isolation**: Always include the tenant ID to ensure proper isolation
3. **Atomic Operations**: Both `GenerateCodeAndCreateEntityAsync` and `UpdateEntityCodeAsync` operations are atomic, wrapped in a single transaction to ensure data consistency and prevent gaps
4. **Error Handling**: Implement proper exception handling for concurrency issues
5. **Prefix Selection**: Keep prefixes short but meaningful (2-5 characters)
6. **Padding**: Choose padding that accommodates future growth
7. **Code Updates**: When updating codes, ensure the new code follows the same prefix pattern

### Example Usage

Basic code generation:
```csharp
string orderNumber = await _codeGenerationService.GenerateCodeAsync(
    tenantId: 1, 
    typeOf: "Order",
    prefix: "ORD",
    minPadding: 6);
// Result: "ORD000001"
```

Batch code generation:
```csharp
IReadOnlyList<string> batchCodes = await _codeGenerationService.GenerateBatchCodesAsync(
    tenantId: 1,
    typeOf: "SerialNumber",
    prefix: "SN",
    batchSize: 10,
    minPadding: 8);
// Results: ["**********", "**********", ...]
```

Entity creation with code generation:
```csharp
Product product = await _codeGenerationService.GenerateCodeAndCreateEntityAsync(
    tenantId: 1,
    entityTypeOf: "Product",
    prefix: "PRD",
    createEntityFunc: (code) => {
        return Task.FromResult(new Product { 
            TenantId = 1, 
            Code = code, 
            Name = "Test Product" 
        });
    },
    minPadding: 6);
```

Entity code update:
```csharp
Product updatedProduct = await _codeGenerationService.UpdateEntityCodeAsync<Product>(
    tenantId: 1,
    entityTypeOf: "Product",
    prefix: "PRD",
    entityId: 123,
    updateEntityFunc: async (product) => {
        product.Name = "Updated Product Name";
        await Task.CompletedTask;
    },
    customCode: "PRD000999",
    minPadding: 6);
```

## Extension Points

When extending the code generation service:

1. **New Entity Types**: No changes needed, just use a new entity type string
2. **Custom Formatting**: If needed, create a wrapper service that formats codes according to specific requirements
3. **Performance Optimization**: For high-volume scenarios, consider implementing a batch allocation strategy 