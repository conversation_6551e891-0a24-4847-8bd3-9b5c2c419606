using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Infrastructure.Persistence;
using KvFnB.Shared.Persistence.ShardingDb;
using KvFnB.Shared.Tests.Persistence.ShardingDb;
using KvFnB.TestUtilities;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Menu.Infrastructure.Tests.Persistence;

[Trait("Type", "Integration")]
[Collection("Sequential")]
public class PriceBookRepositoryTests : IClassFixture<MsSqlContainerFixtureProvider>
{
    private readonly DbContextOptions<ShardingDbContext>? _dbContextOptions;

    public PriceBookRepositoryTests(MsSqlContainerFixtureProvider fixtureProvider)
    {
        _dbContextOptions = fixtureProvider.DbContextOptions;
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnTrue_WhenNoPriceBookExists()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);
        
        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("NewPriceBook");

        // Assert
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnFalse_WhenPriceBookWithSameNameExists()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);

        var priceBook = PriceBook.Create(
            new CreatePriceBookModel
            {
                Name = "ExistingPriceBook",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(30),
                IsGlobal = true,
                ForAllUser = true,
                ForAllCustomerGroup = true,
                ForAllTableAndRoom = true,
                ForTakeAwayTable = true,
                Type = "Regular"
            }
        );
        
        await repository.AddAsync(priceBook);
        
        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("ExistingPriceBook");

        // Assert
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnTrue_WhenPriceBookWithSameNameIsDeleted()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "DeletedPriceBook",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        }
        );
        await repository.AddAsync(priceBook);
        
        priceBook.Delete();
        await repository.UpdateAsync(priceBook);

        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("DeletedPriceBook");

        // Assert
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnTrue_WhenCheckingSameIdPriceBook()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "ExistingPriceBook",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        await repository.AddAsync(priceBook);

        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("ExistingPriceBook", priceBook.Id);

        // Assert
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnFalse_WhenDifferentPriceBookHasSameName()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "ExistingPriceBook",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        await repository.AddAsync(priceBook);

        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("ExistingPriceBook", priceBook.Id + 1);

        // Assert
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsUniquePriceBookNameAsync_ShouldReturnTrue_WhenNameDiffersByCase()
    {
        // Arrange
        await using var context = new ShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _dbContextOptions!);
        var repository = new PriceBookRepository(context);

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "ExistingPriceBook",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        await repository.AddAsync(priceBook);

        // Act
        var isUnique = await repository.IsUniquePriceBookNameAsync("existingpricebook");

        // Assert
        Assert.True(isUnique);
    }
}