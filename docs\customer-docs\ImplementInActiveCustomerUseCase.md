You are a senior .NET Core 8.0 engineer working on the KvFnB Core Web API (hexagonal architecture).  
Refer to the following:

1. Architecture (docs/ARCHITECTURE.md):
   - Presentation layer lives under `src/Modules/Customer/Presentation/Restful`
   - Application layer under `src/Modules/Customer/Application/UseCases`
   - Infrastructure under `src/Modules/Customer/Infrastructure`

2. UseCase implementation rules (usecase_implementation.mdc):
   - Every use case needs: Request, Response, Validator, UseCase class  
   - Follow the folder structure:  
     ```
     Application/
       └── UseCases/
           └── CustomerUseCase/
               ├── DeactivateCustomerRequest.cs
               ├── DeactivateCustomerResponse.cs
               ├── DeactivateCustomerValidator.cs
               └── DeactivateCustomerUseCase.cs
     ```

3. Clean‐code conventions (coding_convention.mdc):
   - No more than 7 parameters per constructor/method  
   - Add `[JsonPropertyName("…"), Description("…")]` on all Request/Response properties for Swagger  
   - Use standardized messages from `ErrorMessages.InternalServerError` (never expose raw exception text)  
   - Log full exception internally but return only the constant message  

Generate the following artifacts:

A. Controller action (`CustomersController.cs`):
   - `[HttpPost("inactive")]` under route `v2/customers`  
   - Inject and call `DeactivateCustomerUseCase`  
   - Return `ActionResult<Result<DeactivateCustomerResponse>>`

B. DeactivateCustomerRequest/Response:
   - Request: `public long CustomerId { get; init; }`  
   - Response: empty or confirmation DTO

C. DeactivateCustomerValidator:
   - Ensure `CustomerId > 0`

D. DeactivateCustomerUseCase:
   - Constructor with `IValidator<DeactivateCustomerRequest>`, `ICustomerRepository`, `IUnitOfWork`, `IMapper`  
   - `ExecuteAsync`: check existence, flip `IsActive = false`, commit, map to response  
   - Catch `Exception ex`, log it, and return `Result<DeactivateCustomerResponse>.Failure(ErrorMessages.InternalServerError)`

E. Mapping profile (`CustomerMappingProfile.cs`):
   - Map between domain entity ↔ DTO and Request ↔ Entity, Entity ↔ Response

F. Module registrar (`ModuleRegistrar.cs`):
   - Register use case, validator, repository, unit of work, mapper

G. Unit tests (`DeactivateCustomerUseCaseTests.cs`):
   - Validation failure, entity not found, success scenario

Produce full file contents for all of the above, using proper namespaces and folder conventions.  