# KvFnB Core - Architecture Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture Principles](#architecture-principles)
3. [Project Structure](#project-structure)
4. [Architecture Diagrams](#architecture-diagrams)
5. [Core Layer](#core-layer)
6. [Modules Layer](#modules-layer)
7. [Shared Layer](#shared-layer)
8. [Presentation Layer](#presentation-layer)
9. [Data Access](#data-access)
10. [Cross-Cutting Concerns](#cross-cutting-concerns)
11. [Testing Strategy](#testing-strategy)
12. [Security](#security)
13. [Message Processing](#message-processing)

## Overview

KvFnB Core is a .NET Core 8.0 Web API project following the hexagonal architecture pattern (also known as ports and adapters architecture). This architecture promotes separation of concerns, maintainability, and testability by isolating the core business logic from external dependencies.

### Key Technologies
- .NET Core 8.0
- Entity Framework Core
- Dapper
- StackExchange.Redis
- MongoDB
- SQL Server
- Serilog
- xUnit
- Moq

## Architecture Principles

### SOLID Principles
- **Single Responsibility**: Each class has one reason to change
- **Open/Closed**: Open for extension, closed for modification
- **Liskov Substitution**: Subtypes must be substitutable for their base types
- **Interface Segregation**: Clients should not depend on interfaces they don't use
- **Dependency Inversion**: High-level modules should not depend on low-level modules

### Clean Architecture
- Clear separation of concerns
- Domain-driven design (DDD) principles
- Dependency inversion
- Testability
- Maintainability

## Project Structure

```
src/
├── Core/                 # Core domain and shared logic
│   ├── Domain/          # Domain entities and business rules
│   ├── Abstractions/    # Core interfaces and contracts
│   ├── Authentication/  # Authentication models and logic
│   ├── Validation/      # Shared validation logic
│   └── Utilities/       # Common utility functions
├── Modules/             # Feature modules
│   ├── Domain/         # Module-specific domain logic
│   ├── Application/    # Use cases and interfaces
│   ├── Infrastructure/ # Module-specific infrastructure
│   └── Presentation/   # API controllers and endpoints
├── Shared/             # Shared infrastructure components
│   ├── AuditTrail/     # Audit logging
│   ├── Authentication/ # Shared auth mechanisms
│   ├── Caching/        # Caching implementations
│   ├── DependencyInjection/ # DI configuration
│   ├── DistributedLock/    # Distributed locking
│   ├── Filter/         # Request/response filters
│   ├── Logging/        # Logging infrastructure
│   ├── Mapping/        # Object mapping
│   ├── MessageQueueProvider/ # Message queue implementations
│   ├── Middlewares/    # HTTP middleware
│   ├── MultiTenancy/   # Multi-tenant support
│   ├── Persistence/    # Data access abstractions
│   └── StackExchangeRedis/ # Redis integration
└── Presentation/       # API documentation and OpenAPI
```

## Architecture Diagrams

### High-Level Architecture
```mermaid
graph TB
    %% External Systems
    subgraph External["External Systems"]
        direction LR
        DB[(Database)]
        Redis[(Redis Cache)]
        MongoDB[(MongoDB)]
        MQ[Message Queue]
    end

    %% Root Presentation Layer
    subgraph RootPresentation["Root Presentation Layer"]
        direction LR
        OpenAPI[OpenAPI]
        PartnerAPI[Partner API]
        CoreAPI[Core API]
    end

    %% Core Layer
    subgraph Core["Core Layer"]
        direction TB
        CoreDomain[Domain Models]
        CoreAbstractions[Abstractions]
        CoreAuth[Authentication]
        CoreValidation[Validation]
    end

    %% Shared Infrastructure
    subgraph Shared["Shared Infrastructure"]
        direction LR
        DI[Dependency Injection]
        Logging[Logging]
        Caching[Caching]
        Auth[Authentication]
        Kafka[Kafka Command Dispatcher]
    end

    %% Module Layer
    subgraph Modules["Module Layer"]
        direction TB
        %% Domain Layer
        subgraph ModuleDomain["Domain Layer"]
            direction TB
            Domain[Domain Models]
            Events[Domain Events]
            Ports[Ports/Interfaces]
        end

        %% Application Layer
        subgraph ModuleApp["Application Layer"]
            direction TB
            UseCases[Use Cases]
            DTOs[DTOs]
        end

        %% Infrastructure Layer
        subgraph ModuleInfra["Infrastructure Layer"]
            direction TB
            Adapters[Adapters]
            Repositories[Repository Implementations]
            Services[External Services]
        end

        %% Module Presentation Layer
        subgraph ModulePres["Module Presentation Layer"]
            direction LR
            Restful[Restful API]
            Background[Background Workers]
            Grpc[gRPC Services]
        end
    end

    %% Dependencies
    %% Domain Layer Dependencies
    Domain --> Ports
    Events --> Ports
    Ports --> CoreAbstractions

    %% Application Layer Dependencies
    UseCases --> Domain
    UseCases --> Ports
    DTOs --> Domain

    %% Infrastructure Layer Dependencies
    Adapters --> Ports
    Repositories --> Ports
    Services --> Ports
    Adapters --> External
    Repositories --> External
    Services --> External
    Adapters --> Shared
    Repositories --> Shared
    Services --> Shared

    %% Presentation Layer Dependencies
    RootPresentation --> ModulePres
    ModulePres --> UseCases

    %% Core Dependencies
    Domain --> CoreDomain
```

### Module Structure
```
Module/
├── Domain/                    # Core business logic and interfaces
│   ├── Entities/             # Domain entities
│   ├── ValueObjects/         # Value objects
│   ├── Events/               # Domain events
│   └── Ports/                # Repository and service interfaces
├── Application/              # Use cases and orchestration
│   ├── UseCases/            # Application use cases
│   └── DTOs/                # Data transfer objects
├── Infrastructure/           # External concerns and implementations
│   ├── Adapters/            # External service adapters
│   ├── Repositories/        # Repository implementations
│   └── Services/            # External service implementations
└── Presentation/            # External communication
    ├── Restful/             # REST API Controllers
    ├── Background/          # Background Workers
    └── Grpc/               # gRPC Services
```

### Hexagonal Architecture Principles
- **Domain Layer**: Contains business logic, entities, and port interfaces
- **Application Layer**: Orchestrates use cases and implements business rules
- **Infrastructure Layer**: Implements ports (adapters) for external concerns
- **Presentation Layer**: Handles external communication and routing

### Ports and Adapters
- **Ports**: Interfaces defined in the Domain layer
  - Repository interfaces
  - External service interfaces
  - Event interfaces
- **Adapters**: Implementations in the Infrastructure layer
  - Repository implementations
  - External service adapters
  - Event handlers

### Database Architecture
```mermaid
graph TB
    subgraph Database["Database Layer"]
        direction TB
        subgraph Master["Master Database"]
            TenantInfo[Tenant Information]
            ShardMapping[Shard Mapping]
            Config[Configuration]
        end

        subgraph Shards["Shard Databases"]
            direction LR
            Shard1[(Shard 1)]
            Shard2[(Shard 2)]
            ShardN[(Shard N)]
        end

        subgraph NoSQL["MongoDB"]
            DocStore[Document Store]
        end

        subgraph Cache["Redis Cache"]
            CacheStore[Cache Store]
            DistributedLock[Distributed Lock]
        end
    end

    subgraph DataAccess["Data Access Layer"]
        direction TB
        ShardRouter[Shard Router]
        Repositories[Repositories]
        UnitOfWork[Unit of Work]
        QueryHandlers[Query Handlers]
    end

    %% Shard Routing
    ShardRouter --> TenantInfo
    ShardRouter --> ShardMapping
    ShardRouter --> Shards

    %% Data Access Flow
    Repositories --> ShardRouter
    Repositories --> EF
    Repositories --> Dapper
    Repositories --> DocStore
    Repositories --> CacheStore
    UnitOfWork --> EF
    QueryHandlers --> Dapper

    %% Shard Access
    ShardRouter -->|GroupId| Shard1
    ShardRouter -->|GroupId| Shard2
    ShardRouter -->|GroupId| ShardN
```

### Database Sharding Strategy
- **Master Database**
  - Stores tenant information and metadata
  - Maintains shard mapping configuration
  - Manages tenant configurations
  - Handles shard allocation

- **Shard Databases**
  - Contains tenant-specific data
  - Grouped by tenant groupId
  - Each shard is a separate database instance
  - Supports horizontal scaling

- **Shard Router**
  - Routes requests to appropriate shard
  - Uses groupId as sharding key
  - Manages shard connections
  - Handles shard failover

### Authentication Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Auth
    participant Redis

    Client->>API: Request with JWT
    API->>Auth: Validate Token
    Auth->>Redis: Check Token Status
    Redis-->>Auth: Token Status
    Auth-->>API: Validation Result
    API-->>Client: Response
```

### Caching Strategy
```mermaid
graph LR
    subgraph Cache["Cache Layer"]
        ICacheProvider[ICacheProvider]
        RedisProvider[RedisCacheProvider]
        Redis[(Redis Cache)]
    end

    subgraph Data["Data Layer"]
        DB[(SQL Server)]
        MongoDB[(MongoDB)]
    end

    subgraph App["Application"]
        Request[Request]
        Repository[Repository]
    end

    %% Cache Provider Implementation
    ICacheProvider --> RedisProvider
    RedisProvider --> Redis

    %% Data Flow
    Request --> Repository
    Repository --> ICacheProvider
    ICacheProvider --> RedisProvider
    RedisProvider --> Redis

    %% Cache Miss Flow
    RedisProvider -->|Cache Miss| Repository
    Repository -->|SQL Data| DB
    Repository -->|Document Data| MongoDB
    Repository -->|Cache Result| ICacheProvider
```

## Core Layer

The Core layer contains the essential business logic and domain models that are independent of any external concerns.

### Key Components
- **Domain Models**: Core business entities and value objects
- **Interfaces**: Core abstractions defining contracts
- **Domain Events**: Events that represent business state changes
- **Domain Services**: Complex business operations
- **Value Objects**: Immutable objects with no identity

## Modules Layer

Each module represents a bounded context in the system, following DDD principles. Each module is self-contained and includes its own presentation layer for exposing functionality.

### Module Structure
```
Module/
├── Domain/
│   ├── Entities/
│   ├── ValueObjects/
│   ├── Events/
│   └── Ports/          # Repository and service interfaces
├── Application/
│   ├── UseCases/
│   └── DTOs/
├── Infrastructure/
│   ├── Adapters/       # External service adapters
│   ├── Repositories/   # Repository implementations
│   └── Services/       # External service implementations
└── Presentation/
    ├── Restful/        # REST API Controllers
    ├── Background/     # Background Workers
    └── Grpc/          # gRPC Services
```

### Interface Implementation Strategy
- **Module-Specific Interfaces**: Implemented in the module's Infrastructure layer
- **Shared Interfaces**: Implemented in the Shared project
- **Core Interfaces**: Implemented in the Core layer

## Shared Layer

The Shared layer provides reusable infrastructure components and cross-cutting concerns that are used across the application.

### Authentication
- JWT token handling and validation
- Authentication middleware
- Identity management
- Multi-tenant authentication support

### Caching
- Redis cache implementation
- Cache invalidation strategies
- Distributed caching
- Cache-aside pattern implementation

### Logging
- Structured logging with Serilog
- Log correlation
- Log levels and filtering
- Audit trail logging

### Persistence
- Database connection management
- Repository base implementations
- Unit of work pattern
- Transaction management

### Message Queue
- Redis-based message queue
- Kafka command dispatcher
- Pub/Sub patterns
- Event handling
- Message persistence

### Distributed Lock
- Redis-based distributed locking
- Lock timeout management
- Deadlock prevention
- Lock renewal

### MultiTenancy
- Tenant identification
- Tenant-specific configuration
- Tenant isolation
- Tenant data filtering

### Mapping
- AutoMapper configurations
- Custom value resolvers
- Collection mapping
- Deep object mapping

### Dependency Injection
- Service registration
- Scoped lifetime management
- Module-specific DI configuration
- Service resolution

### Filtering
- Request/response filtering
- Validation filters
- Authorization filters
- Exception handling filters

### Middleware
- Request pipeline components
- Response transformation
- Error handling
- Performance monitoring

### Audit Trail
- User action tracking
- Change history
- Audit log persistence
- Audit log querying

### Redis Integration
- Connection management
- Key-value operations
- Pub/Sub operations
- Cache operations
- Distributed lock operations

## Presentation Layer

The Presentation layer handles HTTP requests and responses, implementing RESTful principles.

### Components
- API Controllers
- Middleware
- Filters
- OpenAPI documentation
- Request/Response models

## Data Access

### Entity Framework Core
- Used for complex domain models
- Implements repository pattern
- Supports migrations
- Handles relationships and complex queries

### Dapper
- Used for performance-critical queries
- Implements micro-ORM pattern
- Supports dynamic queries
- Handles simple CRUD operations

## Cross-Cutting Concerns

### Logging
- Serilog for structured logging
- Different log levels for different environments
- Correlation IDs for request tracking

### Caching
- Redis for distributed caching
- Cache invalidation strategies
- Cache-aside pattern implementation

### Authentication/Authorization
- JWT-based authentication
- Role-based authorization
- Multi-tenant support

### Validation
- FluentValidation for request validation
- Domain validation rules
- Custom validation attributes

## Testing Strategy

### Unit Testing
- xUnit for test framework
- Moq for mocking
- Focus on domain logic
- Test coverage requirements

### Integration Testing
- Test database interactions
- External service integration
- API endpoint testing

### Performance Testing
- Load testing
- Stress testing
- Endurance testing

## Security

### Authentication
- JWT token-based authentication
- Refresh token mechanism
- Token validation and revocation

### Authorization
- Role-based access control (RBAC)
- Policy-based authorization
- Resource-based authorization

### Data Protection
- Encryption at rest
- Encryption in transit
- Secure configuration management

## Message Processing

The system uses both Redis and Kafka for message processing, each with specific purposes:

### Redis Message Queue
- Used for lightweight, internal messaging
- Suitable for transient messages that don't require long-term persistence
- Implementation: `RedisMessageQueueProvider`

### Kafka Command Dispatcher
- Used for durable, distributed command processing
- Suitable for mission-critical events that require guaranteed delivery
- Supports higher throughput and scalability
- Implementation: `KafkaCommandDispatcher`

### Message Flow
```mermaid
graph LR
    subgraph Application["Application Layer"]
        Services[Services]
        UseCases[Use Cases]
    end

    subgraph Infrastructure["Infrastructure Layer"]
        CommandDispatcher[Command Dispatcher]
        MessageQueue[Message Queue]
    end

    subgraph MessageBrokers["Message Brokers"]
        Kafka[Kafka]
        Redis[Redis]
    end

    Services -->|Commands| CommandDispatcher
    UseCases -->|Events| MessageQueue
    CommandDispatcher -->|Durable Commands| Kafka
    MessageQueue -->|Transient Messages| Redis
```

### Command Types
The system uses two primary command types:
- **BaseCommand**: Standard commands for general operations
- **Domain Events**: Events that represent business state changes

### Integration Points
- Customer creation events (via Kafka)
- Order processing commands (via Kafka)
- Real-time notifications (via Redis)
- Cache invalidation messages (via Redis)

### For more details, see [Kafka Command Dispatcher](KAFKA_COMMAND_DISPATCHER.md) documentation.

## Best Practices

### Code Style
- Follow C# coding conventions
- Use async/await for asynchronous operations
- Implement proper exception handling
- Use dependency injection

### Performance
- Implement caching strategies
- Optimize database queries
- Use async operations
- Implement proper connection pooling

### Monitoring
- Application insights integration
- Health checks
- Performance metrics
- Error tracking

## Conclusion

This architecture document provides a comprehensive overview of the KvFnB Core project structure and implementation details. The hexagonal architecture ensures that the core business logic remains independent of external concerns, making the system maintainable, testable, and scalable.

For specific implementation details, refer to the respective module documentation and code comments. 