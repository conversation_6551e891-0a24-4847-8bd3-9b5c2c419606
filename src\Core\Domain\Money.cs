namespace KvFnB.Core.Domain
{
    public readonly struct Money : IEquatable<Money>, IComparable<Money>
    {
        public decimal Amount { get; }
        public string Currency { get; }

        private Money(decimal amount, string currency = "VND")
        {
            if (amount < 0)
                throw new ArgumentException("Amount cannot be negative", nameof(amount));
                
            if (string.IsNullOrEmpty(currency)) {
                currency = "VND";
            }
            Amount = amount;
            Currency = currency;
        }

        public static Money Create(decimal amount, string currency = "VND")
        {
            if (string.IsNullOrEmpty(currency)) {
                currency = "VND";
            }
            return new Money(amount, currency);
        }

        public static Money Zero(string currency = "VND") => new Money(0, currency);

        public Money Add(Money other)
        {
            EnsureSameCurrency(other);
            return new Money(Amount + other.Amount, Currency);
        }

        public Money Subtract(Money other)
        {
            EnsureSameCurrency(other);
            return new Money(Amount - other.Amount, Currency);
        }

        public Money MultiplyBy(decimal factor)
        {
            return new Money(Amount * factor, Currency);
        }

        private void EnsureSameCurrency(Money other)
        {
            if (Currency != other.Currency)
                throw new InvalidOperationException($"Cannot operate on different currencies: {Currency} and {other.Currency}");
        }

        #region Equality Members
        
        public bool Equals(Money other)
        {
            return Amount == other.Amount && 
                   string.Equals(Currency, other.Currency, StringComparison.OrdinalIgnoreCase);
        }

#pragma warning disable CS8765 // Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes).
        public override bool Equals(object obj)
#pragma warning restore CS8765 // Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes).
        {
            return obj is Money money && Equals(money);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Amount, Currency.ToUpperInvariant());
        }
        
        public static bool operator ==(Money left, Money right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(Money left, Money right)
        {
            return !left.Equals(right);
        }
        
        #endregion
        
        #region Comparison Members
        
        public int CompareTo(Money other)
        {
            if (!string.Equals(Currency, other.Currency, StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException($"Cannot compare different currencies: {Currency} and {other.Currency}");
            }
            
            return Amount.CompareTo(other.Amount);
        }
        
        public static bool operator <(Money left, Money right)
        {
            return left.CompareTo(right) < 0;
        }

        public static bool operator >(Money left, Money right)
        {
            return left.CompareTo(right) > 0;
        }

        public static bool operator <=(Money left, Money right)
        {
            return left.CompareTo(right) <= 0;
        }

        public static bool operator >=(Money left, Money right)
        {
            return left.CompareTo(right) >= 0;
        }
        
        #endregion
        
        public override string ToString()
        {
            return $"{Amount:N} {Currency}";
        }
    }
}