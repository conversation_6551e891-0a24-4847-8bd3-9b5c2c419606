using System.Linq.Expressions;

namespace KvFnB.Core.Validation
{
    public abstract class Validator<T> : IValidator<T>
    {
        private readonly Dictionary<Guid, List<ValidationRule<T>>> _rules = new Dictionary<Guid, List<ValidationRule<T>>>();
        private readonly List<string> _errors = new List<string>();

        protected RuleBuilder<T, TProperty> RuleFor<TProperty>(Expression<Func<T, TProperty>> expression)
        {
            var groupId = Guid.NewGuid();
            return new RuleBuilder<T, TProperty>(this, expression, groupId);
        }

        internal void AddRule(Guid groupId, Func<T, bool> rule, string errorMessage)
        {
            if (!_rules.TryGetValue(groupId, out List<ValidationRule<T>>? value))
            {
                value = [];
                _rules[groupId] = value;
            }

            value.Add(new ValidationRule<T>(rule, errorMessage));
        }

        public void RemoveRules(Guid groupId)
        {
            _rules.Remove(groupId);
        }

        internal List<ValidationRule<T>> GetRules()
        {
            return _rules.Values.SelectMany(g => g).ToList();
        }

        internal List<ValidationRule<T>> GetRules(Guid groupId)
        {
            return _rules.TryGetValue(groupId, out var rules) ? rules : [];
        }

        internal void ClearRules(Guid groupId)
        {
            if (_rules.TryGetValue(groupId, out List<ValidationRule<T>>? value))
            {
                value.Clear();
            }
        }

        internal void AddError(string errorMessage)
        {
            _errors.Add(errorMessage);
        }

        public ValidationResult Validate(T obj)
        {
            var errors = new List<string>();
            _errors.Clear();

            foreach (var rule in _rules.Values.SelectMany(g => g))
            {
                if (!rule.Rule(obj))
                {
                    errors.Add(rule.ErrorMessage);
                }
            }

            // Add any errors collected during validation
            errors.AddRange(_errors);

            return new ValidationResult(errors.Count == 0, errors);
        }
    }
}