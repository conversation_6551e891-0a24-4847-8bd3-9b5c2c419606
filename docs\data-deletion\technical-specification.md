# Technical Specification: <PERSON><PERSON><PERSON> Năng Xóa Dữ Liệu Gian Hàng

## 1. Tổng Quan

Chức năng xóa dữ liệu gian hàng là một tính năng quan trọng cho phép admin gian hàng có thể xóa dữ liệu định kỳ theo các điều kiện được cấu hình trước. <PERSON><PERSON><PERSON><PERSON> pháp kỹ thuật này bao gồm thiết kế cơ sở dữ liệu, kiến trú<PERSON> dịch vụ, quy trình xử lý, và các chiến lược xử lý vấn đề hiệu năng và khả năng mở rộng.

### 1.1. <PERSON><PERSON><PERSON> Tiêu Chính

- Cung cấp khả năng xóa dữ liệu có chọn lọc theo thời gian và điều kiện
- <PERSON><PERSON><PERSON> b<PERSON><PERSON> tính toàn vẹn dữ liệu sau khi xóa (s<PERSON> liệ<PERSON> ki<PERSON>ho, c<PERSON><PERSON> nợ)
- <PERSON><PERSON><PERSON><PERSON> kế hệ thống an toàn với xác thực nhiều lớp
- Cho phép khôi phục dữ liệu trong thời gian cho phép (7 ngày)
- Đảm bảo hiệu năng cao và khả năng mở rộng

## 2. Thiết Kế Cơ Sở Dữ Liệu

### 2.1. Bảng Quản Lý Yêu Cầu Xóa Dữ Liệu

```sql
CREATE TABLE DeleteDataRequest (
    Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
    RetailerId INT NOT NULL,
    CreatedBy BIGINT NOT NULL,  -- UserId của người yêu cầu xóa
    ModifiedBy BIGINT NULL, 
    Status TINYINT NOT NULL,  -- 0: Pending, 1: Approved, 2: Processing, 3: Completed, 4: Failed, 5: Cancelled
    ScheduleType TINYINT NOT NULL, -- 0: Manual, 1: Schedule
    BranchIds VARCHAR(MAX) NOT NULL,
    Type TINYINT NOT NULL, -- 0: Manual, 1: Scheduled
    FilterCondition NVARCHAR(MAX) NULL, -- JSON chứa các điều kiện lọc
    ScheduleConfig NVARCHAR(MAX) NULL, -- JSON chứa cấu hình lịch nếu là lập lịch
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME NULL,
    OtpCode NVARCHAR(10) NULL,
    OtpExpiredDate DATETIME NULL,
    OtpVerified BIT NOT NULL DEFAULT 0,
    ExecuteDate DATETIME NULL,
    CompletedDate DATETIME NULL,
    NotificationSent BIT NOT NULL DEFAULT 0,
    RestoreDeadline DATETIME NULL, -- Hạn chót khôi phục (7 ngày từ khi xóa xong)
    Email NVARCHAR(200)  NULL,
    CONSTRAINT FK_DeleteDataRequest_Retailer FOREIGN KEY (RetailerId) REFERENCES Retailer(Id),
    CONSTRAINT FK_DeleteDataRequest_User FOREIGN KEY (CreatedBy) REFERENCES [User](Id)
);
CREATE INDEX IX_DeleteDataRequest_RetailerId ON DeleteDataRequest(RetailerId);
CREATE INDEX IX_DeleteDataRequest_Status ON DeleteDataRequest(Status);

```

### 2.2. Bảng Lưu Trữ Chi Tiết Xóa Dữ Liệu

```sql
CREATE TABLE ProcessingDataJob (
  Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  RequestId BIGINT NOT NULL,
  RetailerId INT NOT NULL,
  BranchId INT NOT NULL,
  Status TINYINT NOT NULL, -- 0: Pending, 1: Processing, 2: Completed, 3: Failed
  CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
  ModifiedDate DATETIME NULL DEFAULT GETDATE(),
  CONSTRAINT FK_ProcessingDataJobs_Request FOREIGN KEY (RequestId) REFERENCES DeleteDataRequest(Id)
);

CREATE INDEX IX_ProcessingDataJob_RetailerId_RequestId ON ProcessingDataJob(RetailerId, RequestId);
```

### 2.3. Bảng Lịch Sử Xóa Dữ Liệu

```sql
CREATE TABLE DeleteDataDetail (
  Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  RequestId BIGINT NOT NULL,
  BranchId INT NOT NULL,
  ProcessingDataJobId BIGINT NOT NULL,
  Type NVARCHAR(100) NOT NULL,
  CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
  RecordCount INT NOT NULL, -- Số lượng bản ghi đã được cập nhật RetailerId
  RetailerId INT NOT NULL, -- RetailerId ban đầu
  Status TINYINT NOT NULL, -- 0: Pending, 1: Processed, 2: Failed, 3: Restored
  ProcessedTime INT NOT NULL, -- Thời gian thực hiện theo giây
  ProcessedDate DATETIME NULL,
  FilterConditions NVARCHAR(MAX) NULL, -- JSON chứa các điều kiện lọc
  ErrorMessage NVARCHAR(1000) NULL,
  CONSTRAINT FK_DeleteDataDetail_Request FOREIGN KEY (RequestId) REFERENCES DeleteDataRequest(Id)
);

CREATE INDEX IX_DeleteDataDetail_RequestId ON DeleteDataDetail(RequestId);
CREATE INDEX IX_DeleteDataDetail_RetailerId ON DeleteDataDetail(RetailerId);
CREATE INDEX IX_DeleteDataDetail_ProcessingDataJobId ON ProcessingDataJob(Id);
```

```sql
CREATE TABLE DeleteDataHistory (
  Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  RetailerId INT NOT NULL,
  RequestId BIGINT NOT NULL,
  BranchId INT NOT NULL,
  Summary NVARCHAR(MAX) NOT NULL, -- JSON tóm tắt số lượng bản ghi đã xóa theo loại
  ExecutedBy BIGINT NOT NULL, -- UserId của người thực hiện (có thể là system)
  ExecutedDate DATETIME NOT NULL,
  ProcessingDataJobId BIGINT NOT NULL,
  Status TINYINT NOT NULL, --
  FilterConditions NVARCHAR(MAX) NULL, 
  Email VARCHAR(200)  NULL,
  CONSTRAINT FK_DeleteDataHistory_Retailer FOREIGN KEY (RetailerId) REFERENCES Retailer(Id),
  CONSTRAINT FK_DeleteDataHistory_Request FOREIGN KEY (RequestId) REFERENCES DeleteDataRequest(Id),
  CONSTRAINT FK_DeleteDataHistory_User FOREIGN KEY (ExecutedBy) REFERENCES [User](Id)
);

CREATE INDEX IX_DeleteDataHistory_RetailerId ON DeleteDataHistory(RetailerId);
CREATE INDEX IX_DeleteDataHistory_RequestId ON DeleteDataHistory(RequestId);
```

### 2.4. Bảng Lưu Trữ Phiếu Cân Bằng Tự Động

```sql
CREATE TABLE BalanceAdjustmentAutoGenerated (
  Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  RetailerId INT NOT NULL,
  BranchId BIGINT NOT NULL,
  BalanceAdjustmentId BIGINT NOT NULL, -- ID của phiếu cân bằng chính
  RequestId BIGINT NOT NULL, -- ID của yêu cầu xóa
  Type TINYINT NOT NULL, -- 0: Kiểm kho, 1: Công nợ
  CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
  CONSTRAINT FK_BalanceAdjustmentAutoGenerated_Retailer FOREIGN KEY (RetailerId) REFERENCES Retailer(Id),
  CONSTRAINT FK_BalanceAdjustmentAutoGenerated_Request FOREIGN KEY (RequestId) REFERENCES DeleteDataRequest(Id)
);

CREATE INDEX IX_BalanceAdjustmentAutoGenerated_RetailerId ON BalanceAdjustmentAutoGenerated(RetailerId);
CREATE INDEX IX_BalanceAdjustmentAutoGenerated_RequestId ON BalanceAdjustmentAutoGenerated(RequestId);
```

## 3. Kiến Trúc Dịch Vụ

### 3.1. Sơ Đồ Tổng Thể

```
+------------------------+ +------------------------+ +------------------------+
|                        |                          |                          |
| MHQL Service      +----->+    Delete Data Service +----->+ Scheduler Service |
|                        |                          |                          |
+------------------------+ +------------+-----------+ +------------------------+
                                        |
                                        v
+------------------------+ +------------------------+ +------------------------+
|                        |                          |                          |
| CRM Validation Service|<-----+ Data Processing Service+----->+ Notification Service |
|                        |                          |                          |
+------------------------+ +------------------------+ +------------------------+
```

### 3.2. Các Thành Phần Chính

#### 3.2.1. MHQL Service
- API quản lý yêu cầu xóa dữ liệu
- Giao diện quản lý và theo dõi tiến trình
- Xác thực quyền admin gian hàng

#### 3.2.2. Delete Data Service
- API xử lý yêu cầu xóa dữ liệu
- Xác thực và phân tích yêu cầu
- Lập lịch trình xóa dữ liệu

#### 3.2.3. Scheduler Service
- Quản lý các lịch trình xóa tự động
- Lấy dữ liệu từ bảng ```DeleteDataRequest```
- Retry mechanism cho các tác vụ thất bại

#### 3.2.4. Data Processing Service
- Dịch vụ thực hiện cập nhật RetailerId (giá trị âm)
- Xử lý phiếu cân bằng kiểm kho và công nợ
- Ghi log chi tiết hoạt động cập nhật

#### 3.2.5. CRM Validation Service
- Xác thực số điện thoại thông qua CRM
- Quản lý OTP và xác thực

#### 3.2.6. Notification Service
- Gửi thông báo email khi xóa thành công
- Thông báo khi gần đến hạn khôi phục

## 4. Quy Trình Xử Lý Xóa Dữ Liệu

### 4.1. Quy Trình Tạo Yêu Cầu Xóa
### 4.2. Quy Trình Xử Lý Xóa Dữ Liệu Bằng Cách Thay Đổi RetailerId
### 4.3. Quy Trình Khôi Phục Dữ Liệu

## 5. Chi Tiết Kỹ Thuật

### 5.1. Danh Sách Bảng Bị Ảnh Hưởng

Dựa vào phân tích cơ sở dữ liệu, các bảng sau đây là các bảng chính có chứa cột RetailerId cần được xử lý:

#### 5.1.1. Nhóm Bảng Giao Dịch Chính
1. Invoice - Hóa đơn chính
2. InvoiceDetail - Chi tiết hóa đơn
3. Payment - Thanh toán
4. PurchaseOrder - Đơn hàng nhập
5. PurchaseOrderDetail - Chi tiết đơn hàng nhập
6. PurchaseReturn - Trả hàng nhập
7. PurchaseReturnDetail - Chi tiết trả hàng nhập
8. Return - Trả hàng bán
9. ReturnDetail - Chi tiết trả hàng bán
10. Transfer - Chuyển kho
11. TransferDetail - Chi tiết chuyển kho
12. CashFlow - Dòng tiền
13. ManufacturingDetail - Chi tiết sản xuất

#### 5.1.2. Nhóm Bảng Theo Dõi
1. InventoryTracking - Theo dõi tồn kho
2. BalanceTracking - Theo dõi công nợ
3. PointTracking - Theo dõi điểm thưởng

#### 5.1.3. Nhóm Bảng Thiết Lập
1. Product - Sản phẩm
2. Customer - Khách hàng
3. Supplier - Nhà cung cấp
4. Branch - Chi nhánh
5. User - Người dùng

#### 5.1.4. Các Bảng Liên Quan Khác
1. EInvoice - Hóa đơn điện tử
2. PartnerOrder - Đơn đặt hàng từ đối tác
3. StockTake - Kiểm kho
4. StockTakeDetail - Chi tiết kiểm kho

### 5.2. Chiến Lược Xác Định và Cập Nhật Dữ Liệu

1. Xác định dữ liệu theo điều kiện thời gian và bộ lọc:
   - Lọc theo thời gian giao dịch (PurchaseDate, CreatedDate, etc.)
   - Áp dụng các điều kiện bổ sung (trạng thái, loại giao dịch, etc.)

2. Thứ tự ưu tiên cập nhật RetailerId:
   - Tiên quyết: Kiểm tra hóa đơn đã đồng bộ thuế hay chưa (EInvoice)
   - Thứ tự từ con đến cha: Cập nhật các bảng con trước, bảng cha sau để tránh vi phạm ràng buộc

3. Chiến lược xử lý:
   - Với mỗi bảng, tạo stored procedure cập nhật RetailerId theo điều kiện
   - Ghi lại số lượng bản ghi bị ảnh hưởng để báo cáo

### 5.2. Cấu Trúc Dữ Liệu Lập Lịch

```json
{
  "scheduleType": "monthly", // "daily", "weekly", "monthly", "quarterly", "yearly"
  "dayOfWeek": null, // 0-6 (chỉ áp dụng cho weekly)
  "dayOfMonth": 15, // 1-31 (chỉ áp dụng cho monthly)
  "month": null, // 1-12 (chỉ áp dụng cho yearly)
  "quarterMonth": null, // 1-3 (chỉ áp dụng cho quarterly)
  "executionTime": "02:00:00", // Giờ thực hiện
  "retentionMonths": 3, // Số tháng giữ lại dữ liệu
  "active": true,
  "nextExecutionDate": "2025-06-15T02:00:00"
}
```

### 5.3. Chiến Lược Xử Lý RetailerId

Để đơn giản hóa việc xóa và khôi phục dữ liệu, chúng ta sẽ sử dụng cách tiếp cận đảo dấu RetailerId và thêm thông tin để quản lý nhiều lần xóa:

1. Cập nhật RetailerId thành giá trị âm có định danh: Dùng công thức có thể nhận biết được lần xóa

```sql
-- RetailerId ban đầu: 123
-- RequestId (ID yêu cầu xóa): 456
-- Giá trị RetailerId mới = RequestId
-- Kết quả: -1230456

UPDATE [TableName]
SET RetailerId = @RequestId, ModifiedDate = GETDATE()
WHERE RetailerId > 0
AND RetailerId = @OriginalRetailerId
AND [Các điều kiện lọc khác]
```

Cách này đảm bảo:
- Giá trị âm (để ẩn khỏi queries thông thường)
- Có thể truy ngược RequestId (từ 4 chữ số cuối)
- Giữ được giá trị RetailerId gốc (phần nguyên khi chia cho 10000)

2. Lưu trữ thông tin: Lưu chi tiết trong bảng DeleteDataDetail gồm các thông tin:

```sql
INSERT INTO DeleteDataDetail (
  RequestId,
  Type,
  RecordCount,
  OriginalRetailerId,
  Status,
  ProcessedDate
)
VALUES (
  @RequestId,
  @TableName,
  @RecordCount,
  @OriginalRetailerId,
  1,
  GETDATE()
)
```

3. Application Layer Filter: Tất cả queries chỉ lấy dữ liệu có RetailerId > 0

### 5.4. Cách Xử Lý Cân Bằng Kiểm Kho và Công Nợ

1. Trước khi cập nhật RetailerId:
   - Tính toán số dư cuối cùng của tồn kho và công nợ bị ảnh hưởng
   - Tạo phiếu cân bằng tự động với thông tin tham chiếu đến yêu cầu xóa

2. Sau khi cập nhật RetailerId:
   - Cập nhật EndingStocks và Balance cho bản ghi cuối cùng

### 5.5. Cơ Chế Khôi Phục Dữ Liệu Linh Hoạt

1. Khôi phục theo lần xóa:
   - Hiển thị danh sách các lần xóa dữ liệu cho admin hệ thống
   - Cho phép chọn yêu cầu xóa cụ thể để khôi phục

```sql
-- Lấy danh sách các lần xóa dữ liệu của retailer
SELECT Id, CreatedDate, RequestedBy, FromDate, ToDate
FROM DeleteDataRequest
WHERE RetailerId = @RetailerId AND Status = 3 -- Completed
ORDER BY CreatedDate DESC
```

2. Quy trình khôi phục:
   - Lấy thông tin chi tiết về yêu cầu xóa đã chọn
   - Trích xuất RequestId từ RetailerId âm để xác định đúng dữ liệu cần khôi phục
   - Cập nhật Status = 3 (Restored) trong bảng DeleteDataDetail

```sql
-- Khôi phục dữ liệu cho một yêu cầu cụ thể
UPDATE [TableName]
SET RetailerId = ABS(RetailerId) / 10000 -- Lấy lại RetailerId gốc
WHERE RetailerId < 0
AND (ABS(RetailerId) % 10000) = @RequestId % 10000 -- Chỉ khôi phục dữ liệu từ yêu cầu này
```

3. Xử lý nhiều lần xóa chồng chéo:
   - Hiển thị cảnh báo khi khôi phục có thể gây xung đột
   - Cho phép chọn ưu tiên (giữ dữ liệu cũ hơn hoặc mới hơn)
   - Ghi log chi tiết hoạt động khôi phục

## 6. Chiến Lược Xử Lý Hiệu Năng

### 6.1. Tối Ưu Database

1. Tối Ưu Truy Vấn Cập Nhật RetailerId:
   - Tạo filtered index trên cột RetailerId để tăng tốc truy vấn
   - Đảm bảo có index trên các cột thường xuyên dùng để lọc (CreatedDate, Status, etc.)

```sql
-- Mẫu câu truy vấn trong ứng dụng
SELECT * FROM [TableName] WHERE RetailerId > 0 AND [Các điều kiện khác]

CREATE INDEX IX_TableName_RetailerId_Filtered ON [TableName](RetailerId)
WHERE RetailerId > 0
```

2. Xử Lý Cập Nhật Theo Batch:
   - Chia nhỏ quá trình cập nhật thành các batch (500-1000 bản ghi/lần)
   - Sử dụng transaction để đảm bảo tính nhất quán giữa các bảng liên quan

3. Bảng Tạm Thời:
   - Tạo bảng tạm để lưu danh sách các bản ghi cần cập nhật RetailerId
   - Sử dụng JOIN với bảng tạm để tối ưu hiệu năng

```sql
SELECT * INTO #TempTableToUpdate
FROM [TableName]
WHERE RetailerId = @OriginalRetailerId AND [Các điều kiện lọc]

UPDATE t
SET t.RetailerId = @NegativeRetailerId
FROM [TableName] t
JOIN #TempTableToUpdate tmp ON t.Id = tmp.Id
```

### 6.2. Kiến Trúc Microservice Scalable

1. Horizontal Scaling:
   - Triển khai các service trên Kubernetes
   - Auto-scaling dựa trên tải hệ thống

2. Xử Lý Bất Đồng Bộ:
   - Sử dụng hàng đợi (RabbitMQ/Kafka) cho các tác vụ xóa
   - Giám sát tiến trình qua message

3. Cân Bằng Tải:
   - Phân phối yêu cầu xóa dữ liệu qua nhiều worker
   - Giới hạn tài nguyên cho mỗi worker

### 6.3. Thiết Kế Worker HA (High Availability)

#### 6.3.1. Mô Hình Worker HA

```
┌────────────────┐ ┌────────────────┐ ┌────────────────┐
│ Worker Node 1 │ │ Worker Node 2 │ │ Worker Node 3 │
│               │ │               │ │               │
│ ┌────────────┐ │ │ ┌────────────┐ │ │ ┌────────────┐ │
│ │Job Scanner │ │ │ │Job Scanner │ │ │ │Job Scanner │ │
│ │(Lock Owner)│ │ │ │(Standby)   │ │ │ │(Standby)   │ │
│ └──────┬─────┘ │ │ └────────────┘ │ │ └────────────┘ │
│        │       │ │               │ │               │
│ ┌──────┴─────┐ │ │ ┌────────────┐ │ │ ┌────────────┐ │
│ │Job Enqueuer│ │ │ │Job Processor│ │ │Job Processor│ │
│ └────────────┘ │ │ └────────────┘ │ │ └────────────┘ │
└────────────────┘ └────────────────┘ └────────────────┘
       │                 ▲                 ▲
       │                 │                 │
       ▼                 │                 │
┌────────────────┐       │                 │
│                │       │                 │
│ Message Queue  ├───────┴─────────────────┘
│                │
└────────────────┘
```

#### 6.3.2. Cơ Chế Distributed Locking

1. Tách Job Scanner và Job Processor:
   - Job Scanner: Đọc từ database và đẩy vào queue
   - Job Processor: Lấy từ queue và xử lý

2. Distributed Locking với Redis/ZooKeeper:

```csharp
// Code trong Job Scanner service
public async Task ScanAndEnqueue()
{
  // Cố gắng lấy khóa phân tán
  var lockAcquired = await distributedLock.TryAcquireAsync("delete_job_scanner_lock", TimeSpan.FromMinutes(1));
  
  if (lockAcquired)
  {
    try
    {
      // Chỉ instance có được lock mới quét dữ liệu và đẩy vào queue
      var pendingRequests = await dbContext.DeleteDataRequests
        .Where(r => r.Status == RequestStatus.Approved)
        .OrderBy(r => r.CreatedDate)
        .Take(20)
        .ToListAsync();
      
      foreach (var request in pendingRequests)
      {
        // Phân tách thành các job nhỏ
        var batches = await CreateBatchesForRequest(request);
        
        // Đẩy vào queue với định danh duy nhất
        foreach (var batch in batches)
        {
          await messageQueue.EnqueueAsync("delete_data_jobs", batch);
        }
        
        // Cập nhật trạng thái thành Processing
        request.Status = RequestStatus.Processing;
        await dbContext.SaveChangesAsync();
      }
    }
    finally
    {
      // Đảm bảo luôn giải phóng lock khi xong
      await distributedLock.ReleaseAsync("delete_job_scanner_lock");
    }
  }
  // Nếu không lấy được lock, instance này sẽ chờ và thử lại sau
}
```

#### 6.3.3. Đảm Bảo Idempotency và Theo Dõi Job

1. Bảng Theo Dõi Job:

```sql
CREATE TABLE ProcessingDataJob (
  Id NVARCHAR(50) PRIMARY KEY,
  RequestId BIGINT NOT NULL,
  TableName NVARCHAR(100) NOT NULL,
  RetailerId INT NOT NULL,
  BatchNumber INT NOT NULL,
  TotalBatches INT NOT NULL,
  Status TINYINT NOT NULL, -- 0: Pending, 1: Processing, 2: Completed, 3: Failed
  ProcessorId NVARCHAR(50) NULL, -- ID của instance xử lý
  StartTime DATETIME NULL,
  EndTime DATETIME NULL,
  ProcessedCount INT NOT NULL DEFAULT 0,
  ErrorMessage NVARCHAR(1000) NULL,
  CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
  LastUpdateTime DATETIME NOT NULL DEFAULT GETDATE(),
  CONSTRAINT FK_ProcessingDataJobs_Request FOREIGN KEY (RequestId) REFERENCES DeleteDataRequest(Id)
);

CREATE INDEX IX_ProcessingDataJobs_RequestId ON ProcessingDataJobs(RequestId);
CREATE INDEX IX_ProcessingDataJobs_Status ON ProcessingDataJobs(Status);
CREATE INDEX IX_ProcessingDataJobs_ProcessorId ON ProcessingDataJobs(ProcessorId);
```

2. Xử Lý Idempotent:

```csharp
// Mô hình Job
public class DeleteDataBatchJob
{
  public string JobId { get; set; } = Guid.NewGuid().ToString(); // Định danh duy nhất
  public long RequestId { get; set; }
  public string TableName { get; set; }
  public int RetailerId { get; set; }
  public int BatchNumber { get; set; }
  public int TotalBatches { get; set; }
  public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
  
  // Các thông tin filter
  public DateTime? FromDate { get; set; }
  public DateTime? ToDate { get; set; }
  public string FilterCondition { get; set; }
}

// Code trong Job Processor
public async Task ProcessJob(DeleteDataBatchJob job)
{
  // Kiểm tra xem job đã được xử lý chưa
  if (await IsJobProcessed(job.JobId))
  {
    logger.LogInformation($"Job {job.JobId} đã được xử lý trước đó, bỏ qua");
    return;
  }
  
  try
  {
    // Mark job as processing to avoid duplicate processing
    await MarkJobAsProcessing(job.JobId);
    
    // Xử lý cập nhật RetailerId
    var result = await UpdateRetailerIdForBatch(job);
    
    // Ghi nhận kết quả
    await MarkJobAsCompleted(job.JobId, result);
  }
  catch (Exception ex)
  {
    logger.LogError(ex, $"Lỗi xử lý job {job.JobId}");
    await MarkJobAsFailed(job.JobId, ex.Message);
    // Có thể retry tùy theo policy
  }
}
```

#### 6.3.4. Auto-Failover và Job Recovery

```csharp
// Chạy định kỳ để phát hiện job bị treo
public async Task RecoverStalledJobs()
{
  var threshold = DateTime.UtcNow.AddMinutes(-10); // 10 phút timeout
  
  var stalledJobs = await dbContext.ProcessingDataJobs
    .Where(j => j.Status == JobStatus.Processing && j.LastUpdateTime < threshold)
    .ToListAsync();
  
  foreach (var job in stalledJobs)
  {
    logger.LogWarning($"Phát hiện job treo: {job.JobId}, đặt lại trạng thái");
    job.Status = JobStatus.Pending;
    job.ProcessorId = null;
    job.LastUpdateTime = DateTime.UtcNow;
  }
  
  await dbContext.SaveChangesAsync();
}
```

# Đặc tả kỹ thuật: Triển khai HA trên Kubernetes và các chiến lược phụ trợ

## 6.3.5. Triển khai HA trên Kubernetes

Hệ thống sẽ được triển khai trên nền tảng Kubernetes để đảm bảo tính sẵn sàng cao (High Availability) và khả năng mở rộng. Cấu hình triển khai được mô tả như sau:

```yaml
# worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-deletion-worker
spec:
  replicas: 3 # Số lượng instance
  selector:
    matchLabels:
      app: data-deletion-worker
  template:
    metadata:
      labels:
        app: data-deletion-worker
    spec:
      containers:
      - name: data-deletion-worker
        image: your-registry/data-deletion-worker:latest
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: DB_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: db-secrets
              key: connection-string
        - name: REDIS_CONNECTION
          valueFrom:
            secretKeyRef:
              name: redis-secrets
              key: connection-string
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 15
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
```

### Các đặc điểm chính:

- **Auto-scaling**: Số lượng worker pod được cấu hình ban đầu là 3, có thể tự động mở rộng dựa trên tải hệ thống
- **Resource Management**: Mỗi worker được cấp phát tài nguyên cụ thể (CPU, RAM) để tránh cạnh tranh tài nguyên
- **Health Monitoring**: Sử dụng liveness và readiness probe để kiểm tra sức khỏe của service
- **Secret Management**: Các thông tin nhạy cảm như connection string được lưu trong Kubernetes Secrets
- **Pod Identity**: Mỗi worker có một định danh duy nhất (WORKER_ID) để phục vụ cho việc theo dõi và ghi log

## 6.4. Chiến lược lập lịch thông minh

### 1. Xác định thời điểm tối ưu:
- Ưu tiên thực hiện vào thời gian ít tải (đêm khuya, từ 0h đến 5h sáng)
- Phân bổ các tác vụ xóa dữ liệu lớn thành nhiều đợt, thực hiện trên nhiều ngày
- Tránh các thời điểm cao điểm sử dụng của người dùng (9h-11h sáng, 14h-16h chiều)

### 2. Quản lý tài nguyên:
- Giới hạn số lượng yêu cầu xóa đồng thời (mặc định: tối đa 5 yêu cầu/retailer)
- Theo dõi tài nguyên hệ thống và tự động tạm dừng các job khi CPU/Memory đạt ngưỡng cao
- Cấu hình độ ưu tiên cho các job để đảm bảo job quan trọng được xử lý trước

## 7. Xử lý rủi ro và tình huống đặc biệt

### 7.1. Mất kết nối/lỗi hệ thống

#### 1. Cơ chế Retry với cập nhật RetailerId:
- Retry tự động các tác vụ cập nhật RetailerId thất bại với exponential backoff (30s, 1m, 2m, 5m, 10m)
- Lưu trạng thái tiến độ chi tiết trong bảng DeleteDataDetail
- Giới hạn số lần retry (tối đa 5 lần) để tránh tác vụ treo vô hạn

#### 2. Khôi phục Transaction:
```sql
BEGIN TRANSACTION
-- Cập nhật RetailerId
-- ...
IF @@ERROR <> 0
BEGIN
  ROLLBACK TRANSACTION
  -- Ghi log lỗi
  RETURN
END
COMMIT TRANSACTION
```

- Sử dụng transaction cho mỗi batch cập nhật
- Rollback toàn bộ batch nếu có lỗi xảy ra trong quá trình xử lý
- Ghi nhận thông tin lỗi chi tiết để phục vụ việc debug

#### 3. Cập nhật trạng thái job:
```sql
UPDATE DeleteDataDetail
SET Status = CASE WHEN ErrorMessage IS NULL THEN 1 ELSE 2 END,
    ProcessedDate = GETDATE(),
    ErrorMessage = @ErrorMessage
WHERE RequestId = @RequestId AND Type = @TableName
```

### 7.2. Xử lý trường hợp đặc biệt

#### 1. Xử lý Foreign Key Constraints:
- Xác định thứ tự cập nhật các bảng để tránh vi phạm ràng buộc
- Áp dụng cách tiếp cận "bottom-up" (từ bảng con lên bảng cha)
- Ví dụ thứ tự xử lý:
  1. InvoiceDetail, PurchaseOrderDetail, ReturnDetail
  2. Invoice, PurchaseOrder, Return
  3. Payment, CashFlow
  4. InventoryTracking, BalanceTracking

#### 2. Xử lý dữ liệu đã bị thay đổi:
- Kiểm tra và xử lý các bản ghi đã được cập nhật hoặc xóa trong khoảng thời gian chuẩn bị
- Cơ chế kiểm tra "last modified" trước khi cập nhật
- Báo cáo chi tiết các bản ghi không thể xử lý vì đã thay đổi

#### 3. Xử lý dữ liệu đặc biệt (EInvoice):
- Kiểm tra trạng thái EInvoice trước khi cập nhật RetailerId
- Chỉ cho phép cập nhật khi hóa đơn chưa đồng bộ với thuế
- Loại trừ các hóa đơn đã phát hành khỏi tiến trình cập nhật

### 7.3. Vấn đề bảo mật

#### 1. Xác thực đa lớp:
- Xác thực admin gian hàng (JWT token với quyền admin)
- Xác thực qua OTP trên số điện thoại chủ hợp đồng (6 chữ số, thời gian hiệu lực 5 phút)
- API riêng biệt cho khôi phục chỉ dành cho admin hệ thống
- Sử dụng HTTPS cho tất cả các API endpoints

#### 2. Theo dõi hoạt động:
- Ghi log đầy đủ các hoạt động cập nhật RetailerId
- Lưu thông tin người yêu cầu, thời gian, số lượng bản ghi ảnh hưởng
- Tạo báo cáo audit log cho mỗi lần xóa dữ liệu

### 7.4. Quản lý hiệu năng hệ thống

#### 1. Rate Limiting:
- Giới hạn số lượng yêu cầu cập nhật RetailerId theo retailerId (tối đa 5 yêu cầu/ngày)
- Giới hạn kích thước dữ liệu cập nhật mỗi lần (tối đa 100,000 bản ghi/yêu cầu)
- Sử dụng Redis để quản lý các bộ đếm rate limit

#### 2. Xử lý tác động tới Application Layer:
- Đảm bảo tất cả các queries và stored procedures đều có điều kiện RetailerId > 0
- Cập nhật indexes để tối ưu cho việc lọc RetailerId > 0
- Đánh giá và tối ưu các truy vấn hiện có để tránh ảnh hưởng hiệu năng

## 8. Kế hoạch triển khai (2 tuần)

### 8.1. Ước tính nhân sự
- Senior Backend Engineers: 6 người
- DevOps Engineer: 1 người
- QA Engineer: 2 người
- Frontend Engineer: 2 người
- Database Engineer: 1 người

### 8.2. Phân bổ nhân sự và công việc

| Nhóm | Kỹ sư | Công việc | Timeline |
|------|-------|-----------|----------|
| Database | 1 Senior DB Engineer | Thiết lập schema, indexes, partitioning, stored procedures | Tuần 1 (3 ngày) |
| Core Services | 3 Senior Backend Engineers | Delete Data Service, Data Processing Service, Backup Service | Tuần 1-2 |
| Support Services | 2 Senior Backend Engineers | Scheduler Service, CRM Integration, Notification Service | Tuần 1-2 |
| Frontend | 2 Frontend Engineers | Admin Portal, UI/UX screens | Tuần 1-2 |
| DevOps | 1 DevOps Engineer | CI/CD pipeline, Kubernetes, monitoring | Tuần 1-2 |
| QA | 2 QA Engineers | Automated tests, performance testing | Tuần 1-2 |

### 8.3. Lịch triển khai chi tiết

#### Tuần 1 (Ngày 1-5)
**Ngày 1-2:**
- Database: Thiết lập schema và stored procedures
- Core Team: Thiết kế API, setup project structure
- Support Team: Thiết kế service interfaces
- Frontend: UI mockups và component design

**Ngày 3-5:**
- Database: Hoàn thiện indices, partitioning, và data migration scripts
- Core Team: Phát triển Delete Data API và Data Processing Service (50%)
- Support Team: Phát triển Scheduler Service và CRM Validation (50%)
- Frontend: Phát triển UI screens (50%)
- DevOps: Cấu hình môi trường dev và CI/CD
- QA: Xây dựng test cases và automation framework

#### Tuần 2 (Ngày 6-10)
**Ngày 6-8:**
- Core Team: Hoàn thiện Delete Data API, Data Processing và Backup Service
- Support Team: Hoàn thiện Scheduler và Notification Service
- Frontend: Hoàn thiện UI/UX và integration tests
- QA: Thực hiện functional testing và performance testing
- DevOps: Monitoring setup và logging

**Ngày 9-10:**
- Tích hợp end-to-end testing
- Performance tuning và bug fixing
- User acceptance testing
- Documentation hoàn chỉnh
- Triển khai production

### 8.4. Chiến lược tăng tốc

#### 1. Phát triển song song:
- Các microservices được phát triển đồng thời bởi các nhóm khác nhau
- API contracts được định nghĩa từ sớm để đảm bảo tích hợp suôn sẻ
- Sử dụng mocks API để các team có thể phát triển độc lập

#### 2. Tái sử dụng Components:
- Tận dụng các thư viện và frameworks có sẵn
- Sử dụng template và boilerplate code hiện có
- Kế thừa các modules đã được phát triển từ các dự án trước

#### 3. Feature Prioritization:
- Tập trung vào tính năng core trước (xóa dữ liệu cơ bản)
- Các tính năng nâng cao (lập lịch phức tạp, báo cáo chi tiết) có thể triển khai ở phase 2
- Sử dụng phương pháp MoSCoW để ưu tiên các tính năng (Must have, Should have, Could have, Won't have)

#### 4. Automated Testing:
- Tập trung vào automated testing để giảm thời gian QA
- Sử dụng CI/CD để automated deployment
- Unit tests bao phủ ít nhất 80% code base

#### 5. Daily Sync:
- Họp đồng bộ ngắn 2 lần/ngày để nhanh chóng giải quyết các vấn đề chặn
- Sử dụng công cụ collaboration (Slack, Teams) để giao tiếp real-time
- Bảng Kanban để theo dõi tiến độ và phát hiện bottlenecks

## 9. Monitoring và bảo trì

### 9.1. Giám sát hệ thống
- Theo dõi hiệu năng xử lý yêu cầu xóa
- Giám sát tài nguyên hệ thống (CPU, memory, disk I/O, network)
- Alert cho các tác vụ thất bại (email, SMS, webhook tích hợp)
- Dashboard real-time hiển thị:
  * Số lượng yêu cầu đang xử lý
  * Tỷ lệ thành công/thất bại
  * Thời gian xử lý trung bình
  * Tài nguyên hệ thống

### 9.2. Kế hoạch backup
- Backup định kỳ metadata xóa dữ liệu (mỗi 6 giờ)
- Lưu trữ dài hạn cho dữ liệu mã hóa (90 ngày)
- Kiểm tra tính toàn vẹn backup định kỳ (mỗi tuần)
- Thực hiện restore test mỗi tháng

### 9.3. Cập nhật và nâng cấp
- Lập kế hoạch cho các bảng mới (quy trình review và approval)
- Cập nhật quy trình xóa khi có thay đổi schema
- Nâng cấp thuật toán mã hóa nếu cần
- Quy trình triển khai updates:
  * Testing trên môi trường staging
  * Kiểm tra compatibility
  * Blue-green deployment để giảm thiểu downtime

## 10. Kết luận

Giải pháp kỹ thuật này cung cấp một framework đơn giản, hiệu quả và tối ưu để triển khai tính năng xóa dữ liệu gian hàng, đáp ứng tất cả yêu cầu nghiệp vụ và phi nghiệp vụ đã đề ra.

Bằng cách sử dụng phương pháp cập nhật RetailerId từ giá trị dương sang âm, giải pháp này đạt được hiệu quả cao:

1. **Hiệu năng tối ưu**: Cập nhật RetailerId nhanh hơn nhiều so với xóa và backup dữ liệu, có thể xử lý hàng nghìn yêu cầu đồng thời
2. **Triển khai đơn giản**: Không cần thêm các bảng ánh xạ phức tạp, chỉ cần đảo dấu của RetailerId
3. **Khôi phục dễ dàng**: Việc khôi phục chỉ đơn giản là đảo dấu RetailerId một lần nữa
4. **Tính toàn vẹn dữ liệu**: Đảm bảo tất cả các mối quan hệ dữ liệu vẫn được giữ nguyên
5. **Khả năng mở rộng**: Kiến trúc microservice cho phép dễ dàng mở rộng khi lượng yêu cầu tăng

Cách tiếp cận này cũng xử lý tốt các yêu cầu nghiệp vụ phức tạp như:
- Xác thực đa lớp với OTP qua số điện thoại của chủ hợp đồng
- Quản lý lập lịch xóa dữ liệu tự động
- Xử lý cân bằng kiểm kho và công nợ
- Khôi phục dữ liệu trong thời hạn 7 ngày
- Thông báo cho người dùng

Đây là giải pháp tối ưu có thể triển khai trong vòng 2 tuần với 6 senior engineers, vừa đáp ứng đầy đủ các yêu cầu hiện tại, vừa mở đường cho các tính năng nâng cao trong tương lai.