namespace KvFnB.Core.Domain
{
    /// <summary>
    /// Represents an entity that can be marked as deleted
    /// </summary>
    public interface ISoftDeletable
    {
        /// <summary>
        /// Gets or sets a value indicating whether the entity is deleted
        /// </summary>
        bool? IsDeleted { get; set; }
    }

    /// <summary>
    /// Represents an entity that tracks when it was deleted
    /// </summary>
    public interface IDeletedAt
    {
        /// <summary>
        /// Gets or sets the date and time when the entity was deleted
        /// </summary>
        DateTime? DeletedAt { get; set; }
    }

    /// <summary>
    /// Represents an entity that tracks who deleted it
    /// </summary>
    public interface IDeletedBy
    {
        /// <summary>
        /// Gets or sets the ID of the user who deleted the entity
        /// </summary>
        long? DeletedBy { get; set; }
    }

    /// <summary>
    /// Represents an entity that supports soft deletion with tracking of when and who deleted it
    /// </summary>
    public interface ISoftDeletableEntity : ISoftDeletable, IDeletedAt, IDeletedBy
    {
        // This interface combines ISoftDeletable, IDeletedAt, and IDeletedBy
        // No additional members needed as it inherits all required members
    }
}