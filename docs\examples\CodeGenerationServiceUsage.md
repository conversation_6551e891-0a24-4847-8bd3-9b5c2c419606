# Code Generation Service Usage Examples

## Overview
The `AdvancedCodeGenerationService` is a domain service that provides functionality for generating unique, sequential codes for various entity types across different tenants. It ensures that generated codes are unique and handles concurrency issues automatically.

## Basic Usage

### Generating a Single Code

```csharp
public class OrderService
{
    private readonly ICodeGenerationService _codeGenerationService;
    
    public OrderService(ICodeGenerationService codeGenerationService)
    {
        _codeGenerationService = codeGenerationService;
    }
    
    public async Task<string> CreateOrderNumberAsync(int tenantId)
    {
        // Generate a unique order number with prefix "ORD"
        string orderNumber = await _codeGenerationService.GenerateCodeAsync(
            tenantId, 
            "Order",  // Type of entity
            "ORD",    // Prefix
            6);       // Padding length
            
        // Result example: "ORD000001"
        return orderNumber;
    }
}
```

### Generating Multiple Codes in Batch

```csharp
public class InventoryService
{
    private readonly ICodeGenerationService _codeGenerationService;
    
    public InventoryService(ICodeGenerationService codeGenerationService)
    {
        _codeGenerationService = codeGenerationService;
    }
    
    public async Task<IEnumerable<string>> GenerateSerialNumbersAsync(int tenantId, int count)
    {
        // Generate multiple serial numbers at once
        IEnumerable<string> serialNumbers = await _codeGenerationService.GenerateBatchCodesAsync(
            tenantId,
            "SerialNumber",  // Type of entity
            "SN",           // Prefix
            8,              // Padding length
            count);         // Number of codes to generate
            
        // Result examples: ["SN00000001", "SN00000002", "SN00000003", ...]
        return serialNumbers;
    }
}
```

### Generating Code and Creating Entity Atomically

```csharp
public class ProductService
{
    private readonly ICodeGenerationService _codeGenerationService;
    private readonly IRepository<Product> _productRepository;
    
    public ProductService(
        ICodeGenerationService codeGenerationService,
        IRepository<Product> productRepository)
    {
        _codeGenerationService = codeGenerationService;
        _productRepository = productRepository;
    }
    
    public async Task<Product> CreateProductAsync(ProductData data, int tenantId)
    {
        // This will generate a code and create a product in a single transaction
        Product product = await _codeGenerationService.GenerateCodeAndCreateEntityAsync(
            tenantId,
            "Product",      // Type of entity
            "PRD",          // Prefix
            10,             // Padding length
            (code) => 
            {
                // This function creates the entity with the generated code
                return new Product
                {
                    TenantId = tenantId,
                    ProductCode = code,
                    Name = data.Name,
                    Description = data.Description,
                    Price = data.Price
                };
            });
            
        // The product is already saved to the database
        return product;
    }
}
```

## Advanced Scenarios

### Custom Code Formatting

```csharp
public class InvoiceService
{
    private readonly ICodeGenerationService _codeGenerationService;
    
    public InvoiceService(ICodeGenerationService codeGenerationService)
    {
        _codeGenerationService = codeGenerationService;
    }
    
    public async Task<string> GenerateInvoiceNumberAsync(int tenantId, DateTime issueDate)
    {
        // Using a date as part of the prefix
        string yearMonth = issueDate.ToString("yyyyMM");
        string prefix = $"INV-{yearMonth}-";
        
        string invoiceNumber = await _codeGenerationService.GenerateCodeAsync(
            tenantId,
            "Invoice",
            prefix,
            4);
            
        // Result example: "INV-202310-0001"
        return invoiceNumber;
    }
}
```

### Updating an Entity's Code

```csharp
public class ProductService
{
    private readonly ICodeGenerationService _codeGenerationService;
    private readonly IRepository<Product, int> _productRepository;
    
    public ProductService(
        ICodeGenerationService codeGenerationService,
        IRepository<Product, int> productRepository)
    {
        _codeGenerationService = codeGenerationService;
        _productRepository = productRepository;
    }
    
    public async Task<Product> UpdateProductCodeAsync(int productId, string newCode, int tenantId)
    {
        // Retrieve the product
        var product = await _productRepository.GetAsync(productId);
        if (product == null)
        {
            throw new EntityNotFoundException<Product>(productId);
        }
        
        // Update the product code - all operations occur in a single transaction:
        // - Uniqueness validation
        // - Sequence update (if needed)
        // - Entity code update
        var updatedProduct = await _codeGenerationService.UpdateEntityCodeAsync<Product>(
            tenantId,
            "Product",      // Type of entity
            "PRD",          // Prefix
            newCode,        // New code to assign
            async (code) => {
                // This function updates the entity with the new code
                product.Code = code;
                return await _productRepository.UpdateAsync(product);
            },
            6);             // Padding length
            
        // The product code is updated and changes are already saved to the database
        return updatedProduct;
    }
    
    // When updating a product with a new code that has a higher numeric value than 
    // the current sequence, the service automatically updates the sequence to ensure
    // future codes don't conflict with the newly assigned code.
    public async Task<Product> MigrateProductCodeAsync(int productId, int tenantId)
    {
        var product = await _productRepository.GetAsync(productId);
        if (product == null)
        {
            throw new EntityNotFoundException<Product>(productId);
        }
        
        // This will update the product's code to a higher value
        // and synchronize the sequence in a single transaction
        string newHigherCode = "PRD010000"; // Jumping to a much higher value
        var updatedProduct = await _codeGenerationService.UpdateEntityCodeAsync<Product>(
            tenantId,
            "Product",
            "PRD",
            newHigherCode,
            async (code) => {
                // Update the product with the new code
                product.Code = code;
                return await _productRepository.UpdateAsync(product);
            },
            6);
            
        // Future products will now have codes starting from PRD010001
        return updatedProduct;
    }
}
```

### Error Handling

```csharp
public class DocumentService
{
    private readonly ICodeGenerationService _codeGenerationService;
    private readonly ILogger<DocumentService> _logger;
    
    public DocumentService(
        ICodeGenerationService codeGenerationService,
        ILogger<DocumentService> logger)
    {
        _codeGenerationService = codeGenerationService;
        _logger = logger;
    }
    
    public async Task<string> GenerateDocumentNumberAsync(int tenantId)
    {
        try
        {
            string docNumber = await _codeGenerationService.GenerateCodeAsync(
                tenantId,
                "Document",
                "DOC",
                5);
                
            return docNumber;
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Invalid arguments when generating document number");
            throw new BusinessException("Unable to generate document number due to invalid data", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating document number");
            throw new BusinessException("Unable to generate document number", ex);
        }
    }
}
```

## Best Practices

1. **Consistent Entity Types**: Use consistent entity type names across your application to ensure proper sequencing.
2. **Appropriate Prefix Length**: Choose prefixes that clearly identify the entity type while keeping them concise.
3. **Sufficient Padding**: Consider future growth when choosing padding length.
4. **Tenant Isolation**: Always provide the correct tenant ID to ensure codes are isolated between tenants.
5. **Transactional Operations**: Use `GenerateCodeAndCreateEntityAsync` and `UpdateEntityCodeAsync` for atomic operations to ensure consistency.
6. **Error Handling**: Implement proper error handling to manage concurrency conflicts and other exceptions.
7. **Code Updates**: When updating codes, follow prefix conventions and be aware that code updates will synchronize the sequence if needed.

## Dependency Injection

To use the `AdvancedCodeGenerationService`, register it in your startup configuration:

```csharp
public void ConfigureServices(IServiceCollection services)
{
    // Register the code generation service
    services.AddCodeGenerationService(Configuration);
    
    // Other service registrations...
}
```