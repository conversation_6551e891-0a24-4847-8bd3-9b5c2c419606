using System;
using System.ComponentModel.DataAnnotations.Schema;
using KvFnB.Core.Domain;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Models;
using System.Text.Json;

namespace KvFnB.Modules.DataManagement.Domain.Entities
{
    /// <summary>
    /// Represents a request to delete data in the system
    /// </summary>
    [Table("DeleteDataRequest")]
    public class DeleteDataRequest : AggregateRoot<long>, IAuditableEntity
    {
        #region Properties

        /// <summary>
        /// Email address for verification and notification
        /// </summary>
        [Column("Email")]
        public string? Email { get; set; }

        /// <summary>
        /// Current status of the request
        /// </summary>
        [Column("Status")]
        public int Status { get; set; }

        /// <summary>
        /// The type of schedule for this request
        /// </summary>
        [Column("ScheduleType")]
        public int ScheduleType { get; set; }

        /// <summary>
        /// The type of data to delete
        /// </summary>
        [Column("Type")]
        public int Type { get; set; }

        /// <summary>
        /// Branch IDs to include in the deletion (stored as JSON array)
        /// </summary>
        [Column("BranchIds")]
        public string BranchIds { get; set; }

        /// <summary>
        /// JSON configuration for scheduled deletion
        /// </summary>
        [Column("ScheduleConfig")]
        public string? ScheduleConfig { get; set; }
        
        /// <summary>
        /// JSON configuration for filter condition
        /// </summary>
        [Column("FilterCondition")]
        public string? FilterCondition { get; set; }

        /// <summary>
        /// The date when this request started executing
        /// </summary>
        [Column("ExecuteDate")]
        public DateTime? ExecuteDate { get; set; }

        /// <summary>
        /// The date when this request was completed
        /// </summary>
        [Column("CompletedDate")]
        public DateTime? CompletedDate { get; set; }

        #endregion

        #region IAuditableEntity Properties

        /// <summary>
        /// ID of the user who created this request
        /// </summary>
        [Column("CreatedBy")]
        public long CreatedBy { get; set; }

        /// <summary>
        /// Date and time when this request was created
        /// </summary>
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// ID of the user who last modified this request
        /// </summary>
        [Column("ModifiedBy")]
        public long? ModifiedBy { get; set; }

        /// <summary>
        /// Date and time when this request was last modified
        /// </summary>
        [Column("ModifiedAt")]
        public DateTime? ModifiedAt { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Collection of delete data details associated with this request
        /// </summary>
        public IReadOnlyCollection<DeleteDataDetail> DeleteDataDetails => _deleteDataDetails.AsReadOnly();
        readonly List<DeleteDataDetail> _deleteDataDetails = new();

        #endregion

        // constructor to enforce creation via factory method
        public DeleteDataRequest() { }

        #region Factory Methods

        /// <summary>
        /// Creates a new DeleteDataRequest with the specified parameters
        /// </summary>
        public static DeleteDataRequest Create(
            string? email,
            DeleteDataType? type,
            DeleteDataScheduleType? scheduleType,
            string branchIds,
            DeleteDataRequestStatus status,
            DateTime? executeDate,
            string? scheduleConfigJson,
            string? filterConditionJson = null)
        {
            var request = new DeleteDataRequest
            {
                Email = email,
                Type = type != null ? (int)type: 0,
                ScheduleType = scheduleType !=null ? (int)scheduleType : 0,
                BranchIds = branchIds,
                Status = (int)status,
                ExecuteDate = executeDate,
                ScheduleConfig = scheduleConfigJson,
                FilterCondition = filterConditionJson
            };
            return request;
        }

        #endregion

        #region State-Changing Methods

        /// <summary>
        /// Approves the request, changing its status to Approved
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when trying to approve a request that is not in Pending status</exception>
        public void Approve()
        {
            if (Status != (int)DeleteDataRequestStatus.Pending)
                throw new InvalidOperationException("Can only approve pending requests");
                
            Status = (int)DeleteDataRequestStatus.Approved;
        }

        /// <summary>
        /// Marks the request as executing, changing its status to Executing
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when trying to execute a request that is not in Approved status</exception>
        public void MarkAsExecuting()
        {
            if (Status != (int)DeleteDataRequestStatus.Approved)
                throw new InvalidOperationException("Can only execute approved requests");
                
            Status = (int)DeleteDataRequestStatus.Executing;
            ExecuteDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Completes the request, changing its status to Completed
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when trying to complete a request that is not in Executing status</exception>
        public void Complete()
        {
            if (Status != (int)DeleteDataRequestStatus.Executing)
                throw new InvalidOperationException("Can only complete executing requests");
                
            Status = (int)DeleteDataRequestStatus.Completed;
            CompletedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Cancels the request, changing its status to Cancelled
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when trying to cancel a request that is not in Pending or Approved status</exception>
        public void Cancel()
        {
            if (Status != (int)DeleteDataRequestStatus.Pending && Status != (int)DeleteDataRequestStatus.Approved)
                throw new InvalidOperationException("Can only cancel pending or approved requests");
                
            Status = (int)DeleteDataRequestStatus.Cancelled;
        }
        
        /// <summary>
        /// Adds a detail to this delete data request
        /// </summary>
        /// <param name="detail">The detail to add</param>
        public void AddDeleteDataDetail(DeleteDataDetail detail)
        {
            if (detail == null)
                throw new ArgumentNullException(nameof(detail));
                
            _deleteDataDetails.Add(detail);
        }

        /// <summary>
        /// Updates the next execution date for this request
        /// </summary>
        /// <param name="nextExecutionDate">The new next execution date</param>
        public void UpdateNextExecutionDate(DateTime nextExecutionDate)
        {
            ExecuteDate = nextExecutionDate;
        }
        public void UpdateFilterCondition(string filterCondition)
        {
            FilterCondition = filterCondition;
        }

        /// <summary>
        /// Calculates the next execution date for a scheduled request
        /// </summary>
        /// <param name="config">The schedule configuration</param>
        /// <param name="currentTime">The current date and time</param>
        /// <returns>The next execution date</returns>
        public DateTime CalculateNextExecutionDate(ScheduleConfig config,DateTime executeDate)
        {
            switch (config.ScheduleType)
            {
                case Enums.ScheduleType.Daily:
                    return executeDate.Date.Add(TimeSpan.Parse(config.ExecutionTime)).AddDays(1);

                case Enums.ScheduleType.Weekly:
                    if (!config.DayOfWeek.HasValue)
                    {
                        throw new InvalidOperationException("DayOfWeek must be specified for weekly schedule");
                    }
                    var nextDate = executeDate.Date.AddDays(7);
                    return executeDate.Date.AddDays(7);

                case Enums.ScheduleType.Monthly:
                    if (!config.DayOfMonth.HasValue)
                    {
                        throw new InvalidOperationException("DayOfMonth must be specified for monthly schedule");
                    }

                    var nextMonthDate = new DateTime(executeDate.Year, executeDate.Month, 1).AddMonths(1);
                    int targetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(nextMonthDate.Year, nextMonthDate.Month));
                    return new DateTime(nextMonthDate.Year, nextMonthDate.Month, targetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                
                case Enums.ScheduleType.Yearly:
                    if (!config.DayOfMonth.HasValue || !config.Month.HasValue)
                    {
                        throw new InvalidOperationException("DayOfMonth and Month must be specified for yearly schedule");
                    }

                    int yearToUse = executeDate.Year;
                    if (executeDate.Month > config.Month.Value ||
                        (executeDate.Month == config.Month.Value && executeDate.Day > config.DayOfMonth.Value))
                    {
                        yearToUse++;
                    }
                    int yearlyTargetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(yearToUse, config.Month.Value));
                    return new DateTime(yearToUse, config.Month.Value, yearlyTargetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));

                
                case Enums.ScheduleType.Quarterly:
                    if (!config.DayOfMonth.HasValue || !config.QuarterMonth.HasValue)
                    {
                        throw new InvalidOperationException("DayOfMonth and QuarterMonth must be specified for quarterly schedule");
                    }

                    var nextQuarterDate = executeDate.Date;
                    int currentQuarter = (executeDate.Month - 1) / 3 + 1;
                    int monthsToAdd = (currentQuarter * 3) - executeDate.Month + config.QuarterMonth.Value;
                    if (monthsToAdd <= 0 || (monthsToAdd == 0 && executeDate.Day > config.DayOfMonth.Value))
                        monthsToAdd += 3;
                    nextQuarterDate = nextQuarterDate.AddMonths(monthsToAdd);
                    int quarterTargetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(nextQuarterDate.Year, nextQuarterDate.Month));
                    return new DateTime(nextQuarterDate.Year, nextQuarterDate.Month, quarterTargetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                
                case Enums.ScheduleType.EveryThreeMonths:
                    if (!config.DayOfMonth.HasValue)
                    {
                        throw new InvalidOperationException("DayOfMonth must be specified for monthly schedule");
                    }

                    var datOfExecution = executeDate.Date;
                    datOfExecution = datOfExecution.AddMonths(3);
                    int scheduleTargetDay = Math.Min(config.DayOfMonth.Value, DateTime.DaysInMonth(datOfExecution.Year, datOfExecution.Month));
                    return new DateTime(datOfExecution.Year, datOfExecution.Month, scheduleTargetDay)
                        .Add(TimeSpan.Parse(config.ExecutionTime));
                default:
                    throw new ArgumentException($"Unsupported schedule type: {config.ScheduleType}");
            }
        }
        #endregion

        public void UpdateStatus(
            DeleteDataRequestStatus status)
        {
            Status = (int)status;
        }
        
        public void UpdateInfo(
            string? email,
            DeleteDataType type,
            DeleteDataScheduleType scheduleType,
            string branchIds,
            DateTime? executeDate,
            string scheduleConfigJson,
            string filterConditionJson)
        {
            Email = email;
            Type = (int)type;
            ScheduleType = (int)scheduleType;
            BranchIds = branchIds;
            ExecuteDate = executeDate;
            ScheduleConfig = scheduleConfigJson;
            FilterCondition = filterConditionJson;
        }
    }
}
