namespace KvFnB.Core.Contracts
{
    public record PagingRequest
    {
        public int PageNumber { get; init; } = 1; // Default to first page
        public int PageSize { get; init; } = 15;  // Default to 10 items per page

        public PagingRequest() { }

        public PagingRequest(int pageNumber, int pageSize)
        {
            PageNumber = pageNumber < 1 ? 1 : pageNumber;
            PageSize = pageSize < 1 ? 10 : pageSize;
        }
    }
}