﻿using System.Dynamic;
using KvFnB.Core.Domain;
using KvFnB.Core.Exceptions;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.ValueObjects;

namespace KvFnB.Modules.Menu.Domain.Models
{
    public class Product : AggregateRoot<long>, IDeletedAt, ICreatedAt, IModifiedAt, ICode
    {
        public string Code { get; set; }
        public string Name { get; private set; }
        public int CategoryId { get; private set; }
        public string? Description { get; private set; }
        public bool AllowSale { get; private set; }
        public decimal BasePrice { get; private set; }
        public float Tax { get; private set; }
        public bool IsActive { get; private set; }
        public byte? ProductType { get; private set; }
        public bool HasVariants { get; private set; }
        public long? MasterProductId { get; private set; }
        public string? Unit { get; private set; }
        public float ConversionValue { get; private set; }
        public long? MasterUnitId { get; private set; }
        public string? OrderTemplate { get; private set; }
        public string? FullName { get; private set; }
        public bool? IsTimeServices { get; private set; }
        public bool? IsRewardPoint { get; private set; }
        public float? Weight { get; private set; }
        public string? ShortDescription { get; private set; }
        public bool? IsTimeType { get; private set; }
        public bool? IsTopping { get; private set; }
        public bool? IsProcessedGoods { get; private set; }
        public byte? ProductGroup { get; private set; }
        public string MasterCode { get; private set; }
        public byte[] Revision { get; private set; }
        public bool? InventoryTrackingIgnore { get; private set; }
        public int? Rank { get; private set; }
        public bool? IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ModifiedAt { get; set; }
        public List<ProductImage> ProductImages { get; set; } = [];
        public List<ProductShelf> ProductShelves { get; set; } = [];
        public List<ProductFormula> ProductFormulas { get; set; } = [];
        public List<ProductBranch> ProductBranches { get; set; } = [];
        private readonly List<ProductTopping> _productToppings = [];
        public IReadOnlyCollection<ProductTopping> ProductToppings => _productToppings.AsReadOnly();
        public List<CustomizableComboGroup> ComboGroups { get; set; } = [];
        public List<ProductSaleBranch> ProductSaleBranches { get; set; } = [];

        public ProductTax? ProductTax 
        {
            get => ProductTaxes.FirstOrDefault(x => x.Status == 0);
        }

        public List<ProductTax> ProductTaxes { get; set; } = [];

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        private Product()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        {}

        public static Product CreateProduct(
            string code,
            string name,
            int categoryId,
            byte productType,
            string? unit,
            float conversionValue,
            string masterCode)
        {
            return new Product
            {
                Code = code,
                Name = name,
                FullName = name, // since its supported combo product, so full name is the same as name
                CategoryId = categoryId,
                IsActive = true,
                HasVariants = false,
                ProductType = productType,
                Unit = unit,
                ConversionValue = conversionValue,
                MasterCode = masterCode ?? code,
                IsDeleted = false,
                AllowSale = true,   
            };
        }

        public void Update(
            string code,
            string name,
            int categoryId,
            string? description,
            decimal basePrice,
            string? unit,
            float conversionValue)
        {
            Code = code;
            Name = name;
            FullName = name; // since its supported combo product, so full name is the same as name
            CategoryId = categoryId;
            Description = description;
            BasePrice = basePrice;
            Unit = unit;
            ConversionValue = conversionValue;
        }

        public void SetDescription(string? description)
        {
            Description = description;
        }

        public void SetBasePrice(decimal basePrice)
        {
            BasePrice = basePrice;
        }
        
        public void SetAllowSale(bool allowSale)
        {
            AllowSale = allowSale;
        }

        public void SetProductTax(ProductTax productTax)
        {
            // if have any product tax, set Status = 1 and add new producttax with status = 0
            if (ProductTaxes.Count != 0)
            {
                ProductTaxes.ForEach(x => x.Status = 1);
            }
            // if exist product tax (productTax.Tax id = productTaxes.TaxId), replace that product tax
            if (ProductTaxes.Any(x => x.TaxId == productTax.TaxId))
            {
                // get product tax exists:
                var productTaxExists = ProductTaxes.First(x => x.TaxId == productTax.TaxId);
                productTaxExists.Status = 0;
                productTaxExists.TaxRate = productTax.TaxRate;
                productTaxExists.Name = productTax.Name;
            }
            else
            {
                ProductTaxes.Add(productTax);
            }
        }

        public void AddInventoryOnBranch(ProductBranch branch)
        {
            if (ProductBranches.Any(b => b.BranchId == branch.BranchId))
                throw new InvalidOperationException($"Branch {branch.BranchId} is already configured for this product.");

            ProductBranches.Add(branch);
        }

        public void UpdateInventoryOnBranch(ProductBranch updatedBranch)
        {
            var existingBranch = ProductBranches.FirstOrDefault(b => b.BranchId == updatedBranch.BranchId) ?? throw new InvalidOperationException($"Branch {updatedBranch.BranchId} is not configured for this product.");
            existingBranch.UpdateInventory(updatedBranch.OnHand, updatedBranch.MinQuantity, updatedBranch.MaxQuantity);
        }

        public void AddTopping(long toppingProductId, int? retailerId)
        {
            if (_productToppings.Any(t => t.ToppingProductId == toppingProductId))
                throw new InvalidOperationException($"Topping product {toppingProductId} is already added to this product.");

            _productToppings.Add(ProductTopping.Create(Id, toppingProductId, retailerId, DateTime.Now));
        }

        public void RemoveTopping(long toppingProductId)
        {
            var topping = _productToppings.FirstOrDefault(t => t.ToppingProductId == toppingProductId);
            if (topping != null)
            {
                _productToppings.Remove(topping);
            }
        }

        public void UpdateToppings(List<long> toppingProductIds, int? retailerId)
        {
            _productToppings.Clear();
            foreach (var toppingId in toppingProductIds)
            {
                _productToppings.Add(ProductTopping.Create(Id, toppingId, retailerId, DateTime.Now));
            }
        }
        
        // Helper method to get all topping product IDs
        public List<long> GetToppingProductIds()
        {
            return _productToppings.Select(t => t.ToppingProductId).ToList();
        }
        
        public void Activate() => IsActive = true;
        public void Deactivate() => IsActive = false;
        public void Delete() => IsDeleted = true;

        public void SetAsMasterVariant(long masterProductId)
        {
            if (masterProductId == Id)
                throw new InvalidOperationException("A product cannot be a variant of itself.");

            MasterProductId = masterProductId;
            HasVariants = false;
        }

        public void SetAsHasVariants()
        {
            HasVariants = true;
            MasterProductId = null;
        }

        public void UpdateProductType(byte? productType) => ProductType = productType;
        public void UpdateProductGroup(byte? productGroup) => ProductGroup = productGroup;
        public void UpdateInventorySettings(bool? inventoryTrackingIgnore) => InventoryTrackingIgnore = inventoryTrackingIgnore;
        public void UpdateRank(int? rank) => Rank = rank;

        public void UpdateBasePrice(decimal newPrice)
        {
            BasePrice = newPrice;
        }

        public void AddImage(string imageUrl, int? tenantId)
        {
            var image = ProductImage.Create(imageUrl, tenantId);
            ProductImages.Add(image);
        }

        public void ClearImages()
        {
            ProductImages.Clear();
        }

        #region Customizable Combo Methods

        public void AddComboGroup(CustomizableComboGroup group)
        {
            ComboGroups.Add(group);
        }

        /// <summary>
        /// Gets a combo group by ID
        /// </summary>
        public CustomizableComboGroup? GetComboGroup(long groupId)
        {
            return ComboGroups.FirstOrDefault(g => g.Id == groupId);
        }

        /// <summary>
        /// Removes a combo group from this product
        /// </summary>
        public void RemoveComboGroup(long groupId)
        {
            var group = ComboGroups.FirstOrDefault(g => g.Id == groupId);
            if (group != null)
            {
                ComboGroups.Remove(group);
            }
        }

        /// <summary>
        /// Adds a new item to a combo group
        /// </summary>
        public CustomizableComboGroupItem AddComboGroupItem(
            long groupId,
            long productId,
            decimal additionalPrice = 0,
            int sortOrder = 0)
        {
            var group = GetComboGroup(groupId) ?? throw new DomainException($"Combo group with ID {groupId} not found");
            var item = CustomizableComboGroupItem.Create(
                groupId,
                productId,
                additionalPrice,
                sortOrder);
                
            group.AddItem(item);
            return item;
        }

        /// <summary>
        /// Removes an item from a combo group
        /// </summary>
        public void RemoveComboGroupItem(long groupId, long itemId)
        {
            var group = GetComboGroup(groupId) ?? throw new DomainException($"Combo group with ID {groupId} not found");
            group.RemoveItem(itemId);
        }

        /// <summary>
        /// Sets this product as a customizable combo
        /// </summary>
        public void SetAsCustomizableCombo()
        {
            ProductType = (byte)ProductTypes.CustomizableCombo.Id;
        }

        public void UpdateWeight(float weight)
        {
            Weight = weight;
        }

        public void UpdateAllowSale(bool allowSale)
        {
            AllowSale = allowSale;
        }

        public void UpdateCode(string code)
        {
            Code = code;
        }

        #endregion

        public void SetFullName(string fullName)
        {
            FullName = fullName;
        }

        public void SetOrderTemplate(string orderTemplate)
        {
            OrderTemplate = orderTemplate;
        }

        public void UpdateIsRewardPoint(bool isRewardPoint)
        {
            IsRewardPoint = isRewardPoint;
        }

        public void AddShelf(long shelvesId)
        {
            var shelf = ProductShelf.Create(shelvesId);
            ProductShelves.Add(shelf);
        }

        public void AddProductSaleBranch(ProductSaleBranch productSaleBranch)
        {
            if (ProductSaleBranches.Any(x => x.BranchId == productSaleBranch.BranchId))
            {
                var existingSaleBranch = ProductSaleBranches.First(x => x.BranchId == productSaleBranch.BranchId);
                existingSaleBranch.SetIsActive(productSaleBranch.IsActive);
            }
            else
            {
                ProductSaleBranches.Add(productSaleBranch);
            }
        }

        public void AddFormula(long materialId, float quantity, int tenantId)
        {
            var formula = ProductFormula.Create(materialId, quantity, tenantId);
            ProductFormulas.Add(formula);
        }

        public void UpdateOrderTemplate(string value)
        {
            OrderTemplate = value;
        }
    }
}