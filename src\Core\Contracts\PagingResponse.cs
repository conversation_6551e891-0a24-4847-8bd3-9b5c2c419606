namespace KvFnB.Core.Contracts
{
    public record PagingResponse<T>
    {
        public int PageNumber { get; init; } = 1;
        public int PageSize { get; init; } = 15;
        public int TotalItems { get; init; }
        public int TotalPages => (int)Math.Ceiling((double)TotalItems / PageSize);
        public IEnumerable<T> Items { get; init; }

        public PagingResponse(IEnumerable<T> items, int pageNumber, int pageSize, int totalItems)
        {
            Items = items;
            PageNumber = pageNumber;
            PageSize = pageSize;
            TotalItems = totalItems;
        }

        public PagingResponse(IEnumerable<T> items)
        {
            Items = items;
        }
    }
}