using System;
using System.Collections.Generic;

namespace KvFnB.Core.Validation.Extensions
{
    /// <summary>
    /// Extension method for custom validation that allows access to sub-validator errors
    /// </summary>
    public static class CustomExtension
    {
        /// <summary>
        /// Applies a custom validation rule that can add multiple detailed error messages
        /// </summary>
        /// <typeparam name="T">The type being validated</typeparam>
        /// <typeparam name="TProperty">The property type being validated</typeparam>
        /// <param name="ruleBuilder">The rule builder</param>
        /// <param name="customValidation">A function that performs validation and returns a tuple with validation status and errors</param>
        /// <param name="defaultErrorMessage">Default error message if validation fails</param>
        /// <returns>The rule builder for method chaining</returns>
        public static RuleBuilder<T, TProperty> Custom<T, TProperty>(
            this RuleBuilder<T, TProperty> ruleBuilder, 
            Func<TProperty, (bool IsValid, IEnumerable<string> Errors)> customValidation,
            string defaultErrorMessage = "Validation failed")
        {
            ruleBuilder.Validator.AddRule(
                ruleBuilder.GroupId,
                x => 
                {
                    var value = ruleBuilder.Expression.Compile()(x);
                    var (isValid, errors) = customValidation(value);
                    
                    if (!isValid)
                    {
                        // Add detailed error messages if available
                        foreach (var error in errors)
                        {
                            ruleBuilder.Validator.AddError(error);
                        }
                    }
                    
                    return isValid;
                },
                defaultErrorMessage
            );
            
            return ruleBuilder;
        }
        
        /// <summary>
        /// Applies a custom validation rule for the root object that can add multiple detailed error messages
        /// </summary>
        /// <typeparam name="T">The type being validated</typeparam>
        /// <param name="ruleBuilder">The rule builder</param>
        /// <param name="customValidation">A function that performs validation and returns a tuple with validation status and errors</param>
        /// <param name="defaultErrorMessage">Default error message if validation fails</param>
        /// <returns>The rule builder for method chaining</returns>
        public static RuleBuilder<T, T> Custom<T>(
            this RuleBuilder<T, T> ruleBuilder, 
            Func<T, (bool IsValid, IEnumerable<string> Errors)> customValidation,
            string defaultErrorMessage = "Validation failed")
        {
            ruleBuilder.Validator.AddRule(
                ruleBuilder.GroupId,
                x => 
                {
                    var (isValid, errors) = customValidation(x);
                    
                    if (!isValid)
                    {
                        // Add detailed error messages if available
                        foreach (var error in errors)
                        {
                            ruleBuilder.Validator.AddError(error);
                        }
                    }
                    
                    return isValid;
                },
                defaultErrorMessage
            );
            
            return ruleBuilder;
        }
    }
} 