using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Shared.Localization;
using KvFnB.Shared.MultiTenancy;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity
{
    /// <summary>
    /// Implements the UpdateProductOnHandQuantity use case
    /// </summary>
    public class UpdateProductOnHandQuantityUseCase
    {
        private readonly ILocalizationProvider _multiLang;
        private readonly IValidator<UpdateProductOnHandQuantityRequest> _validator;
        private readonly IProductRepository _productRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITenantProvider _tenantProvider;
        private readonly TenantConfiguration _tenantConfiguration;
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductOnHandQuantityUseCase"/> class.
        /// </summary>
        public UpdateProductOnHandQuantityUseCase(
            IValidator<UpdateProductOnHandQuantityRequest> validator,
            IProductRepository productRepository,
            IUnitOfWork unitOfWork,
            ITenantProvider tenantProvider,
            TenantConfiguration tenantConfiguration,
            ILocalizationProvider multiLang)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _tenantConfiguration = tenantConfiguration ?? throw new ArgumentNullException(nameof(tenantConfiguration));
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
        }

        /// <summary>
        /// Executes the update product on-hand quantity use case.
        /// </summary>
        /// <param name="request">The request containing the product on-hand quantity to update.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the updated product on-hand quantity or error information.</returns>
        public async Task<Result<UpdateProductOnHandQuantityResponse>> ExecuteAsync(
            UpdateProductOnHandQuantityRequest request,
            CancellationToken cancellationToken = default)
        {
            // 1. Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(validationResult.Errors);
            }

            // get branch id from request
            var branchId = _tenantProvider.GetBranchId();
            if (branchId == null)
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_branch_not_found));
            }

            // 2. Get existing product
            var product = await _productRepository.GetAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_product_not_found));
            }

            // product is delete state cannot change 
            if (product.IsDeleted.GetValueOrDefault())
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_delete_product_deleted));
            }

            var isChangeInventoryTrackingIgnore = product.InventoryTrackingIgnore != request.InventoryTrackingIgnore;
            var isChangeOnHandQuantity = product.ProductBranches.Any(b => b.BranchId == branchId && b.OnHand != request.OnHandQuantity);

            
            // product type = 2 cannot change inventory tracking ignore and on hand quantity
            if ((product.ProductType == (byte)ProductTypes.Service.Id || product.ProductType == (byte)ProductTypes.CustomizableCombo.Id) && (isChangeInventoryTrackingIgnore || isChangeOnHandQuantity))
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_service_combo_no_inventory_update));
            }

            // 3. Check if inventory tracking is enabled
            if (product.InventoryTrackingIgnore == true && isChangeOnHandQuantity)
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_no_tracking_no_inventory_update));
            }

            // find product branch by branch id
            var productBranch = product.ProductBranches.FirstOrDefault(b => b.BranchId == branchId);
            if (productBranch == null)
            {
                // Create new ProductBranch for this branch if it doesn't exist
                var tenantId = _tenantProvider.GetTenantId() ?? throw new InvalidOperationException("Tenant ID not found.");
                
                // Create new ProductBranch
                var newProductBranch = ProductBranch.Create(
                    tenantId: tenantId,
                    branchId: (int) branchId.Value,
                    cost: request.Cost,
                    isFavourite: request.IsFavourite);
                
                // Set initial inventory values based on request
                newProductBranch.UpdateInventory(
                    request.OnHandQuantity,
                    request.MinQuantity,
                    request.MaxQuantity);
                
                // Add the new branch to the product
                product.AddInventoryOnBranch(newProductBranch);
                
                // Use the new branch for the rest of the method
                productBranch = newProductBranch;
            }

            var isChangeCost = productBranch.Cost != request.Cost;

            if(_tenantConfiguration.UseAvgCost && isChangeCost)
            {
                return Result<UpdateProductOnHandQuantityResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_avg_cost_no_update));
            }

            product.UpdateInventorySettings(request.InventoryTrackingIgnore);

            // update product branch on hand quantity
            productBranch.UpdateInventory(
                request.OnHandQuantity,
                request.MinQuantity,
                request.MaxQuantity
            );

            productBranch.UpdateCost(request.Cost);

            productBranch.UpdateIsFavourite(request.IsFavourite);
            
            // update product 
            product.UpdateInventoryOnBranch(productBranch);
        
            product = await _productRepository.UpdateAsync(product, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            // 5. Create response
            var response = new UpdateProductOnHandQuantityResponse
            {
                Id = product.Id,
                OnHandQuantity = productBranch.OnHand,
                MinQuantity = productBranch.MinQuantity,
                MaxQuantity = productBranch.MaxQuantity,
                Cost = productBranch.Cost
            };

            return Result<UpdateProductOnHandQuantityResponse>.Success(response);
        }
    }
}
