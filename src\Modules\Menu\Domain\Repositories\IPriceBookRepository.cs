using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Menu.Domain.Models;

namespace KvFnB.Modules.Menu.Domain.Repositories
{
    public interface IPriceBookRepository : IRepository<PriceBook, long>
    {
        Task<bool> IsUniquePriceBookNameAsync(string name, long? excludePriceBookId = null, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets multiple price books by their IDs
        /// </summary>
        /// <param name="ids">Collection of price book IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of price books matching the provided IDs</returns>
        Task<List<PriceBook>> GetByIdsAsync(IEnumerable<long> ids, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Updates multiple price books at once
        /// </summary>
        /// <param name="priceBooks">Collection of price books to update</param>
        /// <returns>Task representing the asynchronous operation</returns>
        void UpdateRangeAsync(IEnumerable<PriceBook> priceBooks);

        /// <summary>
        /// Gets all price books that have prices for a specific product
        /// </summary>
        /// <param name="productId">Product ID to find price books for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of price books that have prices for the specified product</returns>
        Task<List<PriceBook>> GetForProductAsync(long productId, CancellationToken cancellationToken = default);
    }       
} 
