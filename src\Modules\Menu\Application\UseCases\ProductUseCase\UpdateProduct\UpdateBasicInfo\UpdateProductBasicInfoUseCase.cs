using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Application.Dtos.Response;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo
{

    /// <summary>
    /// Implements the UpdateProduct use case
    /// </summary>
    public class UpdateProductBasicInfoUseCase
    {
        private readonly ILocalizationProvider _multiLang; 
        private readonly IValidator<UpdateProductBasicInfoRequest> _validator;
        private readonly IProductRepository _productRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly ITaxRepository _taxRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITenantProvider _tenantProvider;
        private readonly IMapper _mapper;
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductUseCase"/> class.
        /// </summary>
        /// <param name="validator">The validator for the request.</param>
        /// <param name="productRepository">The product repository.</param>
        /// <param name="categoryRepository">The category repository.</param>
        /// <param name="unitOfWork">The unit of work.</param>
        /// <param name="mapper">The mapper.</param>
        #pragma warning disable S107 // Methods should not have too many parameters
        public UpdateProductBasicInfoUseCase(
            ILocalizationProvider multiLang,
            IValidator<UpdateProductBasicInfoRequest> validator,
            IProductRepository productRepository,
            ICategoryRepository categoryRepository,
            ITaxRepository taxRepository,
            IUnitOfWork unitOfWork,
            ITenantProvider tenantProvider,
            IMapper mapper)
        {
            _multiLang = multiLang ?? throw new ArgumentNullException(nameof(multiLang));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _categoryRepository = categoryRepository ?? throw new ArgumentNullException(nameof(categoryRepository));
            _taxRepository = taxRepository ?? throw new ArgumentNullException(nameof(taxRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }
        #pragma warning restore S107 // Methods should not have too many parameters

        /// <summary>
        /// Executes the update product use case.
        /// </summary>
        /// <param name="request">The request containing the product details to update.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the updated product or error information.</returns>
        public async Task<Result<UpdateProductBasicInfoResponse>> ExecuteAsync(
            UpdateProductBasicInfoRequest request,
            CancellationToken cancellationToken = default)
        {
            // 1. Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<UpdateProductBasicInfoResponse>.Failure(validationResult.Errors);
            }

            // 2. Get existing product
            var product = await _productRepository.GetAsync(request.ProductId, cancellationToken);
            if (product == null)

            {
                return Result<UpdateProductBasicInfoResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_required));
            }

            // 3. Check if product code is unique (excluding current product)
            if (product.Code != request.Code)
            {
                var isUniqueProductCode = await _productRepository.IsUniqueProductCodeAsync(request.Code, request.ProductId, cancellationToken);
                if (!isUniqueProductCode)
                {
                    return Result<UpdateProductBasicInfoResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_code_exists));
                }
            }

            // 4. Check if category exists
            var category = await _categoryRepository.GetAsync(request.CategoryId, cancellationToken);
            if (category == null)
            {
                return Result<UpdateProductBasicInfoResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_category_not_found));
            }

            // 5. Update product basic information
            product.Update(
                request.Code,
                request.Name,
                request.CategoryId,
                request.Description,
                request.BasePrice,
                request.Unit,
                request.ConversionValue ?? product.ConversionValue
            );

            // Update weight using extension method
            product.UpdateWeight(request.Weight);

            // Update allow sale flag if provided
            if (request.AllowSale.HasValue)
            {
                product.UpdateAllowSale(request.AllowSale.Value);
            }

            // Update product tax if provided
            if (request.TaxId.HasValue)
            {
                var tax = await _taxRepository.GetAsync(request.TaxId.Value, cancellationToken) ?? throw new InvalidOperationException(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_tax_not_found));
                product.SetProductTax(ProductTax.Create(tax.Id, tax.Name, tax.TaxRate, _tenantProvider.GetTenantId() ?? 0));
            }

            // Update product group if provided
            if (request.ProductGroupId.HasValue)
            {
                product.UpdateProductGroup(request.ProductGroupId.Value);
            }

            // Update order template if provided
            if (request.OrderTemplate != null)
            {
                product.UpdateOrderTemplate(request.OrderTemplate);
            }

            // Update is reward point if provided
            if (request.IsRewardPoint.HasValue)
            {
                product.UpdateIsRewardPoint(request.IsRewardPoint.Value);
            }

            // 7. Save changes
            product = await _productRepository.UpdateAsync(product, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            // 8. Create response
            var response = _mapper.Map<UpdateProductBasicInfoResponse>(product);

            await MappingTax(product, response, cancellationToken);
            return Result<UpdateProductBasicInfoResponse>.Success(response);
        }

        private async Task MappingTax(Product product, UpdateProductBasicInfoResponse response, CancellationToken cancellationToken)
        {
            if (product.ProductTaxes.Count != 0)
            {
                var tax = await _taxRepository.GetAsync(product.ProductTaxes.First(x => x.Status == 0).TaxId, cancellationToken) ?? throw new InvalidOperationException("Tax not found.");
                response.Tax = new TaxResponse
                {
                    Id = product.ProductTaxes.First().TaxId,
                    Name = tax.Name,
                    TaxRate = tax.TaxRate,
                    TaxType = tax.TaxType
                };
            }
        }
    }

    public class UpdateField<T>
    {
        public bool ShouldUpdate { get; set; }
        public T? Value { get; set; }
    }
} 