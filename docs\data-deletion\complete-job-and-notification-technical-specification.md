# Technical Specification: Complete Job and Notification Service

## 1. Tổng Quan

Complete Job and Notification Service là một thành phần quan trọng trong hệ thống xóa dữ liệu gian hà<PERSON>, có trách nhiệm theo dõ<PERSON>, cập nhật trạng thái và gửi thông báo cho người dùng về kết quả xử lý các yêu cầu xóa dữ liệu. Service này hoạt động như một background job ch<PERSON><PERSON> đ<PERSON>, đả<PERSON> b<PERSON><PERSON> các quy trình xóa dữ liệu được hoàn tất đúng cách và người dùng được thông báo kịp thời.

### 1.1. <PERSON><PERSON><PERSON> Tiêu <PERSON>h

- <PERSON> dõi và cập nhật trạng thái của các `ProcessingDataJob` khi hoàn tất xử lý
- <PERSON><PERSON><PERSON> bản ghi lịch sử xóa dữ liệu trong cơ sở dữ liệu
- <PERSON><PERSON><PERSON> thông báo email đến người dùng khi quá trình xóa dữ liệu hoàn tất thành công
- Đảm bảo tính nhất quán và toàn vẹn dữ liệu trong quá trình xóa

## 2. Thiết Kế Dịch Vụ

### 2.1. Sơ Đồ Tổng Thể

```
+------------------------+      +------------------------+      +------------------------+
|                        |      |                        |      |                        |
| Complete Job Service   +----->+   Database (Update)    +----->+  Notification Service  |
|                        |      |                        |      |                        |
+------------------------+      +------------------------+      +------------------------+
```

### 2.2. Cấu Trúc Background Job

Background job được thiết kế để chạy định kỳ 5 phút một lần, cấu hình được định nghĩa trong `CompleteDataProcessConfiguration`:

```csharp
public class CompleteDataProcessConfiguration
{
    public CompleteJobConfiguration CompleteJob { get; set; } = new();
    public EmailNotificationConfiguration EmailNotification { get; set; } = new();
}

public class CompleteJobConfiguration
{
    public int IntervalMinutes { get; set; } = 5;
    public ExecutionTimeWindow ExecutionTime { get; set; } = new();
    public int CancellationTimeMinutes { get; set; } = 30;
    public int BatchSize { get; set; } = 100;
}

public class ExecutionTimeWindow
{
    public int FromHour { get; set; } = 23;
    public int ToHour { get; set; } = 5;
}

public class EmailNotificationConfiguration
{
    public bool Enabled { get; set; } = true;
    public string TemplateId { get; set; } = "delete-data-complete";
    public string FromEmail { get; set; } = "<EMAIL>";
    public string FromName { get; set; } = "System Admin";
}
```

Và cấu hình trong file `appSettings.json`:

```json
{
  "CompleteDataProcessSettings": {
    "CompleteJob": {
      "IntervalMinutes": 5,
      "ExecutionTime": {
        "FromHour": 23,
        "ToHour": 5
      },
      "CancellationTimeMinutes": 30,
      "BatchSize": 100
    },
    "EmailNotification": {
      "Enabled": true,
      "TemplateId": "delete-data-complete",
      "FromEmail": "<EMAIL>",
      "FromName": "System Admin"
    }
  }
}
```

Với cấu hình này, background job sẽ chỉ chạy trong khoảng thời gian từ 23:00 đến 5:00 sáng hôm sau để tránh thời gian cao điểm sử dụng hệ thống.

## 3. Thiết Kế UseCase

Để triển khai chức năng này, chúng ta sẽ sử dụng mô hình UseCase tuân theo chuẩn hexagonal architecture và Domain-Driven Design trong KvFnB Core project.

### 3.1. Cấu Trúc Thư Mục

```
src/Modules/DataManagement/
├── Application/
│   ├── Abstractions/ 
│   │   └── IDataManagementQueryService.cs
│   ├── Dtos/
│   │   └── CompleteProcessingJob/
│   │       ├── CompleteProcessingDataJobDto.cs
│   │       ├── CompleteDeleteDataDetailDto.cs
│   │       └── CompleteDeleteDataRequestDto.cs
│   └── UseCases/
│       └── DataProcessingUseCase/
│           └── CompleteProcessingJob/
│               ├── CompleteProcessingJobRequest.cs
│               ├── CompleteProcessingJobResponse.cs
│               ├── CompleteProcessingJobValidator.cs
│               └── CompleteProcessingJobUseCase.cs
├── Domain/
│   ├── Configurations/
│   │   └── CompleteDataProcessConfiguration.cs
│   ├── Enums/
│   │   └── DataDeletionJobStatus.cs
│   └── Abstractions/
│       └── IEmailService.cs
├── Infrastructure/
│   ├── DependencyInjection/
│   │   └── ModuleRegistrar.cs
│   └── Services/
│       └── EmailService.cs
└── BackgroundServices/
    └── CompleteJobHostedService.cs
```

### 3.2. Các Thành Phần Chính

#### 3.2.1. Request Model

```csharp
namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob
{
    public class CompleteProcessingJobRequest
    {
        /// <summary>
        /// Maximum number of jobs to process in a single batch
        /// </summary>
        [Range(1, 1000)]
        public int BatchSize { get; set; } = 100;
    }
}
```

#### 3.2.2. Response Model

```csharp
namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob
{
    public class CompleteProcessingJobResponse
    {
        /// <summary>
        /// Total number of jobs updated during processing
        /// </summary>
        public int UpdatedJobsCount { get; set; }            
    }
}
```

#### 3.2.3. Data Transfer Objects

```csharp
namespace KvFnB.Modules.DataManagement.Application.Dtos.CompleteProcessingJob
{
    public class CompleteProcessingDataJobDto
    {
        public long Id { get; set; }
        public long RequestId { get; set; }
        public long BranchId { get; set; }
        public int RetailerId { get; set; }
        public byte Status { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class CompleteDeleteDataDetailDto
    {
        public long Id { get; set; }
        public long RequestId { get; set; }
        public long BranchId { get; set; }
        public int RetailerId { get; set; }
        public byte Status { get; set; }
        public string FilterConditions { get; set; }
    }

    public class CompleteDeleteDataRequestDto
    {
        public long Id { get; set; }
        public string Email { get; set; }
    }
}
```

#### 3.2.4. UseCase Implementation

```csharp
using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Enums;
using Microsoft.Extensions.Options;
using KvFnB.Modules.DataManagement.Domain.Configurations;
using Microsoft.Extensions.Logging;
using System.Text;
using KvFnB.Modules.DataManagement.Application.Dtos.CompleteProcessingJob;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.CompleteProcessingJob
{
    public class CompleteProcessingJobUseCase
    {
        private readonly IValidator<CompleteProcessingJobRequest> _validator;
        private readonly IDataManagementQueryService _queryService;
        private readonly IEmailService _emailService;
        private readonly IOptions<CompleteDataProcessConfiguration> _jobSettings;
        private readonly ILogger<CompleteProcessingJobUseCase> _logger;

        private const string ShopUserName = @"Chủ cửa hàng";        
        public CompleteProcessingJobUseCase(
            IValidator<CompleteProcessingJobRequest> validator,
            IDataManagementQueryService queryService,
            IEmailService emailService,
            IOptions<CompleteDataProcessConfiguration> jobSettings,
            ILogger<CompleteProcessingJobUseCase> logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _jobSettings = jobSettings ?? throw new ArgumentNullException(nameof(jobSettings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Core.Contracts.Result<CompleteProcessingJobResponse>> ExecuteAsync(
            CompleteProcessingJobRequest request,
            CancellationToken cancellationToken = default)
        {
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Core.Contracts.Result<CompleteProcessingJobResponse>.Failure(validationResult.Errors);
            }

            var response = new CompleteProcessingJobResponse();

            try
            {
                // Get jobs that have been processed but not marked as complete yet
                var jobsToComplete = await GetJobsToCompleteAsync(request.BatchSize, cancellationToken);
                
                if (jobsToComplete.Count == 0)
                {
                    return Core.Contracts.Result<CompleteProcessingJobResponse>.Success(response);
                }
                response.UpdatedJobsCount = jobsToComplete.Count;

                foreach (var job in jobsToComplete)
                {
                    try
                    {
                        await HandleCompleteSingleJob(job, cancellationToken);
                       
                    }
                    catch (Exception ex)
                    {
                        // only log error to continue to next job
                        _logger.LogError(ex, "Error Completed job status");
                    }
                }

                return Core.Contracts.Result<CompleteProcessingJobResponse>.Success(response);
            }
            catch (Exception)
            {
                return Core.Contracts.Result<CompleteProcessingJobResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }

        private async Task<List<CompleteProcessingDataJobDto>> GetJobsToCompleteAsync(
            int batchSize,
            CancellationToken cancellationToken)
        {
            string sql;
            object parameters;
            
            // Query for both completed processing jobs and stale jobs
            // Query only for completed processing jobs
            sql = @"
            SELECT TOP (@BatchSize) 
                        Id
                        ,[RequestId]
                        ,[RetailerId]
                        ,[BranchId]
                        ,[Status]
                        ,[CreatedDate]
            FROM [dbo].[ProcessingDataJob] p
            where p.[Status] < 2
            and NOT EXISTS (
                select 1 from DeleteDataDetail d
                where d.ProcessingDataJobId = p.Id
                and d.[Status] NOT IN (1,2)
            )";
            
            parameters = new
            {
                BatchSize = batchSize
            };
            
            return (await _queryService.QueryPlainTextAsync<CompleteProcessingDataJobDto>(sql, parameters)).ToList();
        }

        private async Task HandleCompleteSingleJob(CompleteProcessingDataJobDto job, CancellationToken cancellationToken)
        {
            string sql = @"
            SELECT 
                Id,
                RequestId,
                BranchId,
                RetailerId,
                Status,
                FilterConditions
            FROM [dbo].[DeleteDataDetail]
            WHERE [ProcessingDataJobId] = @JobId";
            // update status to completed
            object parameters = new { JobId = job.Id };
            var deletedDetails = (await _queryService.QueryPlainTextAsync<CompleteDeleteDataDetailDto>(sql, parameters)).ToList();
            if(deletedDetails == null || deletedDetails.Count() == 0)
            {
                await UpdateProcessingDataJobStatusAsync(job.Id, ProcessingDataJobStatus.Failed, cancellationToken);
                return;
            }

            var requestId = deletedDetails.First().RequestId;
            var requestSql = @"
            SELECT TOP (1)
                Id,
                Email
            FROM [dbo].[DeleteDataRequest]
            WHERE [Id] = @RequestId";
            var request = (await _queryService.QueryPlainTextAsync<CompleteDeleteDataRequestDto>(requestSql, new { RequestId = requestId })).ToList().FirstOrDefault();   
            
            if(request == null)
            {
                throw new InvalidDataException("Không tìm thấy yêu cầu xoá dữ liệu");
            }

            var status = deletedDetails.All(d => d.Status == (int)DataDeletionDetailJobStatus.Completed) ? 
                            ProcessingDataJobStatus.Completed : 
                            ProcessingDataJobStatus.Failed;
            await UpdateProcessingDataJobStatusAsync(job.Id, status, cancellationToken);

            await WriteDeleteDataHistoryAsync(job, request.Email, status, deletedDetails, cancellationToken);

            if(status == ProcessingDataJobStatus.Completed && request.Email != null)
            {
                await SendCompletionNotificationAsync(request.Email, ShopUserName , job.Id, job.CreatedDate, DateTime.Now,
                            job.RetailerId, cancellationToken);
            }
        }

        private async Task WriteDeleteDataHistoryAsync(CompleteProcessingDataJobDto job, string email, ProcessingDataJobStatus status, List<CompleteDeleteDataDetailDto> deletedDetails, CancellationToken cancellationToken)
        {
            var filterCondition = deletedDetails.First().FilterConditions;
            string sql = @"
            INSERT INTO [dbo].[DeleteDataHistory] (RetailerId, RequestId, BranchId, Summary, ExecutedBy, ExecutedDate, FilterConditions, Status, Email, ProcessingDataJobId)
            VALUES (@RetailerId, @RequestId, @BranchId, @Summary, @ExecutedBy, @ExecutedDate, @FilterConditions, @Status, @Email, @ProcessingDataJobId)";

            var summary = status == ProcessingDataJobStatus.Completed ? "Đã xoá thành công" : "Đã xoá thất bại";

            var systemUserId = await GetAdminUserAsync(job.RetailerId);
            if(systemUserId == null)
            {
                throw new InvalidDataException("Không tìm thấy user admin");
            }

            var parameters = new
            {
                RetailerId = job.RetailerId,
                RequestId = job.RequestId,
                BranchId = job.BranchId,
                Summary = summary.ToString(),
                ExecutedBy = systemUserId,
                ExecutedDate = job.CreatedDate,
                FilterConditions = filterCondition,
                Status = (byte)status,
                Email = email,
                ProcessingDataJobId = job.Id
            };  

            await _queryService.ExecutePlainTextAsync(sql, parameters);
        }

        private async Task<int?> GetAdminUserAsync(int retailerId) {
            string sql = @"
            SELECT TOP 1 [Id]
            FROM [dbo].[User]
            WHERE [RetailerId] = @RetailerId
            AND [IsAdmin] = 1";
            var parameters = new { RetailerId = retailerId };
            return (await _queryService.QueryPlainTextAsync<int>(sql, parameters)).FirstOrDefault();
        }

        private async Task UpdateProcessingDataJobStatusAsync(long jobId, ProcessingDataJobStatus status, CancellationToken cancellationToken)
        {
            string sql = @"
            UPDATE [dbo].[ProcessingDataJob]
            SET [Status] = @Status
            WHERE [Id] = @JobId";
            var parameters = new { JobId = jobId, Status = status };
            await _queryService.ExecutePlainTextAsync(sql, parameters);  
        }   

        private async Task SendCompletionNotificationAsync(string userEmail, string userName, long jobId, DateTime createdAt, DateTime? completedDate, int retailerId, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(userEmail))
            {
                return;
            }

            // Get email configuration
            var templateId = _jobSettings.Value.EmailNotification.TemplateId;
            var fromEmail = _jobSettings.Value.EmailNotification.FromEmail;
            var fromName = _jobSettings.Value.EmailNotification.FromName;

            // Prepare template data
            var templateData = new
            {
                JobId = jobId,
                RequestDate = createdAt,
                CompletionDate = completedDate,
                RetailerId = retailerId,
                UserName = userName ?? "Customer"
            };

            try
            {
                // Send email
                await _emailService.SendTemplateEmailAsync(
                    userEmail,
                    templateId,
                    templateData,
                    fromEmail,
                    fromName,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send completion notification email");
            }
        }
    }
}
```

### 3.3. Email Service và Template Renderer

Email Service được sử dụng để gửi thông báo cho người dùng khi quá trình xóa dữ liệu hoàn tất. Email Service được định nghĩa thông qua interface IEmailService và sử dụng ITemplateRenderer để render nội dung email.

#### 3.3.1. IEmailService

```csharp
using System.Threading;
using System.Threading.Tasks;

namespace KvFnB.Modules.DataManagement.Domain.Services
{
    /// <summary>
    /// Interface for email sending operations
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Sends an email using a predefined template
        /// </summary>
        /// <param name="recipientEmail">Email address of the recipient</param>
        /// <param name="templateId">ID of the email template to use</param>
        /// <param name="templateData">Data to populate the template</param>
        /// <param name="fromEmail">Sender email address</param>
        /// <param name="fromName">Sender display name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendTemplateEmailAsync(
            string recipientEmail,
            string templateId,
            object templateData,
            string fromEmail,
            string fromName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends a simple email message
        /// </summary>
        /// <param name="recipientEmail">Email address of the recipient</param>
        /// <param name="subject">Email subject</param>
        /// <param name="htmlBody">HTML content of the email</param>
        /// <param name="textBody">Plain text content of the email</param>
        /// <param name="fromEmail">Sender email address</param>
        /// <param name="fromName">Sender display name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendEmailAsync(
            string recipientEmail,
            string subject,
            string htmlBody,
            string textBody,
            string fromEmail,
            string fromName,
            CancellationToken cancellationToken = default);
    }
}
```

#### 3.3.2. ITemplateRenderer

```csharp
using System.Threading.Tasks;

namespace KvFnB.Modules.DataManagement.Infrastructure.Services
{
    /// <summary>
    /// Service for rendering email templates
    /// </summary>
    public interface ITemplateRenderer
    {
        /// <summary>
        /// Renders the subject line for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered subject</returns>
        Task<string> RenderSubjectAsync(string templateId, object data);
        
        /// <summary>
        /// Renders the HTML body for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered HTML body</returns>
        Task<string> RenderHtmlBodyAsync(string templateId, object data);
        
        /// <summary>
        /// Renders the plain text body for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered text body</returns>
        Task<string> RenderTextBodyAsync(string templateId, object data);
    }
}
```

## 4. Triển Khai Background Job

### 4.1. Thiết Lập Background Service

```csharp
public class CompleteJobHostedService : BackgroundService
{
    private readonly ILogger<CompleteJobHostedService> _logger;
    private readonly CompleteDataProcessConfiguration _completeDateConfig;
    private readonly IDistributedLockProvider _lockProvider;
    private readonly IConfiguration _configuration;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private const string LOCK_KEY = "CompleteJobProcessing";

    public CompleteJobHostedService(
        ILogger<CompleteJobHostedService> logger,
        IOptions<CompleteDataProcessConfiguration> completeDataConfig,
        IDistributedLockProvider lockProvider,
        IConfiguration configuration,
        IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _completeDateConfig = completeDataConfig?.Value ?? throw new ArgumentNullException(nameof(completeDataConfig));
        _lockProvider = lockProvider ?? throw new ArgumentNullException(nameof(lockProvider));
        _configuration = configuration;
        _serviceScopeFactory = serviceScopeFactory;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Complete Job Service is starting.");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Complete Job Service is running at: {time}", DateTimeOffset.Now);
            
            try
            {
                var currentHour = DateTime.Now.Hour;
                
                // Kiểm tra xem thời gian hiện tại có nằm trong cửa sổ thực thi không
                bool isWithinExecutionWindow = IsWithinExecutionWindow(currentHour);
                
                if (isWithinExecutionWindow)
                {
                    _logger.LogInformation("Current time is within execution window. Executing job with timeout of {timeout} minutes.", 
                        _completeDateConfig.CompleteJob.CancellationTimeMinutes);
                    
                    // Tạo CancellationTokenSource cho timeout của job
                    using (var jobCts = new CancellationTokenSource(TimeSpan.FromMinutes(_completeDateConfig.CompleteJob.CancellationTimeMinutes)))
                    using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(jobCts.Token, stoppingToken))
                    {
                        try
                        {
                            // Sử dụng distributed lock để đảm bảo chỉ có một instance chạy tại một thời điểm
                            await _lockProvider.LockAsync(
                                LOCK_KEY,
                                TimeSpan.FromMinutes(_completeDateConfig.CompleteJob.CancellationTimeMinutes),
                                async () =>
                                {
                                    _logger.LogInformation("Acquired distributed lock for CompleteJob processing");
                                    
                                    using (var scope = _serviceScopeFactory.CreateScope())
                                    {
                                        var useCase = scope.ServiceProvider.GetRequiredService<CompleteProcessingJobUseCase>();
                                        var request = new CompleteProcessingJobRequest
                                        {
                                            BatchSize = _completeDateConfig.CompleteJob.BatchSize
                                        };
                                        
                                        var result = await useCase.ExecuteAsync(request, linkedCts.Token);
                                        
                                        if (!result.IsSuccess)
                                        {
                                            _logger.LogWarning("CompleteProcessingJob failed: {errors}", string.Join(", ", result.Errors));
                                        }
                                    }
                                    
                                    _logger.LogInformation("Released distributed lock for CompleteJob processing");
                                });
                        }
                        catch (OperationCanceledException)
                        {
                            if (jobCts.IsCancellationRequested && !stoppingToken.IsCancellationRequested)
                            {
                                _logger.LogWarning("Job execution was canceled after timeout of {timeout} minutes", 
                                    _completeDateConfig.CompleteJob.CancellationTimeMinutes);
                            }
                            else
                            {
                                throw;
                            }
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("Current time {currentHour} is outside execution window ({fromHour} to {toHour}). Skipping execution.", 
                        currentHour, _completeDateConfig.CompleteJob.ExecutionTime.FromHour, _completeDateConfig.CompleteJob.ExecutionTime.ToHour);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing completed jobs");
            }
            
            // Chờ đến lần chạy tiếp theo
            await Task.Delay(TimeSpan.FromMinutes(_completeDateConfig.CompleteJob.IntervalMinutes), stoppingToken);
        }
        
        _logger.LogInformation("Complete Job Service is stopping.");
    }
    
    private bool IsWithinExecutionWindow(int currentHour)
    {
        int fromHour = _completeDateConfig.CompleteJob.ExecutionTime.FromHour;
        int toHour = _completeDateConfig.CompleteJob.ExecutionTime.ToHour;
        
        // Trường hợp cửa sổ thực thi là qua nửa đêm (ví dụ: 23:00 - 05:00)
        if (fromHour > toHour)
        {
            return currentHour >= fromHour || currentHour <= toHour;
        }
        // Trường hợp cửa sổ thực thi là trong cùng một ngày (ví dụ: 01:00 - 05:00)
        else
        {
            return currentHour >= fromHour && currentHour <= toHour;
        }
    }
}
```

### 4.2. Dependency Injection

```csharp
// Trong Module Registrar
public static class ModuleRegistrar
{
    public static IServiceCollection RegisterDataManagementModule(this IServiceCollection services, IConfiguration configuration)
    {
        // Đăng ký use cases
        services.AddScoped<CompleteProcessingJobUseCase>();
        
        // Đăng ký validators
        services.AddScoped<IValidator<CompleteProcessingJobRequest>, CompleteProcessingJobValidator>();
        
        // Đăng ký cấu hình
        services.Configure<CompleteDataProcessConfiguration>(
            configuration.GetSection("CompleteDataProcessSettings"));
        
        // Đăng ký Hosted Service
        services.AddHostedService<CompleteJobHostedService>();
        
        // Đăng ký các dependencies
        services.AddScoped<IEmailService, EmailService>();
        
        return services;
    }
}
```

## 5. Xử Lý Chống Lỗi và Tính Mạnh Mẽ

### 5.1. Đảm Bảo Idempotency

UseCase được thiết kế để xử lý idempotent, đảm bảo rằng nếu cùng một job được xử lý nhiều lần, kết quả vẫn nhất quán. Điều này đạt được thông qua:

1. **Kiểm tra trạng thái hiện tại**: Chỉ cập nhật jobs với trạng thái chưa hoàn thành (Status < 2)
2. **Xác định xong xử lý**: Tự động phát hiện và đánh dấu các jobs khi tất cả các chi tiết đã hoàn tất
3. **Xử lý riêng lẻ**: Mỗi job được xử lý độc lập, lỗi của một job không ảnh hưởng đến các jobs khác

### 5.2. Xử Lý Lỗi

UseCase bao gồm xử lý lỗi toàn diện tại nhiều cấp độ:

1. **Try-catch bao quanh toàn bộ quy trình**: Đảm bảo lỗi không lan truyền ra background service
2. **Try-catch cho từng job riêng lẻ**: Đảm bảo lỗi của một job không ảnh hưởng đến các jobs khác, với ghi log chi tiết
   ```csharp
   try
   {
       await HandleCompleteSingleJob(job, cancellationToken);
   }
   catch (Exception ex)
   {
       // only log error to continue to next job
       _logger.LogError(ex, "Error Completed job status");
   }
   ```
3. **Xử lý null hoặc empty results**: Bổ sung thêm kiểm tra null/empty cho kết quả query, đảm bảo không xảy ra lỗi khi dữ liệu không tồn tại
   ```csharp
   if(deletedDetails == null || deletedDetails.Count() == 0)
   {
       await UpdateProcessingDataJobStatusAsync(job.Id, ProcessingDataJobStatus.Failed, cancellationToken);
       return;
   }
   ```
4. **Kiểm tra request**: Kiểm tra dữ liệu request đảm bảo tính toàn vẹn dữ liệu
   ```csharp
   if(request == null)
   {
       throw new InvalidDataException("Không tìm thấy yêu cầu xoá dữ liệu");
   }
   ```
5. **Xử lý lỗi gửi email**: Bao bọc việc gửi email trong try-catch để đảm bảo thao tác hoàn tất ngay cả khi gửi email thất bại
   ```csharp
   try
   {
       // Send email
       await _emailService.SendTemplateEmailAsync(
           userEmail,
           templateId,
           templateData,
           fromEmail,
           fromName,
           cancellationToken);
   }
   catch (Exception ex)
   {
       _logger.LogError(ex, "Failed to send completion notification email");
   }
   ```
6. **Ghi log chi tiết**: Ghi nhận đầy đủ thông tin lỗi để dễ dàng debug và theo dõi
7. **Áp dụng ErrorMessages.InternalServerError**: Tuân thủ quy tắc error handling, không trả về message lỗi trực tiếp
   ```csharp
   catch (Exception)
   {
       return Core.Contracts.Result<CompleteProcessingJobResponse>.Failure(ErrorMessages.InternalServerError);
   }
   ```

### 5.3. Timeout Handling

Cơ chế timeout được triển khai để đảm bảo job không chạy quá lâu:

1. **Cấu hình thời gian hủy**: Mỗi lần chạy job có thời gian tối đa được cấu hình trong `CompleteDataProcessConfiguration.CompleteJob.CancellationTimeMinutes` (mặc định 30 phút)
2. **CancellationToken**: Sử dụng CancellationTokenSource để tự động hủy job nếu chạy quá thời gian quy định:
   ```csharp
   using (var jobCts = new CancellationTokenSource(TimeSpan.FromMinutes(_completeDateConfig.CompleteJob.CancellationTimeMinutes)))
   using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(jobCts.Token, stoppingToken))
   {
       // Job execution with timeout
   }
   ```
3. **Xử lý graceful**: Bắt OperationCanceledException để ghi log và xử lý khi job bị hủy do timeout:
   ```csharp
   catch (OperationCanceledException)
   {
       if (jobCts.IsCancellationRequested && !stoppingToken.IsCancellationRequested)
       {
           _logger.LogWarning("Job execution was canceled after timeout of {timeout} minutes", 
               _completeDateConfig.CompleteJob.CancellationTimeMinutes);
       }
       else
       {
           throw;
       }
   }
   ```
4. **Xử lý lỗi từng job**: Ngay cả khi một job riêng lẻ gặp lỗi, các job khác vẫn được xử lý nhờ vào cơ chế try-catch cho từng job:
   ```csharp
   foreach (var job in jobsToComplete)
   {
       try
       {
           await HandleCompleteSingleJob(job, cancellationToken);
       }
       catch (Exception ex)
       {
           // only log error to continue to next job
           _logger.LogError(ex, "Error Completed job status");
       }
   }
   ```

### 5.4. Đồng Bộ Hóa Phân Tán

Để đảm bảo chỉ có một instance của background job chạy tại một thời điểm trong môi trường phân tán, chúng ta sử dụng `IDistributedLockProvider`:

1. **Khóa phân tán**: Sử dụng khóa với key cố định "CompleteJobProcessing" để đảm bảo các host khác nhau không xử lý cùng lúc:
   ```csharp
   await _lockProvider.LockAsync(LOCK_KEY, TimeSpan.FromMinutes(_completeDateConfig.CompleteJob.CancellationTimeMinutes), async () => 
   {
       // Job execution with distributed lock
   });
   ```

2. **Timeout khóa**: Khóa sẽ tự động giải phóng sau thời gian timeout được cấu hình theo `CancellationTimeMinutes`, đảm bảo không bị lock vĩnh viễn nếu ứng dụng gặp sự cố

3. **Cửa sổ thực thi thông minh**: Chỉ chạy job trong khung giờ cấu hình để tránh ảnh hưởng đến hiệu suất hệ thống trong giờ cao điểm:
   ```csharp
   private bool IsWithinExecutionWindow(int currentHour)
   {
       int fromHour = _completeDateConfig.CompleteJob.ExecutionTime.FromHour;
       int toHour = _completeDateConfig.CompleteJob.ExecutionTime.ToHour;
       
       // Trường hợp cửa sổ thực thi là qua nửa đêm (ví dụ: 23:00 - 05:00)
       if (fromHour > toHour)
       {
           return currentHour >= fromHour || currentHour <= toHour;
       }
       // Trường hợp cửa sổ thực thi là trong cùng một ngày (ví dụ: 01:00 - 05:00)
       else
       {
           return currentHour >= fromHour && currentHour <= toHour;
       }
   }
   ```

4. **Scoped Service**: Sử dụng `IServiceScopeFactory` để tạo scope mới cho mỗi lần chạy job, đảm bảo quản lý tài nguyên hiệu quả và tránh memory leak:
   ```csharp
   using (var scope = _serviceScopeFactory.CreateScope())
   {
       var useCase = scope.ServiceProvider.GetRequiredService<CompleteProcessingJobUseCase>();
       var request = new CompleteProcessingJobRequest
       {
           BatchSize = _completeDateConfig.CompleteJob.BatchSize
       };
       
       var result = await useCase.ExecuteAsync(request, linkedCts.Token);
   }
   ```

### 5.5. Luồng Xử Lý Job

Luồng xử lý job đã được triển khai như sau:

1. **Lấy danh sách jobs cần xử lý**: Sử dụng `GetJobsToCompleteAsync` để lấy các jobs đang trong trạng thái xử lý và không còn job con đang xử lý
   ```csharp
   sql = @"
   SELECT TOP (@BatchSize) 
               Id
               ,[RequestId]
               ,[RetailerId]
               ,[BranchId]
               ,[Status]
               ,[CreatedDate]
   FROM [dbo].[ProcessingDataJob] p
   where p.[Status] < 2
   and NOT EXISTS (
       select 1 from DeleteDataDetail d
       where d.ProcessingDataJobId = p.Id
       and d.[Status] NOT IN (1,2)
   )";
   ```

2. **Xử lý từng job riêng biệt**: Mỗi job được xử lý độc lập thông qua phương thức `HandleCompleteSingleJob`, với try-catch riêng để đảm bảo một job lỗi không ảnh hưởng đến các jobs khác

3. **Lấy thông tin chi tiết và xác định trạng thái**: Kiểm tra chi tiết từng DeleteDataDetail để quyết định trạng thái cuối cùng của job
   ```csharp
   var status = deletedDetails.All(d => d.Status == (int)DataDeletionDetailJobStatus.Completed) ? 
                   ProcessingDataJobStatus.Completed : 
                   ProcessingDataJobStatus.Failed;
   ```

4. **Cập nhật trạng thái job**: Cập nhật trạng thái của job trong cơ sở dữ liệu
   ```csharp
   await UpdateProcessingDataJobStatusAsync(job.Id, status, cancellationToken);
   ```

5. **Ghi lịch sử xóa dữ liệu**: Tạo bản ghi lịch sử trong `DeleteDataHistory` để theo dõi quá trình xử lý
   ```csharp
   await WriteDeleteDataHistoryAsync(job, request.Email, status, deletedDetails, cancellationToken);
   ```
   Với phương thức `WriteDeleteDataHistoryAsync` lưu đầy đủ thông tin, bao gồm cả điều kiện lọc và email:
   ```csharp
   string sql = @"
   INSERT INTO [dbo].[DeleteDataHistory] (RetailerId, RequestId, BranchId, Summary, ExecutedBy, ExecutedDate, FilterConditions, Status, Email, ProcessingDataJobId)
   VALUES (@RetailerId, @RequestId, @BranchId, @Summary, @ExecutedBy, @ExecutedDate, @FilterConditions, @Status, @Email, @ProcessingDataJobId)";

   var summary = status == ProcessingDataJobStatus.Completed ? "Đã xoá thành công" : "Đã xoá thất bại";

   var systemUserId = await GetAdminUserAsync(job.RetailerId);
   if(systemUserId == null)
   {
       throw new InvalidDataException("Không tìm thấy user admin");
   }

   var parameters = new
   {
       RetailerId = job.RetailerId,
       RequestId = job.RequestId,
       BranchId = job.BranchId,
       Summary = summary.ToString(),
       ExecutedBy = systemUserId,
       ExecutedDate = job.CreatedDate,
       FilterConditions = filterCondition,
       Status = (byte)status,
       Email = email,
       ProcessingDataJobId = job.Id
   };  
   ```

6. **Gửi thông báo có điều kiện**: Chỉ gửi thông báo khi job hoàn thành thành công và có địa chỉ email người dùng
   ```csharp
   if(status == ProcessingDataJobStatus.Completed && request.Email != null)
   {
       await SendCompletionNotificationAsync(request.Email, ShopUserName, job.Id, job.CreatedDate, DateTime.Now,
                    job.RetailerId, cancellationToken);
   }
   ```

7. **Tổng hợp và trả về kết quả**: Cập nhật số lượng jobs đã xử lý vào response và trả về kết quả
   ```csharp
   response.UpdatedJobsCount = jobsToComplete.Count;
   return Core.Contracts.Result<CompleteProcessingJobResponse>.Success(response);
   ```

Mỗi bước trong luồng xử lý đều được thiết kế để đảm bảo độ tin cậy và khả năng phục hồi, ngay cả khi có lỗi xảy ra tại một bước cụ thể.

## 6. Kết Luận

CompleteProcessingJobUseCase là một thành phần quan trọng trong quy trình xóa dữ liệu, đảm bảo tính toàn vẹn dữ liệu và cung cấp thông báo kịp thời cho người dùng. Với thiết kế theo chuẩn hexagonal architecture và Domain-Driven Design, UseCase này mang lại nhiều lợi ích:

- **Tính mô-đun hóa cao**: Các thành phần như IDataManagementQueryService, IEmailService, và IOptions<CompleteDataProcessConfiguration> được tiêm vào một cách linh hoạt, giúp dễ dàng test và mở rộng
- **Xử lý SQL trực tiếp**: Sử dụng các truy vấn SQL trực tiếp với đối tượng tham số mạnh mẽ, giúp tối ưu hiệu suất và kiểm soát chính xác cách truy xuất dữ liệu
- **Xử lý lỗi chuyên nghiệp**: Tuân thủ quy tắc error handling, không để lộ chi tiết lỗi nội bộ và đảm bảo hệ thống tiếp tục hoạt động ngay cả khi có lỗi xảy ra
- **Cấu trúc đa tầng rõ ràng**: 
  - DTOs chuyên biệt cho từng loại dữ liệu (CompleteProcessingDataJobDto, CompleteDeleteDataDetailDto)
  - UseCase chịu trách nhiệm điều phối luồng xử lý
  - Cấu hình tập trung qua CompleteDataProcessConfiguration
- **Khả năng mở rộng**: Sử dụng chiến lược xử lý phân tán với IDistributedLockProvider, cho phép triển khai trên nhiều máy chủ mà không xảy ra xung đột
- **Hiệu suất tối ưu**: Chỉ chạy trong khung giờ hệ thống thấp tải (23:00 - 05:00), giúp giảm thiểu ảnh hưởng đến trải nghiệm người dùng

Việc tích hợp IEmailService cho phép hệ thống gửi thông báo linh hoạt mà không cần thay đổi code. Với cơ chế batch processing và quản lý lỗi chi tiết, CompleteProcessingJobUseCase đảm bảo các jobs xóa dữ liệu được xử lý kịp thời, cập nhật trạng thái và thông báo cho người dùng một cách đáng tin cậy, hoàn thiện quy trình xóa dữ liệu gian hàng. 