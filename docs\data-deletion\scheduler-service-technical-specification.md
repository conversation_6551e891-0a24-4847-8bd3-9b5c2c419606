# Technical Specification: Scheduler Service cho Chức Năng Xóa Dữ Liệu

## 1. Tổng Quan

Scheduler Service là thành phần quan trọng trong kiến trúc hệ thống xóa dữ liệu gian <PERSON>, chị<PERSON> tr<PERSON>ch nhiệm quản lý các lịch trình xóa dữ liệu tự động theo cấu hình của người dùng. Service này đảm bảo các yêu cầu xóa dữ liệu được xử lý đúng thời điểm, theo đúng chu kỳ, và có khả năng phục hồi khi xảy ra lỗi.

### 1.1. <PERSON><PERSON><PERSON>i<PERSON>h

- Quản lý các lịch trình xóa dữ liệu tự động với nhiều tần suất khác nhau (hàng ngà<PERSON>, hàng tu<PERSON>, hà<PERSON> thán<PERSON>, hà<PERSON> qu<PERSON>, h<PERSON><PERSON> nă<PERSON>)
- <PERSON><PERSON><PERSON> bảo việc xử lý các yêu cầu xóa dữ liệu vào thời điể<PERSON> phù <PERSON>, gi<PERSON>m tải cho hệ thống

## 2. Kiến Trúc 

### 2.1. Sơ Đồ Tổng Thể

```
+------------------------+      +------------------------+
|                        |      |                        |
| Delete Data Service    +----->+ Scheduler Service      |
|                        |      |                        |
+------------------------+      +-----------+------------+
                                            |
                                            v
+------------------------+      +------------------------+
|                        |      |                        |
| Data Processing Service|<-----+ Message Queue          |
|                        |      |                        |
+------------------------+      +------------------------+
```

### 2.2. Các Thành Phần Chính của Scheduler Service

#### 2.2.1. Schedule Scanner
- Quét định kỳ 1h đêm hàng ngày bảng `DeleteDataRequest` để tìm các yêu cầu đã được lập lịch
- Sử dụng distributed lock để đảm bảo chỉ một instance thực hiện quét tại một thời điểm 
- Tính toán và cập nhật thời điểm thực thi tiếp theo cho mỗi lịch trình

#### 2.2.2. Job Dispatcher
- Phát hiện các lịch trình cần được thực thi dựa trên thời gian hiện tại
- Tạo các job và đẩy vào hàng đợi để Data Processing Service xử lý

## 3. Cấu Trúc Dữ Liệu Lập Lịch

Scheduler Service sử dụng định dạng JSON sau đây để cấu hình lịch trình xóa dữ liệu, cấu hình này được lấy từ bảng ```DeleteDataRequest``` trường ```ScheduleConfig```:

```json
{
  "scheduleType": "monthly", // "daily", "weekly", "monthly", "quarterly", "yearly"
  "dayOfWeek": null, // 0-6 (chỉ áp dụng cho weekly)
  "dayOfMonth": 15, // 1-31 (chỉ áp dụng cho monthly)
  "month": null, // 1-12 (chỉ áp dụng cho yearly)
  "quarterMonth": null, // 1-3 (chỉ áp dụng cho quarterly)
  "executionTime": "02:00:00", // Giờ thực hiện
  "retentionMonths": 3, // Số tháng giữ lại dữ liệu
  "active": true,
  "nextExecutionDate": "2025-06-15T02:00:00"
}
```

### 3.1. Các Loại Lịch Trình Hỗ Trợ

| Loại Lịch Trình | Mô Tả | Tham Số Bắt Buộc |
|-----------------|-------|------------------|
| Daily | Thực hiện hàng ngày vào giờ cố định | executionTime |
| Weekly | Thực hiện hàng tuần vào ngày và giờ cố định | dayOfWeek, executionTime |
| Monthly | Thực hiện hàng tháng vào ngày và giờ cố định | dayOfMonth, executionTime |
| Quarterly | Thực hiện hàng quý vào tháng, ngày và giờ cố định | quarterMonth, dayOfMonth, executionTime |
| Yearly | Thực hiện hàng năm vào tháng, ngày và giờ cố định | month, dayOfMonth, executionTime |

### 3.2. Tính Toán Thời Gian Thực Hiện Tiếp Theo

Scheduler Service sẽ tính toán thời gian thực hiện tiếp theo cho mỗi lịch trình dựa trên các quy tắc sau:

```csharp
public DateTime CalculateNextExecutionTime(ScheduleConfig config, DateTime currentTime)
{
    switch (config.ScheduleType)
    {
        case "daily":
            return currentTime.Date.Add(TimeSpan.Parse(config.ExecutionTime)).AddDays(1);
        
        case "weekly":
            int daysUntilNextDay = ((int)config.DayOfWeek - (int)currentTime.DayOfWeek + 7) % 7;
            if (daysUntilNextDay == 0 && currentTime.TimeOfDay > TimeSpan.Parse(config.ExecutionTime))
                daysUntilNextDay = 7;
            return currentTime.Date.Add(TimeSpan.Parse(config.ExecutionTime)).AddDays(daysUntilNextDay);
        
        case "monthly":
            var nextDate = new DateTime(currentTime.Year, currentTime.Month, 1).AddMonths(1);
            int targetDay = Math.Min(config.DayOfMonth, DateTime.DaysInMonth(nextDate.Year, nextDate.Month));
            return new DateTime(nextDate.Year, nextDate.Month, targetDay)
                .Add(TimeSpan.Parse(config.ExecutionTime));
        
        // Tương tự cho quarterly và yearly
    }
}
```

## 4. Quy Trình Xử Lý Chính

### 4.1. Quy Trình Quét và Kích Hoạt Lịch Trình

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│ Schedule Scanner│────>│  Job Dispatcher │────>│ Message Queue   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                      │                        │
        │                      │                        │
        v                      v                        v
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│ Database (Read) │     │Database (Update)│     │Data Processing  │
│                 │     │                 │     │    Service      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

**Các bước thực hiện:**

1. **Schedule Scanner:** 
   - Chạy mỗi ngày một lần lúc 1h sáng
   - Lấy distributed lock để đảm bảo chỉ một instance thực hiện
   - Quét các yêu cầu có trạng thái "Approved" và loại "Scheduled"
   - Kiểm tra `nextExecutionDate` để xác định có cần kích hoạt không

2. **Job Dispatcher (using kafka):**
   - Nếu `nextExecutionDate <= currentTime`, kích hoạt yêu cầu
   - Tạo job mới với trạng thái "Pending" trong bảng `ProcessingDataJob`
   - Cập nhật trạng thái yêu cầu và tính toán `nextExecutionDate` mới
   - Đẩy job vào message queue

## 5. Triển Khai Highly Available

### 5.1. Distributed Locking

Để đảm bảo chỉ một instance của Scheduler Service thực hiện quét và kích hoạt lịch trình tại một thời điểm, service sử dụng distributed lock với Redis:

```csharp
public async Task<bool> AcquireLock(string lockName, TimeSpan duration)
{
    var expiry = duration.TotalSeconds;
    var lockValue = Guid.NewGuid().ToString();
    
    var acquired = await redisClient.StringSetAsync(
        $"lock:{lockName}",
        lockValue,
        TimeSpan.FromSeconds(expiry),
        When.NotExists);
    
    if (acquired)
    {
        _lockValue = lockValue;
        return true;
    }
    
    return false;
}

public async Task ReleaseLock(string lockName)
{
    if (string.IsNullOrEmpty(_lockValue))
        return;
    
    var script = @"
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end";
    
    await redisClient.ScriptEvaluateAsync(script,
        new RedisKey[] { $"lock:{lockName}" },
        new RedisValue[] { _lockValue });
    
    _lockValue = null;
}
```
## 6. Kết Luận

Scheduler Service là thành phần quan trọng trong hệ thống xóa dữ liệu gian hàng, đảm bảo các yêu cầu được xử lý đúng thời điểm và có khả năng phục hồi cao khi xảy ra lỗi. Với thiết kế highly available, retry thông minh và khả năng tích hợp, service này cung cấp một giải pháp đáng tin cậy cho việc tự động hóa quá trình xóa dữ liệu định kỳ.

Key features của Scheduler Service bao gồm:
- Hỗ trợ nhiều loại lịch trình (daily, weekly, monthly, quarterly, yearly)
- Distributed locking đảm bảo không xảy ra xử lý trùng lặp
- Retry mechanism thông minh với exponential backoff sử dụng thư viện Polly
- Auto-failover khi instance chính gặp sự cố
- Phân tách job lớn thành nhiều batch nhỏ để tối ưu hiệu năng
- Monitoring đầy đủ với metrics, logs và alerting

Với những đặc điểm này, Scheduler Service đáp ứng đầy đủ yêu cầu về một hệ thống lập lịch tự động, đáng tin cậy và có khả năng mở rộng cho tính năng xóa dữ liệu gian hàng. 