﻿namespace KvFnB.Core.Domain.Models
{
    public class Branch : AggregateRoot<int>, IAuditableEntity
    {
        // Basic Information
        public string Name { get; private set; } = string.Empty;
        public string? Code { get; private set; }
        public string? TaxCode { get; private set; }
        public byte Type { get; private set; }

        // Location Information
        public string? Address { get; private set; }
        public string? LocationName { get; private set; }
        public string? WardName { get; private set; }
        public string? Province { get; private set; }
        public string? District { get; private set; }

        // Contact Information
        public string? ContactNumber { get; private set; }
        public string? Email { get; private set; }

        // Branch Settings
        public bool IsActive { get; private set; }
        public bool? LimitAccess { get; private set; }

        public string? TimeZone { get; private set; }
        public int? CountryId { get; private set; }
        public long? AddressId { get; private set; }
        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public long? ModifiedBy { get; set; }
        private Branch() { }

        public static Branch Create(
            string name,
            byte type,
            string? code = null,
            string? taxCode = null,
            string? address = null,
            string? locationName = null,
            string? wardName = null,
            string? province = null,
            string? district = null,
            string? contactNumber = null,
            string? email = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            return new Branch
            {
                Name = name,
                Type = type,
                Code = code,
                TaxCode = taxCode,
                Address = address,
                LocationName = locationName,
                WardName = wardName,
                Province = province,
                District = district,
                ContactNumber = contactNumber,
                Email = email,
                IsActive = true,
            };
        }

        public void Update(
            string name,
            byte type,
            string? code,
            string? taxCode,
            string? address,
            string? locationName,
            string? wardName,
            string? province,
            string? district,
            string? contactNumber,
            string? email)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            Name = name;
            Type = type;
            Code = code;
            TaxCode = taxCode;
            Address = address;
            LocationName = locationName;
            WardName = wardName;
            Province = province;
            District = district;
            ContactNumber = contactNumber;
            Email = email;
        }


        public void UpdateLocation(
            int? countryId,
            string? timeZone)
        {
            CountryId = countryId;
            TimeZone = timeZone;
        }

        public void UpdateSettings(
            bool? limitAccess)
        {
            LimitAccess = limitAccess;
        }

        public void Activate()
        {
            IsActive = true;
        }

        public void Deactivate()
        {
            IsActive = false;
        }
    }
}
