﻿using Dapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Domain.Entities;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.GetPageDeleteDataRequests
{
    public class GetPageDeleteDataRequestsUseCase : UseCaseBase<GetPageDeleteDataRequestsRequest, GetPageDeleteDataRequestsResponse>
    {
        private readonly IValidator<GetPageDeleteDataRequestsRequest> _validator;
        private readonly ITenantProvider _tenantProvider;
        private readonly IQueryService _queryService;
        private readonly IMapper _mapper;

        public GetPageDeleteDataRequestsUseCase(
            IValidator<GetPageDeleteDataRequestsRequest> validator,
            ITenantProvider tenantProvider,
            IQueryService queryService,
            IMapper mapper)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public override async Task<Result<GetPageDeleteDataRequestsResponse>> ExecuteAsync(
            GetPageDeleteDataRequestsRequest request,
            CancellationToken cancellationToken = default)
        {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<GetPageDeleteDataRequestsResponse>.Failure(validationResult.Errors);
                }

                var retailerId = _tenantProvider.GetTenantId() ?? 0;
                var parameters = new DynamicParameters();
                parameters.Add("retailerId", retailerId);
                parameters.Add("offset", (request.PageNumber - 1) * request.PageSize);
                parameters.Add("pageSize", request.PageSize);

                // Get total count of records
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM DeleteDataRequest d WITH(NOLOCK)
                    WHERE d.RetailerId = @retailerId AND Status NOT IN (3,4)";

                var total = (await _queryService.QueryPlainTextAsync<int>(countQuery, parameters)).FirstOrDefault();

                // Get data with pagination - Use DeleteDataRequest entity
                var query = $@"
                    SELECT
                        d.Id,
                        d.Email,
                        d.Type,
                        d.ScheduleType,
                        d.BranchIds,
                        d.ScheduleConfig,
                        d.FilterCondition,
                        d.Status,
                        d.ExecuteDate,
                        d.RetailerId
                    FROM DeleteDataRequest d WITH(NOLOCK)
                    WHERE d.RetailerId = @retailerId AND Status NOT IN (3,4) 
                    ORDER BY d.Id DESC
                    OFFSET @offset ROWS
                    FETCH NEXT @pageSize ROWS ONLY";

                var requests = await _queryService.QueryPlainTextAsync<DeleteDataRequest>(query, parameters);
                
                // Use AutoMapper to map from DeleteDataRequest to DeleteDataRequestResponse
                var mappedRequests = _mapper.Map<List<DeleteDataRequestResponse>>(requests);

                return Result<GetPageDeleteDataRequestsResponse>.Success(new GetPageDeleteDataRequestsResponse
                {
                    Items = mappedRequests,
                    TotalCount = total,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling(total / (double)request.PageSize)
                });
        }
    }
} 