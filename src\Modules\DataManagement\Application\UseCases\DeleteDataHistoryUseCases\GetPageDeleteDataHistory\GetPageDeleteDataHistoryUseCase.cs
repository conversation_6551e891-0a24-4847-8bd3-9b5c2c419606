﻿using Dapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Domain.Entities;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetPageDeleteDataHistory
{
    public class GetPageDeleteDataHistoryUseCase : UseCaseBase<GetPageDeleteDataHistoryRequest, GetPageDeleteDataHistoryResponse>
    {
        private readonly IValidator<GetPageDeleteDataHistoryRequest> _validator;
        private readonly ITenantProvider _tenantProvider;
        private readonly IQueryService _queryService;
        private readonly IMapper _mapper;

        public GetPageDeleteDataHistoryUseCase(
            IValidator<GetPageDeleteDataHistoryRequest> validator,
            ITenantProvider tenantProvider,
            IQueryService queryService,
            IMapper mapper)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public override async Task<Result<GetPageDeleteDataHistoryResponse>> ExecuteAsync(
            GetPageDeleteDataHistoryRequest request,
            CancellationToken cancellationToken = default)
        {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<GetPageDeleteDataHistoryResponse>.Failure(validationResult.Errors);
                }

                var retailerId = _tenantProvider.GetTenantId() ?? 0;
                var parameters = new DynamicParameters();
                parameters.Add("retailerId", retailerId);
                parameters.Add("offset", (request.PageNumber - 1) * request.PageSize);
                parameters.Add("pageSize", request.PageSize);


                // Get total count of records
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM DeleteDataHistory d WITH(NOLOCK)
                    WHERE d.RetailerId = @retailerId";

                var total = (await _queryService.QueryPlainTextAsync<int>(countQuery, parameters)).FirstOrDefault();

                // Get data with pagination
                var query = $@"
                    SELECT
                        d.Id,
                        d.BranchId,
                        d.ExecutedDate AS ExecutedDate,
                        d.RequestId,
                        d.Email,
                        d.Summary,
                        d.Status,
                        d.FilterConditions,
                        d.ExecutedBy
                    FROM DeleteDataHistory d WITH(NOLOCK)
                    WHERE d.RetailerId = @retailerId 
                    ORDER BY d.Id DESC
                    OFFSET @offset ROWS
                    FETCH NEXT @pageSize ROWS ONLY";

                var histories = await _queryService.QueryPlainTextAsync<DeleteDataHistory>(query, parameters);
                // Use AutoMapper to map from DeleteDataRequest to DeleteDataRequestResponse
                var mappedRequests = _mapper.Map<List<DeleteDataHistoryResponse>>(histories);
                return Result<GetPageDeleteDataHistoryResponse>.Success(new GetPageDeleteDataHistoryResponse
                {
                    Items = mappedRequests,
                    TotalCount = total,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling(total / (double)request.PageSize)
                });
        }
    }
} 