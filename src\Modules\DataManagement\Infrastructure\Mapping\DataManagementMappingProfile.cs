using AutoMapper;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases;
using KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase;
using KvFnB.Modules.DataManagement.Domain.Models;
using KvFnB.Modules.DataManagement.Infrastructure.Helpers;
using KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataHistoryUseCases.GetPageDeleteDataHistory;

namespace KvFnB.Modules.DataManagement.Infrastructure.Mapping
{
    public class DataManagementMappingProfile : Profile
    {
        public DataManagementMappingProfile()
        {
            CreateMap<DeleteDataRequest, DeleteDataRequestResponse>()
                .ForMember(dest => dest.ScheduleConfig, opt => opt.MapFrom(src => 
                    DeserializationHelper.DeserializeScheduleConfig(src.ScheduleConfig ?? "")))
                .ForMember(dest => dest.FilterCondition, opt => opt.MapFrom(src => 
                    DeserializationHelper.DeserializeFilterCondition(src.FilterCondition ?? "")))
                .ForMember(dest => dest.BranchIds, opt => opt.MapFrom(src => 
                    DeserializationHelper.DeserializeBranchIds(src.BranchIds)));
            CreateMap<DeleteDataHistory, DeleteDataHistoryResponse>()
              .ForMember(dest => dest.FilterConditions, opt => opt.MapFrom(src =>
                  DeserializationHelper.DeserializeFilterCondition(src.FilterConditions ?? "")));
            CreateMap<DataProcessingRequest, DataProcessingMessage>();
            CreateMap<ProcessingResult, DataProcessingResponse>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.JobId, opt => opt.MapFrom(src => src.JobId))
                .ForMember(dest => dest.TotalRecordsProcessed, opt => opt.MapFrom(src => src.TotalRecordsProcessed))
                .ForMember(dest => dest.AlreadyProcessed, opt => opt.MapFrom(src => src.AlreadyProcessed));
        }
    }
} 