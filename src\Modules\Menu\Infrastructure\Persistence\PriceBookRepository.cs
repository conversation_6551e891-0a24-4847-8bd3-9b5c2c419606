using Microsoft.EntityFrameworkCore;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;

namespace KvFnB.Modules.Menu.Infrastructure.Persistence
{
    public class PriceBookRepository : BaseRepository<PriceBook, long>, IPriceBookRepository
    {
		public PriceBookRepository(ShardingDbContext context) : base(context)
        {
        }

        public async Task<bool> IsUniquePriceBookNameAsync(string name, long? excludePriceBookId = null, CancellationToken cancellationToken = default)
        {
            var query = _context.Set<PriceBook>().Where(p => p.Name == name && (p.IsDeleted == false || p.IsDeleted == null));

            if (excludePriceBookId.HasValue)
            {
                query = query.Where(p => p.Id != excludePriceBookId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
        /// <inheritdoc />
        public async Task<List<PriceBook>> GetByIdsAsync(IEnumerable<long> ids, CancellationToken cancellationToken = default)
        {
            var idList = ids.ToList();
            return await _context.Set<PriceBook>()
                .Include(pb => pb.PriceBookDetails)
                .Where(pb => idList.Contains(pb.Id))
                .ToListAsync(cancellationToken);
        }

        /// <inheritdoc />
        public void UpdateRangeAsync(IEnumerable<PriceBook> priceBooks)
        {
            _context.Set<PriceBook>().UpdateRange(priceBooks);
        }

        /// <inheritdoc />
        public async Task<List<PriceBook>> GetForProductAsync(long productId, CancellationToken cancellationToken = default)
        {
            // Find all price books that have a price book detail for this product
            return await _context.Set<PriceBook>()
                .Include(pb => pb.PriceBookDetails)
                .Where(pb => pb.PriceBookDetails.Any(pbd => pbd.ProductId == productId))
                .ToListAsync(cancellationToken);
        }
    }
}

