<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
        Microsoft ResX Schema 
        
        Version 2.0
        
        The primary goals of this format is to allow a simple XML format 
        that is mostly human readable. The generation and parsing of the 
        various data types are done through the TypeConverter classes 
        associated with the data types.
        
        Example:
        
        ... ado.net/XML headers & schema ...
        <resheader name="resmimetype">text/microsoft-resx</resheader>
        <resheader name="version">2.0</resheader>
        <resheader name="reader">System.Resources.Extensions.ResXResourceReader, System.Resources.Extensions, ...</resheader>
        <resheader name="writer">System.Resources.Extensions.ResXResourceWriter, System.Resources.Extensions, ...</resheader>
        <data name="Name1" xml:space="preserve">
                <value>Value1</value>
        </data>
        <data name="Name2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.binary.base64">
                <value>[base64 encoded bitmap]</value>
        </data>
        <data name="Name3" mimetype="application/x-microsoft.net.object.binary.base64">
                <value>[base64 encoded string]</value>
        </data>
        <assembly alias="System.Resources.Extensions" name="System.Resources.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"            />
    -->
    <xsd:schema xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" />
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string" />
                            <xsd:attribute name="type" type="xsd:string" />
                            <xsd:attribute name="mimetype" type="xsd:string" />
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" use="required" type="xsd:string" />
                            <xsd:attribute name="name" use="required" type="xsd:string" />
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string" msdata:Ordinal="1" />
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" />
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string" />
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.Extensions.ResXResourceReader, System.Resources.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.Extensions.ResXResourceWriter, System.Resources.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </resheader>
  <data name="menu.product.category.name_can_not_be_null" xml:space="preserve">
    <value>Tên danh mục sản phẩm không được phép trống</value>
  </data>
  <data name="Test_Hello" xml:space="preserve">
    <value>Xin chào, Thế giới!</value>
  </data>
  <data name="Test_Greeting" xml:space="preserve">
    <value>Xin chào, {0}!</value>
  </data>
  <data name="man.cashflow.message.bank_account_exists" xml:space="preserve">
    <value>Tài khoản ngân hàng đã tồn tại</value>
  </data>
  <data name="man.cashflow.message.branch_ids_invalid" xml:space="preserve">
    <value>Một số ID chi nhánh không thuộc về nhà bán lẻ hiện tại</value>
  </data>
  <data name="man.cashflow.message.bank_account_create_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi tạo tài khoản ngân hàng</value>
  </data>
  <data name="man.cashflow.message.account_name_too_long" xml:space="preserve">
    <value>Vui lòng nhập tên tài khoản không quá 255 ký tự</value>
  </data>
  <data name="man.cashflow.message.account_required" xml:space="preserve">
    <value>Tài khoản không được để trống</value>
  </data>
  <data name="man.cashflow.message.bank_name_required" xml:space="preserve">
    <value>Tên ngân hàng không được để trống</value>
  </data>
  <data name="man.cashflow.message.bank_name_too_long" xml:space="preserve">
    <value>Vui lòng nhập tên ngân hàng không quá 255 ký tự</value>
  </data>
  <data name="man.cashflow.message.bank_code_required" xml:space="preserve">
    <value>Mã ngân hàng không được để trống</value>
  </data>
  <data name="man.cashflow.message.bank_code_too_long" xml:space="preserve">
    <value>Vui lòng nhập mã ngân hàng không quá 50 ký tự</value>
  </data>
  <data name="man.cashflow.message.branch_name_too_long" xml:space="preserve">
    <value>Vui lòng nhập tên chi nhánh không quá 255 ký tự</value>
  </data>
  <data name="man.cashflow.message.description_too_long" xml:space="preserve">
    <value>Vui lòng nhập mô tả không quá 255 ký tự</value>
  </data>
  <data name="man.cashflow.message.bank_account_not_found" xml:space="preserve">
    <value>Không tìm thấy tài khoản ngân hàng</value>
  </data>
  <data name="man.cashflow.message.bank_account_deleted" xml:space="preserve">
    <value>Tài khoản ngân hàng đã bị xóa</value>
  </data>
  <data name="man.cashflow.message.bank_account_delete_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi xóa tài khoản ngân hàng</value>
  </data>
  <data name="man.cashflow.message.bank_account_list_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi lấy danh sách tài khoản ngân hàng</value>
  </data>
  <data name="man.cashflow.message.bank_account_update_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi cập nhật tài khoản ngân hàng</value>
  </data>
  <data name="man.cashflow.message.account_type_required" xml:space="preserve">
    <value>Vui lòng nhập loại tài khoản</value>
  </data>
  <data name="man.cashflow.message.wallet_delete_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi xóa ví điện tử</value>
  </data>
  <data name="man.cashflow.message.wallet_not_found" xml:space="preserve">
    <value>Không tìm thấy ví điện tử với ID {request.Id}</value>
  </data>
  <data name="man.cashflow.message.wallet_list_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi lấy danh sách ví điện tử</value>
  </data>
  <data name="man.cashflow.message.wallet_account_exists" xml:space="preserve">
    <value>Số tài khoản {request.AccountNumber} đã tồn tại</value>
  </data>
  <data name="man.cashflow.message.wallet_update_failed" xml:space="preserve">
    <value>Đã xảy ra lỗi khi cập nhật ví điện tử</value>
  </data>
  <data name="man.product.validate.create.code.max_length_50" xml:space="preserve">
    <value>Mã hàng hóa không được vượt quá 50 ký tự</value>
  </data>
  <data name="man.product.validate.create.name.required" xml:space="preserve">
    <value>Tên hàng hóa là bắt buộc</value>
  </data>
  <data name="man.product.validate.create.name.max_length_255" xml:space="preserve">
    <value>Tên hàng hóa không được vượt quá 255 ký tự</value>
  </data>
  <data name="man.product.validate.create.category_id__more_than_zero" xml:space="preserve">
    <value>ID nhóm hàng hóa phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.create.description.max_length_2000" xml:space="preserve">
    <value>Mô tả không được vượt quá 2000 ký tự</value>
  </data>
  <data name="man.product.validate.create.base_price.min" xml:space="preserve">
    <value>Giá bán phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.tax_rate.range" xml:space="preserve">
    <value>Thuế suất phải nằm trong khoảng từ 0 đến 100</value>
  </data>
  <data name="man.product.validate.create.unit.max_length" xml:space="preserve">
    <value>Đơn vị tính không được vượt quá 50 ký tự</value>
  </data>
  <data name="man.product.validate.create.conversion_value.min" xml:space="preserve">
    <value>Giá trị quy đổi phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.create.min_quantity.min" xml:space="preserve">
    <value>Số lượng tối thiểu phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.max_quantity.min" xml:space="preserve">
    <value>Số lượng tối đa phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.max_quantity.gte_min_quantity" xml:space="preserve">
    <value>Số lượng tối đa phải lớn hơn hoặc bằng số lượng tối thiểu</value>
  </data>
  <data name="man.product.validate.create.cost.min" xml:space="preserve">
    <value>Giá vốn phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.on_hand_quantity.min" xml:space="preserve">
    <value>Tồn kho phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.pricebook_ids_more_than_zero" xml:space="preserve">
    <value>Tất cả ID bảng giá phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.create.pricebook_prices.min" xml:space="preserve">
    <value>Tất cả giá phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.create.image_urls.not_empty" xml:space="preserve">
    <value>Các đường dẫn hình ảnh không được để trống</value>
  </data>
  <data name="man.product.validate.create.product_type_id.positive_optional" xml:space="preserve">
    <value>ID loại hàng hóa phải lớn hơn 0 nếu được chỉ định</value>
  </data>
  <data name="man.product.validate.create.product_group_id.positive_optional" xml:space="preserve">
    <value>ID nhóm sản phẩm phải lớn hơn 0 nếu được chỉ định</value>
  </data>
  <data name="man.product.validate.create.product_group_id.invalid" xml:space="preserve">
    <value>ID nhóm sản phẩm không hợp lệ</value>
  </data>
  <data name="man.product.validate.create.tax_id.positive_optional" xml:space="preserve">
    <value>ID thuế phải lớn hơn 0 nếu được chỉ định</value>
  </data>
  <data name="man.product.validate.delete.product_id.required" xml:space="preserve">
    <value>ID sản phẩm là bắt buộc</value>
  </data>
  <data name="man.product.validate.delete.product_id__more_than_zero" xml:space="preserve">
    <value>ID sản phẩm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.general.search_key_required" xml:space="preserve">
    <value>Từ khóa tìm kiếm là bắt buộc</value>
  </data>
  <data name="man.product.validate.general.search_key_not_empty" xml:space="preserve">
    <value>Từ khóa tìm kiếm không được để trống</value>
  </data>
  <data name="man.product.validate.general.limit_more_than_zero" xml:space="preserve">
    <value>Giới hạn tìm kiếm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.delete.product_id_more_than_zero" xml:space="preserve">
    <value>ID sản phẩm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.general.search_key.not_empty" xml:space="preserve">
    <value>Từ khóa tìm kiếm không được để trống</value>
  </data>
  <data name="man.product.validate.general.limit.more_than_zero" xml:space="preserve">
    <value>Giới hạn tìm kiếm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.update.product_id.required" xml:space="preserve">
    <value>ID sản phẩm là bắt buộc</value>
  </data>
  <data name="man.product.validate.update.product_id_more_than_zero" xml:space="preserve">
    <value>ID sản phẩm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.update.code.required" xml:space="preserve">
    <value>Mã sản phẩm là bắt buộc</value>
  </data>
  <data name="man.product.validate.update.code.max_length" xml:space="preserve">
    <value>Mã sản phẩm phải nhỏ hơn 50 ký tự</value>
  </data>
  <data name="man.product.validate.update.name.required" xml:space="preserve">
    <value>Tên sản phẩm là bắt buộc</value>
  </data>
  <data name="man.product.validate.update.name.max_length" xml:space="preserve">
    <value>Tên sản phẩm phải nhỏ hơn 255 ký tự</value>
  </data>
  <data name="man.product.validate.update.category_id_more_than_zero" xml:space="preserve">
    <value>ID nhóm hàng phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.update.base_price.min" xml:space="preserve">
    <value>Giá bán phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.weight.min" xml:space="preserve">
    <value>Khối lượng phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.product_group_id.positive_optional" xml:space="preserve">
    <value>ID nhóm sản phẩm phải lớn hơn 0 nếu được chỉ định</value>
  </data>
  <data name="man.product.validate.update.product_group_id.invalid" xml:space="preserve">
    <value>ID nhóm sản phẩm không hợp lệ</value>
  </data>
  <data name="man.product.validate.update.tax_id_more_than_zero" xml:space="preserve">
    <value>ID thuế phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.update.order_template.max_length" xml:space="preserve">
    <value>Mẫu đơn hàng phải nhỏ hơn 500 ký tự</value>
  </data>
  <data name="man.product.validate.update.description.max_length" xml:space="preserve">
    <value>Mô tả phải nhỏ hơn 2000 ký tự</value>
  </data>
  <data name="man.product.validate.update.images.max_count" xml:space="preserve">
    <value>Số lượng hình ảnh phải nhỏ hơn 5</value>
  </data>
  <data name="man.product.validate.update.on_hand_quantity.min" xml:space="preserve">
    <value>Tồn kho thực tế phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.min_quantity.min" xml:space="preserve">
    <value>Số lượng tối thiểu phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.max_quantity.min" xml:space="preserve">
    <value>Số lượng tối đa phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.cost.min_on_hand" xml:space="preserve">
    <value>Giá vốn phải lớn hơn hoặc bằng 0</value>
  </data>
  <data name="man.product.validate.update.pricebooks.required" xml:space="preserve">
    <value>Danh sách bảng giá là bắt buộc</value>
  </data>
  <data name="man.product.validate.update.pricebooks.min_items" xml:space="preserve">
    <value>Phải cung cấp ít nhất một bảng giá</value>
  </data>
  <data name="man.product.validate.update.combo_groups.min_items" xml:space="preserve">
    <value>Phải có ít nhất một nhóm combo</value>
  </data>
  <data name="man.product.validate.update.combo_group.name.required" xml:space="preserve">
    <value>Mỗi nhóm combo phải có tên</value>
  </data>
  <data name="man.product.validate.update.combo_group.name.max_length" xml:space="preserve">
    <value>Tên nhóm combo phải nhỏ hơn 100 ký tự</value>
  </data>
  <data name="man.product.validate.update.combo_group.items.min_items" xml:space="preserve">
    <value>Mỗi nhóm combo phải có ít nhất một món</value>
  </data>
  <data name="man.product.validate.update.sale_branch.product_id.positive" xml:space="preserve">
    <value>ID sản phẩm phải lớn hơn 0</value>
  </data>
  <data name="man.product.validate.update.toppings.required" xml:space="preserve">
    <value>Danh sách ID topping là bắt buộc</value>
  </data>
  <data name="man.product.validate.update.toppings.min_items" xml:space="preserve">
    <value>Phải cung cấp ít nhất một ID sản phẩm topping</value>
  </data>
  <data name="man.product.validate.update.request.not_empty" xml:space="preserve">
    <value>Yêu cầu không được để trống</value>
  </data>
  <data name="man.product.validate.update.request.has_update_item" xml:space="preserve">
    <value>Phải có ít nhất một thành phần để cập nhật</value>
  </data>
  <data name="man.product.msg.create.validation_failed" xml:space="preserve">
    <value>Xác thực thất bại</value>
  </data>
  <data name="man.product.msg.create.category_not_found" xml:space="preserve">
    <value>Không tìm thấy nhóm hàng hóa</value>
  </data>
  <data name="man.product.msg.create.create_failed" xml:space="preserve">
    <value>Tạo sản phẩm thất bại</value>
  </data>
  <data name="man.product.msg.create.combo_groups_required" xml:space="preserve">
    <value>Các nhóm combo tùy chọn là bắt buộc đối với sản phẩm combo tùy chọn</value>
  </data>
  <data name="man.product.msg.create.combo_validation_failed" xml:space="preserve">
    <value>Xác thực sản phẩm combo tùy chọn thất bại</value>
  </data>
  <data name="man.product.msg.create.missing_product_ids" xml:space="preserve">
    <value>Không tìm thấy ID sản phẩm nào trong các nhóm combo tùy chọn</value>
  </data>
  <data name="man.product.msg.create.product_missing" xml:space="preserve">
    <value>Sản phẩm với ID {} không tồn tại</value>
  </data>
  <data name="man.product.msg.create.product_deleted" xml:space="preserve">
    <value>Sản phẩm với ID {} đã bị xóa</value>
  </data>
  <data name="man.product.msg.create.combo_mark_missing" xml:space="preserve">
    <value>Sản phẩm với ID {} là combo sản phẩm nhưng không được đánh dấu là sản phẩm chế biến</value>
  </data>
  <data name="man.product.msg.create.service_invalid_combo" xml:space="preserve">
    <value>Sản phẩm với ID {} là dịch vụ tính theo thời gian nên không thể dùng trong combo tùy chọn</value>
  </data>
  <data name="man.product.msg.create.invalid_product_type" xml:space="preserve">
    <value>Sản phẩm với ID {} có loại sản phẩm không hợp lệ. Chỉ cho phép sản phẩm thường, combo chế biến, và dịch vụ không tính thời gian</value>
  </data>
  <data name="man.product.msg.delete.product_not_found" xml:space="preserve">
    <value>Không tìm thấy sản phẩm với ID {}</value>
  </data>
  <data name="man.product.msg.delete.product_deleted" xml:space="preserve">
    <value>Sản phẩm đã bị xóa</value>
  </data>
  <data name="man.product.msg.general.product_detail_not_found" xml:space="preserve">
    <value>Không tìm thấy sản phẩm với ID {}</value>
  </data>
  <data name="man.product.msg.general.internal_server_error" xml:space="preserve">
    <value>Lỗi máy chủ nội bộ</value>
  </data>
  <data name="man.product.msg.update.product_not_found" xml:space="preserve">
    <value>Không tìm thấy sản phẩm</value>
  </data>
  <data name="man.product.msg.update.code_exists" xml:space="preserve">
    <value>Mã sản phẩm đã tồn tại</value>
  </data>
  <data name="man.product.msg.update.category_not_found" xml:space="preserve">
    <value>Không tìm thấy nhóm hàng hóa</value>
  </data>
  <data name="man.product.msg.update.tax_not_found" xml:space="preserve">
    <value>Không tìm thấy thuế</value>
  </data>
  <data name="man.product.msg.update.product_not_found_images" xml:space="preserve">
    <value>Không tìm thấy sản phẩm</value>
  </data>
  <data name="man.product.msg.update.branch_not_found" xml:space="preserve">
    <value>Không tìm thấy chi nhánh</value>
  </data>
  <data name="man.product.msg.update.product_not_found_quantity" xml:space="preserve">
    <value>Không tìm thấy sản phẩm</value>
  </data>
  <data name="man.product.msg.update.deleted_product_no_update" xml:space="preserve">
    <value>Không thể cập nhật tồn kho cho sản phẩm đã bị xóa</value>
  </data>
  <data name="man.product.msg.update.service_combo_no_inventory_update" xml:space="preserve">
    <value>Không thể cập nhật theo dõi tồn kho và tồn kho thực tế cho dịch vụ và combo tùy chọn</value>
  </data>
  <data name="man.product.msg.update.no_tracking_no_inventory_update" xml:space="preserve">
    <value>Không thể cập nhật tồn kho thực tế cho sản phẩm không theo dõi tồn kho</value>
  </data>
  <data name="man.product.msg.update.avg_cost_no_update" xml:space="preserve">
    <value>Không thể cập nhật giá vốn cho sản phẩm có giá vốn trung bình</value>
  </data>
  <data name="man.product.msg.update.product_not_found_pricebooks" xml:space="preserve">
    <value>Không tìm thấy sản phẩm</value>
  </data>
  <data name="man.product.msg.update.product_deleted_pricebooks" xml:space="preserve">
    <value>Sản phẩm đã bị xóa</value>
  </data>
  <data name="man.product.msg.update.pricebooks_not_found" xml:space="preserve">
    <value>Không tìm thấy một số bảng giá: {0}</value>
  </data>
  <data name="man.product.msg.update.pricebooks_deleted" xml:space="preserve">
    <value>Một số bảng giá đã bị xóa: {0}</value>
  </data>
  <data name="man.product.msg.update.pricebooks_inactive" xml:space="preserve">
    <value>Một số bảng giá đang không hoạt động: {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_sale_branches" xml:space="preserve">
    <value>Không tìm thấy sản phẩm với ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_combo" xml:space="preserve">
    <value>Không tìm thấy sản phẩm với ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_combo" xml:space="preserve">
    <value>Sản phẩm với ID {0} không phải là sản phẩm combo tùy chọn</value>
  </data>
  <data name="man.product.msg.update.product_not_in_tenant" xml:space="preserve">
    <value>Sản phẩm với ID {0} không thuộc quyền quản lý của tenant hiện tại</value>
  </data>
  <data name="man.product.msg.update.combo_groups_required_update" xml:space="preserve">
    <value>Phải có ít nhất một nhóm combo</value>
  </data>
  <data name="man.product.msg.update.combo_group_not_found" xml:space="preserve">
    <value>Không tìm thấy nhóm combo với ID {0}</value>
  </data>
  <data name="man.product.msg.update.combo_item_not_found" xml:space="preserve">
    <value>Không tìm thấy món trong nhóm combo với ID {0}</value>
  </data>
  <data name="man.product.msg.update.product_not_found_toppings" xml:space="preserve">
    <value>Không tìm thấy sản phẩm</value>
  </data>
  <data name="man.product.msg.update.topping_product_not_found" xml:space="preserve">
    <value>Không tìm thấy sản phẩm topping với ID {0}</value>
  </data>
  <data name="man.product.msg.update.invalid_topping" xml:space="preserve">
    <value>Sản phẩm với ID {0} không được đánh dấu là topping</value>
  </data>
  <data name="man.product_note.add.message.group_name_exists" xml:space="preserve">
    <value>Tên nhóm ghi chú đã tồn tại</value>
  </data>
  <data name="man.product_note.delete.message.group_id_min_value" xml:space="preserve">
    <value>ID nhóm ghi chú phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.add.message.empty_group_name" xml:space="preserve">
    <value>Tên nhóm ghi chú không được bỏ trống</value>
  </data>
  <data name="man.product_note.add.message.group_name_max_chars" xml:space="preserve">
    <value>Tên nhóm ghi chú không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.add.message.invalid_branch" xml:space="preserve">
    <value>Chi nhánh không hợp lệ</value>
  </data>
  <data name="man.product_note.add.message.note_group_id_min_value" xml:space="preserve">
    <value>ID nhóm ghi chú phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.delete.message.group_not_found" xml:space="preserve">
    <value>Nhóm ghi chú với ID {0} không được tìm thấy</value>
  </data>
  <data name="man.product_note.update.message.group_id_min_value" xml:space="preserve">
    <value>ID nhóm ghi chú phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.update.message.empty_group_name" xml:space="preserve">
    <value>Tên nhóm ghi chú không được bỏ trống</value>
  </data>
  <data name="man.product_note.update.message.group_name_max_chars" xml:space="preserve">
    <value>Tên nhóm ghi chú không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.add.message.empty_note_name" xml:space="preserve">
    <value>Tên ghi chú mẫu không được bỏ trống</value>
  </data>
  <data name="man.product_note.add.message.note_name_max_chars" xml:space="preserve">
    <value>Tên ghi chú mẫu không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.add.message.note_content_max_chars" xml:space="preserve">
    <value>Mô tả ghi chú mẫu không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.add.message.note_invalid_branch" xml:space="preserve">
    <value>Chi nhánh không hợp lệ</value>
  </data>
  <data name="man.product_note.delete.message.note_id_min_value" xml:space="preserve">
    <value>ID ghi chú món phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.add.message.note_name_exists" xml:space="preserve">
    <value>Tên ghi chú món đã tồn tại</value>
  </data>
  <data name="man.product_note.add.message.note_group_not_exists" xml:space="preserve">
    <value>Nhóm ghi chú không tồn tại</value>
  </data>
  <data name="man.product_note.add.message.product_not_found" xml:space="preserve">
    <value>Hàng hoá với ID {0} không được tìm thấy</value>
  </data>
  <data name="man.product_note.delete.message.note_not_found" xml:space="preserve">
    <value>Ghi chú món với ID {0} không được tìm thấy</value>
  </data>
  <data name="man.product_note.update.message.note_group_id_min_value" xml:space="preserve">
    <value>ID nhóm ghi chú phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.update.message.product_id_min_value" xml:space="preserve">
    <value>Tất cả ID của hàng hoá phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.delete.message.note_already_deleted" xml:space="preserve">
    <value>Ghi chú món với ID {0} đã bị xoá</value>
  </data>
  <data name="man.product_note.update.message.note_id_min_value" xml:space="preserve">
    <value>ID ghi chú món phải lớn hơn 0</value>
  </data>
  <data name="man.product_note.update.message.empty_note_name" xml:space="preserve">
    <value>Tên ghi chú món không được bỏ trống</value>
  </data>
  <data name="man.product_note.update.message.note_name_max_chars" xml:space="preserve">
    <value>Tên ghi chú món không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.update.message.note_description_max_chars" xml:space="preserve">
    <value>Mô tả ghi chú món không được quá {0} kí tự</value>
  </data>
  <data name="man.product_note.update.message.note_name_exists" xml:space="preserve">
    <value>Tên ghi chú món đã tồn tại</value>
  </data>
  <data name="man.product_note.update.message.product_not_found" xml:space="preserve">
    <value>Hàng hoá với ID {0} không được tìm thấy</value>
  </data>
  <data name="" xml:space="preserve">
    <value />
  </data>
  <data name="man.delete_data.message_error.duplicate_date" xml:space="preserve">
    <value>Lịch xóa ngày {0} đã tồn tại</value>
  </data>
  <data name="man.product_note.add.message.note_group_required" xml:space="preserve">
    <value>Vui lòng chọn nhóm ghi chú</value>
  </data>
</root>