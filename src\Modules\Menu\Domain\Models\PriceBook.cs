﻿using KvFnB.Core.Domain;
using KvFnB.Core.Exceptions;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.ValueObjects;

namespace KvFnB.Modules.Menu.Domain.Models
{
    public class PriceBook : AggregateRoot<long>, IAuditableEntity, ISoftDeletable
    {
        public string Name { get; private set; }
        public bool IsActive { get; private set; }
        public bool IsGlobal { get; private set; }
        public string? CustomTime { get; private set; }
        public DateTime StartDate { get; private set; }
        public DateTime EndDate { get; private set; }
        public bool ForAllUser { get; private set; }
        public bool ForAllCustomerGroup { get; private set; }
        public bool? ForTakeAwayTable { get; private set; }
        public bool ForAllTableAndRoom { get; private set; }
        public string? Type { get; private set; }
        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public long? ModifiedBy { get; set; }
        public bool? IsDeleted { get; set; }
        private readonly List<PriceBookDetail> _priceBookDetails = [];
        public IReadOnlyList<PriceBookDetail> PriceBookDetails => _priceBookDetails;

        private readonly List<PriceBookBranch> _priceBookBranches = [];
        public IReadOnlyList<PriceBookBranch> PriceBookBranches => _priceBookBranches;

        private readonly List<PriceBookCustomerGroup> _priceBookCustomerGroups = [];
        public IReadOnlyList<PriceBookCustomerGroup> PriceBookCustomerGroups => _priceBookCustomerGroups;

        private readonly List<PriceBookTableAndRoom> _priceBookTableAndRooms = [];
        public IReadOnlyList<PriceBookTableAndRoom> PriceBookTableAndRooms => _priceBookTableAndRooms;

        private readonly List<PriceBookGroup> _priceBookGroups = [];
        public IReadOnlyList<PriceBookGroup> PriceBookGroups => _priceBookGroups;

        private readonly List<PriceBookDiningOption> _priceBookDiningOptions = [];
        public IReadOnlyList<PriceBookDiningOption> PriceBookDiningOptions => _priceBookDiningOptions;

        private readonly List<PriceBookUser> _priceBookUsers = [];
        public IReadOnlyList<PriceBookUser> PriceBookUsers => _priceBookUsers;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        public PriceBook()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        {
        }

        public static PriceBook Create(CreatePriceBookModel createPriceBookModel)
        {
            if (string.IsNullOrWhiteSpace(createPriceBookModel.Name))
            {
                throw new DomainException("Name is required.");
            }

            if (createPriceBookModel.StartDate >= createPriceBookModel.EndDate)
                throw new ArgumentException("Start date must be before end date", nameof(createPriceBookModel.StartDate));

            return new PriceBook
            {
                Name = createPriceBookModel.Name,
                IsActive = true,
                IsGlobal = createPriceBookModel.IsGlobal,
                CustomTime = createPriceBookModel.CustomTime,
                StartDate = createPriceBookModel.StartDate,
                EndDate = createPriceBookModel.EndDate,
                ForAllUser = createPriceBookModel.ForAllUser,
                ForAllCustomerGroup = createPriceBookModel.ForAllCustomerGroup,
                ForAllTableAndRoom = createPriceBookModel.ForAllTableAndRoom,
                ForTakeAwayTable = createPriceBookModel.ForTakeAwayTable,
                Type = createPriceBookModel.Type
            };
        }

        public void Update(UpdatePriceBookModel updatePriceBookModel)
        {
            if (string.IsNullOrWhiteSpace(updatePriceBookModel.Name))
                throw new ArgumentException("Name cannot be empty", nameof(updatePriceBookModel.Name));

            if (updatePriceBookModel.StartDate >= updatePriceBookModel.EndDate)
                throw new ArgumentException("Start date must be before end date", nameof(updatePriceBookModel.StartDate));

            Name = updatePriceBookModel.Name;
            StartDate = updatePriceBookModel.StartDate;
            EndDate = updatePriceBookModel.EndDate;
            IsGlobal = updatePriceBookModel.IsGlobal;
            ForAllUser = updatePriceBookModel.ForAllUser;
            ForAllCustomerGroup = updatePriceBookModel.ForAllCustomerGroup;
            ForAllTableAndRoom = updatePriceBookModel.ForAllTableAndRoom;
            ForTakeAwayTable = updatePriceBookModel.ForTakeAwayTable;
            CustomTime = updatePriceBookModel.CustomTime;
            Type = updatePriceBookModel.Type;
        }

        public void UpdateBasicInfo(
            string name,
            DateTime startDate,
            DateTime endDate)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            if (startDate >= endDate)
                throw new ArgumentException("Start date must be before end date", nameof(startDate));

            Name = name;
            StartDate = startDate;
            EndDate = endDate;
        }

        public void UpdateCustomTime(string? customTime)
        {
            CustomTime = customTime;
        }

        public void Activate() => IsActive = true;

        public void Deactivate() => IsActive = false;

        public void Delete() => IsDeleted = true;

        public void AddPriceBookBranch(int branchId)
        {
            var priceBookBranch = PriceBookBranch.Create(branchId);
            _priceBookBranches.Add(priceBookBranch);
        }

        public void AddPriceBookCustomerGroup(int customerGroupId)
        {
            var priceBookCustomerGroup = PriceBookCustomerGroup.Create(customerGroupId);
            _priceBookCustomerGroups.Add(priceBookCustomerGroup);
        }

        public void AddPriceBookTableAndRoom(int tableAndRoomId)
        {
            var priceBookTableAndRoom = PriceBookTableAndRoom.Create(tableAndRoomId);
            _priceBookTableAndRooms.Add(priceBookTableAndRoom);
        }

        public void AddPriceBookGroup(long groupId, bool status, int type)
        {
            var priceBookGroup = PriceBookGroup.Create(groupId, status, type);
            _priceBookGroups.Add(priceBookGroup);
        }

        public void AddPriceBookDiningOption(byte diningOption, int branchId, int tenantId)
        {
            var priceBookDiningOption = PriceBookDiningOption.Create(diningOption, branchId, tenantId);
            _priceBookDiningOptions.Add(priceBookDiningOption);
        }

        public void AddPriceBookUser(long userId)
        {
            var priceBookUser = PriceBookUser.Create(userId);
            _priceBookUsers.Add(priceBookUser);
        }

        public void AddProductPrice(long productId, decimal price)
        {
            if (_priceBookDetails.Any(p => p.ProductId == productId))
                throw new InvalidOperationException($"Product {productId} already has a price in this price book.");

            var priceBookDetail = PriceBookDetail.Create(Id, productId, price);
            _priceBookDetails.Add(priceBookDetail);
        }

        public void UpdateProductPrice(long productId, decimal price)
        {
            var existingDetail = _priceBookDetails.FirstOrDefault(p => p.ProductId == productId);
            if (existingDetail == null)
                throw new InvalidOperationException($"Product {productId} does not have a price in this price book.");

            existingDetail.UpdatePrice(price);
        }

        public void RemoveProductPrice(long productId)
        {
            var existingDetail = _priceBookDetails.FirstOrDefault(p => p.ProductId == productId);
            if (existingDetail == null)
                return;

            _priceBookDetails.Remove(existingDetail);
        }

        public bool HasProductPrice(long productId)
        {
            return _priceBookDetails.Any(p => p.ProductId == productId);
        }

        public PriceBookDetail? GetProductPrice(long productId)
        {
            return _priceBookDetails.FirstOrDefault(p => p.ProductId == productId);
        }
    }

    public class CreatePriceBookModel
    {
        public string Name { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsGlobal { get; set; }
        public string? CustomTime { get; set; }
        public bool ForAllUser { get; set; }
        public bool ForAllCustomerGroup { get; set; }
        public bool ForAllTableAndRoom { get; set; }
        public bool ForTakeAwayTable { get; set; }
        public string? Type { get; set; }
    }

    public class UpdatePriceBookModel
    {
        public string Name { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsGlobal { get; set; }
        public string? CustomTime { get; set; }
        public bool ForAllUser { get; set; }
        public bool ForAllCustomerGroup { get; set; }
        public bool ForAllTableAndRoom { get; set; }
        public bool ForTakeAwayTable { get; set; }
        public string? Type { get; set; }
    }
}
