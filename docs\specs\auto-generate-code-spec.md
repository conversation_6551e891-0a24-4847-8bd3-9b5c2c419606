# Document 1: Technical Specification Document – Auto-Generated Code Service

---

## 1. Overview

This document specifies a robust, high-performance module for auto-generating sequential codes for entities (such as Products, Invoices, Customers) within a multi-merchant SaaS POS system. Each code consists of a fixed prefix and a numeric part that is padded to a configurable minimum length but automatically expands if the numeric value exceeds that length. The module supports custom code inputs, legacy data migration, and batch code generation. It ensures atomicity by wrapping both code generation and entity creation in a single transaction to prevent gaps.

---

## 2. Requirements

- **Multi-Tenant & Entity Specific:**  
  Unique sequential code sequences per merchant (RetailerId) and entity type (TypeOf) based on a designated prefix (e.g., "SP" for Products).

- **Sequential Integrity:**  
  Codes are generated sequentially in an incremental manner.

- **Numeric Padding and Automatic Expansion:**  
  The numeric part is padded to a configured minimum length (e.g., 6 digits as "000001"). When the value exceeds the padding (e.g., from 999999 to 1000000), the numeric portion automatically expands.

- **Custom Code Support:**  
  Allows users to provide a custom code. If the numeric portion of the custom code is greater than the next sequential value, the sequence is adjusted accordingly.

- **Atomic Transactions:**  
  Both the code generation and the subsequent entity creation must occur within the same transaction. If entity saving fails, the sequence update is rolled back, thereby preventing gaps.

- **High Concurrency:**  
  The system must support heavy load conditions (e.g., hundreds of invoices per merchant during peak periods) using optimistic concurrency control (via a rowversion/timestamp column) and a configurable retry mechanism with exponential backoff.

- **Legacy Data Migration:**  
  On first use, if no sequence record exists, the service must query the legacy data to determine the current maximum code and initialize the sequence accordingly.

- **Batch Code Generation:**  
  Provides the ability to generate a batch of sequential codes atomically, which is useful for bulk operations, while maintaining sequence integrity.

- **Error Handling and Logging:**  
  Detailed logging and error handling are implemented, with configurable parameters for maximum retries and delay intervals.

---

## 3. System Architecture

### 3.1 Hexagonal Architecture

- **Core Domain:**  
  Contains business logic for code generation, including rules for sequential numbering, dynamic padding, custom code handling, and legacy migration.

- **Ports:**  
  Define the interfaces (e.g., ICodeGenerationService) that expose the code generation functionality to the application layer.

- **Adapters:**  
  - **Persistence Adapter:**  
    Implements the domain interfaces using EF Core for SQL Server, managing transactions, concurrency, and data persistence.
  - **Application Service Adapter:**  
    Orchestrates use cases by calling the domain service, without being aware of persistence details.

### 3.2 Transaction Management

- All operations (code generation, legacy migration, entity creation, batch update) are wrapped in a single EF Core transaction to ensure atomicity. In the event of an error, the entire transaction is rolled back.

- **Retry Strategy:**  
  Uses optimistic concurrency with a configurable retry count (e.g., 3 retries) and exponential backoff delays to handle conflicts.

---

## 4. Data Model & Schema Enhancements

### 4.1 AutoGeneratedCode Table

```sql
CREATE TABLE [dbo].[AutoGeneratedCode](
    [RetailerId] [int] NOT NULL,
    [TypeOf] [nvarchar](64) NOT NULL,
    [Prefix] [nvarchar](64) NOT NULL,
    [Value] [bigint] NOT NULL,
    [RowVersion] [rowversion] NOT NULL, -- For optimistic concurrency control
 CONSTRAINT [PK_AutoGeneratedCode] PRIMARY KEY CLUSTERED 
(
    [RetailerId] ASC,
    [TypeOf] ASC,
    [Prefix] ASC
) ON [PRIMARY]
) ON [PRIMARY]
GO
```

- **RetailerId:** Merchant identifier.
- **TypeOf:** Entity type (e.g., "Product", "Invoice").
- **Prefix:** Fixed code prefix.
- **Value:** Current numeric sequence.
- **RowVersion:** Timestamp for optimistic concurrency.

---

## 5. Code Generation Logic

### 5.1 Process Flow

1. **Begin Transaction:**  
   Start an EF Core transaction that wraps the entire process.

2. **Retrieve/Initialize Sequence Record:**  
   - Look up the `AutoGeneratedCode` record based on RetailerId, TypeOf, and Prefix.
   - If not found, perform legacy migration to set the initial sequence value or initialize with zero.

3. **Determine Next Sequence Value:**  
   - Increment the current sequence value by one.
   - If a custom code is provided and its numeric part is higher, use the custom numeric value.

4. **Dynamic Padding Expansion:**  
   Generate the code using .NET formatting (`ToString("D{configuredPadding}")`). This ensures a minimum width, but expands automatically if the numeric value exceeds the configured width.

5. **Persist Sequence Update and Create Entity:**  
   - Update the sequence record.
   - Create the entity with the generated code.
   - Commit the transaction to ensure atomicity.

6. **Error Handling & Retry:**  
   On a concurrency conflict, the transaction is rolled back, and the process is retried with an exponential backoff up to a maximum retry limit.

---

## 6. Legacy Data Migration

- **Purpose:**  
  Migrate existing codes from the legacy system. When no sequence record exists, query the legacy table to get the highest existing code for the given prefix, extract the numeric part, and initialize the sequence as one higher than the maximum found.

- **Process:**  
  - Query legacy tables using dynamic criteria.
  - Extract the numeric part.
  - Insert a new record into the `AutoGeneratedCode` table with the computed value.

---

## 7. Batch Code Generation

### 7.1 Requirements

- **Efficiency:**  
  Generate a batch of sequential codes in one operation to minimize database calls.

- **Atomicity:**  
  The entire batch generation occurs in a single transaction.

- **Sequence Integrity:**  
  The batch must reflect consecutive sequence values reserved atomically.

### 7.2 Process Flow

1. Retrieve the current sequence record.
2. Determine the batch start and end values by incrementing the sequence by the requested batch size.
3. Update the sequence record with the new ending value.
4. Generate and return the list of codes formatted using dynamic padding.

---

## 8. Transaction Atomicity and Gap Handling

- **Atomic Operation:**  
  The code generation and entity creation are wrapped in a single transaction. If entity saving fails, the sequence update is rolled back, ensuring no gaps occur.

- **Potential Gaps:**  
  Minor gaps might occur only in scenarios where external processing post-commit fails. For strictly gapless sequences, a more complex mechanism (like a two-phase commit) could be considered, but this adds overhead.

---

## 9. Entity Code Update Handling

- **Use Case:**  
  In some cases, entities may need to have their codes updated after creation, particularly in migration scenarios or when correcting data.

- **Process Flow:**  
  1. Begin a single database transaction that wraps the entire operation.
  2. Validate that the new code follows the required prefix format.
  3. Check for uniqueness to ensure no other entity uses the same code.
  4. If the numeric part of the new code is higher than the current sequence, update the sequence accordingly.
  5. Update the entity's code.
  6. Commit the transaction to ensure atomicity.

- **Sequence Synchronization:**  
  When updating a code to a higher value than the current sequence, the sequence is automatically updated to match the new value, ensuring future codes remain higher than all existing codes.

- **Uniqueness Validation:**  
  The system validates that the new code is not already used by another entity before allowing the update.

- **Atomicity:**  
  Code updates and sequence updates occur within a single transaction to maintain data integrity. If any part of the process fails, the entire operation is rolled back.

- **Prefix Consistency:**  
  The new code must use the same prefix as specified for the entity type.

- **Concurrency Handling:**  
  Just like with code generation, the update process uses optimistic concurrency with retry logic and exponential backoff to handle potential conflicts.

---

## 10. Concurrency & Error Handling

- **Optimistic Concurrency:**  
  Utilize the `RowVersion` column to detect concurrent updates.
  
- **Retry Mechanism:**  
  A configurable maximum retry count with exponential backoff is used to resolve conflicts.

- **Logging:**  
  Detailed error logging is implemented for troubleshooting and system monitoring.

---

## 11. Scalability & Performance Considerations

- **Indexing:**  
  Ensure the primary key on `(RetailerId, TypeOf, Prefix)` is optimized for lookups.
  
- **Horizontal Scalability:**  
  The service is designed as a stateless microservice, allowing horizontal scaling.
  
- **Monitoring:**  
  Continuously monitor concurrency exceptions and performance metrics, and adjust configurations as needed.

---

## 12. Conclusion

This technical specification outlines a complete solution for auto-generating sequential codes in a multi-merchant SaaS POS system. It covers:
- Atomic integration of code generation and entity creation.
- Dynamic numeric padding with automatic expansion.
- Legacy data migration.
- Batch code generation.
- High concurrency handling via optimistic locking and configurable retries.
