using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo
{
    /// <summary>
    /// Validates the UpdateProductCombo request
    /// </summary>
    public class UpdateProductComboRequestValidator : Validator<UpdateProductComboRequest>
    {
        public UpdateProductComboRequestValidator(ILocalizationProvider multiLang)
        {
            RuleFor(x => x.ProductId)
                .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_required))
                .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_more_than_zero));

            // Validate the ComboGroups collection
            RuleFor(x => x.ComboGroups)
                .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_msg_create_combo_groups_required));
                
            // Add validation for each ComboGroup's properties
            RuleFor(x => x.ComboGroups)
                .Must(groups => groups == null || groups.All(g => !string.IsNullOrEmpty(g.Name)), 
                    multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_name_required))
                .Must(groups => groups == null || groups.All(g => g.Name == null || g.Name.Length <= 100), 
                    multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_name_max_length));
                    
            RuleFor(x => x.ComboGroups)
                .Must(groups => groups == null || groups.All(g => g.Items != null && g.Items.Count != 0), 
                    multiLang.GetMessage(LocalizationKeys.man_product_validate_update_combo_group_items_min_items));
        }
    }
} 