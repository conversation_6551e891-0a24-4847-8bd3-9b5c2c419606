---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: Project Overview Rule
globs: 
alwaysApply: true
---
# Project Overview:

You are an expert in C#, .NET Core, ASP.NET Core, EntityFrameworkCore, Dapper, StackExchangeRedis, MongoDB, SQLServer, Serilog, Software Design
Software Architecture Design (Especially on Hexagonal Architecture), Design Pattern and you are always adhere on SOLID, DRY, Clean code and DDD

Use C# as the programming language and .NET Core 8 as the framework.

This is a .NET Core 8 Core project and following the custom hexagonal architecture pattern with structure below.

Principles:

- Provide concise and technical responses.

- Avoid code repetition; write modular, reusable, and maintainable code.

- Follow object-oriented programming (OOP) and SOLID principles.

- Use meaningful and descriptive variable and method names.

- Enable strict mode in all code.

Coding Standards:

- Use C# 12 features.

- Use async/await for all asynchronous operations.

- Use dependency injection for all services and repositories.

- Follow standard .NET naming conventions:

  - Classes: PascalCase

  - Methods: PascalCase

  - Variables: camelCase

- All public classes and methods should have XML comments.

- Avoid magic numbers; use constants or enums.

- Prefer interfaces over concrete classes for dependencies.

- Interfaces should be prefixed with an "I" (e.g., IRepository).

- Async methods should have an "Async" suffix (e.g., GetDataAsync).

- Constants should be in all uppercase with underscores (e.g., MAX_ITEMS).

Project Structure:

- **src/**: Contains the main source code of the project.
  - **Core/**: Contains shared logic across modules.
    - **Domain/**: Contains shared domain concepts crucial to the overall business logic. This folder houses base entities, aggregate roots, domain events, and domain-specific exceptions that enforce business rules and invariants. It also includes cross-cutting concerns such as multi-tenancy support through tenant providers and any common domain logic shared across different modules.
    - **Abstractions/**: Contains common interfaces and contract definitions that decouple the core business logic from concrete implementations. This folder defines standardized contracts for aspects such as caching, logging, messaging, database access, authentication, and configuration. By centralizing these abstractions, the architecture adheres to the dependency inversion principle, which simplifies testing and maintenance while enabling flexible integration with various infrastructure components.
    - **Authentication**: Contains shared authentication logic and models used throughout the application. This folder includes abstractions and concrete implementations for various authentication schemes, token handling, and user identity management. For example, it houses models such as [`KvFnB.Core.Authentication.Models.PasswordAuthRequest`](mdc:src/Core/Authentication/Models/PasswordAuthRequest.cs) and [`KvFnB.Core.Authentication.Models.ClientCredentials`](mdc:src/Core/Authentication/Models/ClientCredentials.cs).
    - **Validation**: Provides a suite of reusable validators and extension methods to enforce business rules across the system. This folder centralizes validation logic for domain entities, use-case requests, and commands—ensuring consistency across modules. Validators like [`KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.UpdateProductCategory.UpdateProductCategoryValidator`](mdc:src/Modules/Menu/Application/UseCases/CategoryUseCase/UpdateProductCategory/UpdateProductCategoryValidator.cs) exemplify this approach.
    - **Utilities**: Contains helper classes and utility functions shared by various components of the project. This includes string manipulation, date/time operations, logging helpers, and other general-purpose utilities that facilitate common tasks across different layers.
    - **Exceptions/**: Contains common and shared exceptions.
  - **Modules/**: Contains distinct functional modules such as Orders, Customers, Payments, Menu, Inventory.
    - Each module has subdirectories:
      - **Domain/**: Contains domain logic (entities, aggregates, value objects).
      - **Application/**: Contains use cases and input/output ports (interfaces).
      - **Infrastructure/**: Contains specific adapters for the module.
      - **Restful/**: Contains specific presentation components for the module (controllers, endpoints).
  - **Shared/**: Contains shared infrastructure components that support cross-cutting concerns and integration points used by multiple parts of the application. This folder includes reusable implementations that interact with external systems (e.g., caching, messaging, logging, etc.) and help enforce consistent behavior across the project.
    - **AuditTrail**: Provides components and services to log critical system events and user activities. These audit logs are vital for ensuring traceability and compliance in the application.
    - **Authentication**: Contains shared authentication mechanisms and token handling utilities used across different modules. This module standardizes authentication protocols and simplifies user identity management.
    - **Caching**: Implements various caching strategies to improve performance and scalability. This includes Redis or other caching provider implementations that facilitate efficient data retrieval and storage.
    - **DependencyInjection**: Offers helper classes and extension methods to register and configure shared services into the dependency injection container. This module streamlines the bootstrapping of common dependencies across the solution.
    - **DistributedLock**: Provides mechanisms for managing distributed locks to control concurrent access to shared resources in a distributed environment. This is essential for ensuring consistency when multiple processes access a single resource.
    - **Filter**: Contains reusable filters and interceptors that enforce cross-cutting concerns like validation, logging, and error handling. These filters can be applied to incoming requests or outgoing responses to maintain consistent behaviors.
    - **Logging**: Includes logging adapters and configurations integrating with logging frameworks (e.g., Serilog, Microsoft.Extensions.Logging). This module centralizes the logging infrastructure, ensuring consistent log formatting and management.
    - **Mapping**: Provides utilities for object-to-object mapping, typically using tools like AutoMapper. It helps in transforming domain models to Data Transfer Objects (DTOs) or view models, reducing boilerplate code.
    - **MessageQueueProvider**: Implements provider classes for interacting with message queues and publish/subscribe systems (for example, using Redis as shown in [`RedisMessageQueueProvider`](mdc:src/Shared/MessageQueueProvider/RedisMessageQueueProvider.cs)). This module enables asynchronous communication and event-driven architectures.
    - **Middlewares**: Contains shared middleware components for handling HTTP requests and responses, such as authentication, error handling, and logging. These components help maintain a consistent processing pipeline across different endpoints.
    - **MultiCurrency**: This component handles currency value rounding based on a defined currency context. It implements operations for both decimal and double types by utilizing the KvFnBMoney library. It Employing a rounding algorithm specified in the currency context, providing flexibility with custom rounding functions through delegate parameters.
    - **MultiTenancy**: Provides support for multi-tenant scenarios by including shared services and configurations that handle tenant identification, isolation, and custom configurations for different clients.
    - **Persistence**: Houses shared data access abstractions and repository implementations that are used across multiple modules. This module simplifies data storage and retrieval operations in a consistent manner.
    - **StackExchangeRedis**: Offers integration components built on the StackExchange.Redis library. This includes advanced features such as caching, pub/sub messaging, and distributed locking tailored to work seamlessly with Redis.
  - **Presentation/**: Presentation systems like OpenAPI.

- **test/**: Contains unit and integration tests for modules and core components.

Documentation and Comments:

- Always update README.md file when:
  - On project creation: Generate initial README with architecture overview diagram
  - On new bounded context: Add section to README documenting the context
  - On major version increment: Update version history section
  - On new API endpoint: Update API documentation section
  - On dependency changes: Update prerequisites section
  - On architecture changes: Update architecture diagram

- Use XML comments for public methods and classes to provide documentation.

- Write comments to explain complex logic or decisions in the code.

Error Handling:

- Define custom exceptions in the Core layer for custom logic errors.

- Use standard .NET exceptions for infrastructure-related errors.

- In the Application layer, wrap infrastructure exceptions with business logic exceptions if necessary and can define exception in Core layer.

Testing:

- Always use xUnit for write test

- Always use Moq for mocking

- Write unit tests for all public methods and classes.

- Use meaningful test names that describe the scenario being tested.

- Focus testing logic on domain layer

Dependency Injection:

- Register domain services with scoped lifetime

- Register repositories with scoped lifetime

- Always constructor-inject dependencies

- Always make sure one way injection flow and make sure don't have circle depend

Performance:

- Optimize performance with caching (Redis).

- Use asynchronous programming for I/O-bound operations.

- Always use using block for dispoable object

