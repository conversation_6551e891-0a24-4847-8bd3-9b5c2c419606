using System.Threading.Tasks;
using KvFnB.Core.Domain.Commands;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for dispatching commands to external systems.
    /// </summary>
    public interface ICommandDispatcher
    {
        /// <summary>
        /// Sends a command to the specified topic.
        /// </summary>
        /// <typeparam name="TCommand">Type of the command</typeparam>
        /// <param name="command">The command to send</param>
        /// <param name="topic">Optional topic name. If not provided, the default topic will be used.</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SendAsync<TCommand>(TCommand command, string topic = "") where TCommand : BaseDigitalCommand;
    }
} 