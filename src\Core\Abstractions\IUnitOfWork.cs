namespace KvFnB.Core.Abstractions
{
    public interface IUnitOfWork
    {
        /// <summary>
        /// Commits all changes made in the current unit of work
        /// </summary>
        Task CommitAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Begins a transaction that can be committed or rolled back
        /// </summary>
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Commits the current transaction
        /// </summary>
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Rolls back the current transaction
        /// </summary>
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets or sets a value indicating whether a transaction is in progress
        /// </summary>
        bool HasActiveTransaction { get; }
    }
}
