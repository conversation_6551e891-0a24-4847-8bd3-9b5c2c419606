using KvFnB.Core.Domain;

namespace KvFnB.Core.Domain.Models
{
    /// <summary>
    /// Represents an address entity in the system
    /// </summary>
    public class Address : Entity<long>
    {
        /// <summary>
        /// Gets or sets the address line
        /// </summary>
        public string? AddressLine { get; set; }

        /// <summary>
        /// Gets or sets the administrative area identifier
        /// </summary>
        public int AdministrativeAreaId { get; set; }

        /// <summary>
        /// Gets or sets the postal code
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// Private constructor for EF Core
        /// </summary>
        public Address() { }

        /// <summary>
        /// Creates a new instance of the Address class
        /// </summary>
        /// <param name="addressLine">The address line</param>
        /// <param name="administrativeAreaId">The administrative area identifier</param>
        /// <param name="postalCode">The postal code</param>
        /// <returns>A new Address instance</returns>
        public static Address Create(string? addressLine, int administrativeAreaId, string? postalCode)
        {
            return new Address
            {
                AddressLine = addressLine,
                AdministrativeAreaId = administrativeAreaId,
                PostalCode = postalCode
            };
        }

        /// <summary>
        /// Updates the address properties
        /// </summary>
        /// <param name="addressLine">The new address line</param>
        /// <param name="administrativeAreaId">The new administrative area identifier</param>
        /// <param name="postalCode">The new postal code</param>
        public void UpdateAddress(string? addressLine, int administrativeAreaId, string? postalCode)
        {
            AddressLine = addressLine;
            AdministrativeAreaId = administrativeAreaId;
            PostalCode = postalCode;
        }
    }
} 