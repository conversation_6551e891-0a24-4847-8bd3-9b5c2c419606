---
description: 
globs: 
alwaysApply: false
---
 ---
description: Guidelines for middleware ordering in ASP.NET Core applications
globs: ["**/Program.cs", "**/*Startup.cs"]
alwaysApply: false
---

# ASP.NET Core Middleware Ordering Guidelines

## General Principles

1. **Order Matters**: Middleware executes in the order it's added to the pipeline.
2. **Two Phases**: Each middleware has a "before next" and "after next" phase:
   - Code before `await next(context)` runs on the way in (request)
   - Code after `await next(context)` runs on the way out (response)

## Standard Middleware Order

When configuring middleware in `Program.cs` or `Startup.cs`, follow this recommended order:

```csharp
// Early middleware - exception handlers, HSTS, etc.
app.UseExceptionHandler("/Error");
app.UseHsts();

// Routing and network-related middleware
app.UseStaticFiles();
app.UseRouting();

// Security middleware
app.UseCors();
app.UseAuthentication();
app.UseAuthorization();

// Endpoint execution middleware
app.UseSession();
app.MapControllers();  // or UseEndpoints()
```

## Critical Ordering Rules

1. **Exception Handling**: Must be first (`UseExceptionHandler`) to catch exceptions from all other middleware.

2. **Static Files**: Place early (`UseStaticFiles`) to avoid unnecessary processing.

3. **Routing**: `UseRouting()` must come before middleware that depends on routing data.

4. **CORS**: `UseCors()` must come before `UseResponseCaching()` and after `UseRouting()`.

5. **Authentication/Authorization**: Always in this order:
   ```csharp
   app.UseAuthentication();
   app.UseAuthorization();
   ```
   Must come after `UseRouting()` and before endpoints.

6. **Endpoints**: `MapControllers()` or `UseEndpoints()` should be last or near the end.

## Custom Middleware Placement

When adding custom middleware, consider:

1. **Processing needs**: If your middleware needs route information, place after `UseRouting()`.

2. **Request modification**: Middleware that modifies the request should come early.

3. **Response modification**: Middleware that modifies the response should come later.

4. **Terminating middleware**: If your middleware can short-circuit the pipeline (not calling `next`), place carefully considering what middleware should be skipped.

## Example of Proper Ordering

```csharp
var app = builder.Build();

// Exception handling (first)
app.UseExceptionHandler("/Error");
if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
}

// Early middleware
app.UseStaticFiles();

// Routing
app.UseRouting();

// Security
app.UseCors();
app.UseAuthentication();
app.UseAuthorization();

// Custom middleware after auth but before endpoints
app.UseMiddleware<CustomMiddleware>();

// Endpoints (last)
app.MapControllers();

app.Run();
```

Remember: Each middleware component should only be added once to the pipeline, and ordering is critical for proper application behavior.