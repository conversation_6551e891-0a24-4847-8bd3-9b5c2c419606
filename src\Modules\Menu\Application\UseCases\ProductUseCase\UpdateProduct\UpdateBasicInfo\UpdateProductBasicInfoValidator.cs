using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo;

/// <summary>
/// Validates the UpdateProductBasicInfo request
/// </summary>
public class UpdateProductBasicInfoValidator : Validator<UpdateProductBasicInfoRequest>
{
    public UpdateProductBasicInfoValidator(ILocalizationProvider multiLang)
    {
        // Rule for Id: Must not be null and greater than 0
        RuleFor(x => x.ProductId)
            .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_required))
            .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_id_more_than_zero));

        // Rule for Code: Must not be null or empty and have a valid length
        RuleFor(x => x.Code)
            .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_code_required))
            .GreaterThanMaxLength(50, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_code_max_length));

        // Rule for Name: Must not be null or empty and have a valid length
        RuleFor(x => x.Name)
            .NotNull(multiLang.GetMessage(LocalizationKeys.man_product_validate_update_name_required))
            .GreaterThanMaxLength(255, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_name_max_length));

        // Rule for CategoryId: Must be greater than 0
        RuleFor(x => x.CategoryId)
            .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_category_id_more_than_zero));

        // Rule for BasePrice: Must be greater than or equal to 0
        RuleFor(x => x.BasePrice)
            .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_base_price_min));

        // Rule for Weight: Must be greater than or equal to 0
        RuleFor(x => x.Weight)
            .GreaterThanOrEqual(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_weight_min));

        // Rule for ProductGroup: Must be a valid ProductGroupTypes value
        RuleFor(x => x.ProductGroupId)
            .Must(id => id == null || id > 0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_group_id_positive_optional));

        RuleFor(x => x.ProductGroupId)
            .Must(id => id == null || ProductGroupTypes.List().Any(pg => pg.Id == id), multiLang.GetMessage(LocalizationKeys.man_product_validate_update_product_group_id_invalid));

        // Rule for TaxId: Must be greater than 0 if not null   
        RuleFor(x => x.TaxId)
            .GreaterThan(0, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_tax_id_more_than_zero))
            .When(x => x.TaxId.HasValue);

#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        // order template
        RuleFor(x => x.OrderTemplate)
            .GreaterThanMaxLength(500, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_order_template_max_length))
            .When(x => x.OrderTemplate != null);

        // Rule for Description: If provided, must have a valid length
        RuleFor(x => x.Description)
            .GreaterThanMaxLength(2000, multiLang.GetMessage(LocalizationKeys.man_product_validate_update_description_max_length))
            .When(x => !string.IsNullOrEmpty(x.Description));
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
    }
}