using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using System.Net.Mail;
using System.Text.RegularExpressions;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer
{
    /// <summary>
    /// Validates the CreateCustomer request
    /// </summary>
    public class CreateCustomerValidator : Validator<CreateCustomerRequest>
    {
        public CreateCustomerValidator()
        {
            RuleFor(x => x.Code)
                .GreaterThanMaxLength(50, "Customer code must be less than 50 characters.")
                .When(x => !string.IsNullOrEmpty(x.Code));
            
            RuleFor(x => x.Name)
                .NotNullOrEmpty("Customer name is required.")
                .GreaterThanMaxLength(255, "Customer name must be less than 255 characters.");

            RuleFor(x => x.Email ?? "")
                .EmailValid("Invalid email address format.")
                .GreaterThanMaxLength(255, "Email must be less than 255 characters")
                .When(x => !string.IsNullOrEmpty(x.Email));

            RuleFor(x => x.ContactNumber!)
                .GreaterThanMaxLength(20, "Contact number must be less than 20 characters.")
                .When(x => !string.IsNullOrEmpty(x.ContactNumber));

            RuleFor(x => x.Type)
                .Must(t => t >= 1 && t <= 2, "Customer type must be either 1 (Individual) or 2 (Corporate).");

            RuleFor(x => x.Address!)
                .GreaterThanMaxLength(500, "Address must be less than 500 characters.")
                .When(x => !string.IsNullOrEmpty(x.Address));

            RuleFor(x => x.Comments!)
                .GreaterThanMaxLength(1000, "Comments must be less than 1000 characters.")
                .When(x => !string.IsNullOrEmpty(x.Comments));

            RuleFor(x => x.TaxCode!)
                .GreaterThanMaxLength(20, "Tax code must be less than 20 characters.")
                .When(x => !string.IsNullOrEmpty(x.TaxCode));

            RuleFor(x => x.Organization!)
                .GreaterThanMaxLength(255, "Organization must be less than 255 characters.")
                .When(x => !string.IsNullOrEmpty(x.Organization));

            RuleFor(x => x.AddressLocation!.AddressLine ?? "")
                .GreaterThanMaxLength(255, "Address must be less than 255 characters.")
                .When(x => x.AddressLocation != null && !string.IsNullOrEmpty(x.AddressLocation.AddressLine));
        }
    }
} 