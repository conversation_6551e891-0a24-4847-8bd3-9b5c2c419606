using KvFnB.Core.Abstractions;
using KvFnB.Modules.Customer.Domain.Models;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Customer.Infrastructure.Persistence
{
    public class CustomerGroupRepository : ICustomerGroupRepository
    {
        private readonly ShardingDbContext _context;

        public CustomerGroupRepository(IUnitOfWork unitOfWork)
        {
            _context = unitOfWork as ShardingDbContext ?? throw new ArgumentException("UnitOfWork must be of type ShardingDbContext", nameof(unitOfWork));
        }

        public async Task<IEnumerable<CustomerGroup>> GetByIdsAsync(IEnumerable<int> groupIds, CancellationToken cancellationToken = default)
        {
            if (groupIds == null || groupIds.Any())
                return [];
                
            return await _context.Set<CustomerGroup>()
                .Where(g => groupIds.Contains(g.Id) && (g.Is<PERSON>eleted == null || g.IsDeleted == false))
                .ToListAsync(cancellationToken);
        }
    }
} 