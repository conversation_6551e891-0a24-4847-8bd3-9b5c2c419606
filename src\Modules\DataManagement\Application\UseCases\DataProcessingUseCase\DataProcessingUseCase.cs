using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Models;


namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase
{
    /// <summary>
    /// Implements the DataProcessing use case for processing data messages
    /// </summary>
    public class DataProcessingUseCase : UseCaseBase<DataProcessingRequest, DataProcessingResponse>, IDataProcessingUseCase
    {
        private readonly ILogger _logger;
        private readonly IDataProcessingHandlerFactory _handlerFactory;
        private readonly IValidator<DataProcessingRequest> _validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataProcessingUseCase"/> class
        /// </summary>
        /// <param name="validator">The validator for request validation</param>
        /// <param name="logger">The logger</param>
        /// <param name="handlerFactory">The handler factory</param>
        /// <param name="queryService">The query service for database access</param>
        public DataProcessingUseCase(
            IValidator<DataProcessingRequest> validator,
            ILogger logger,
            IDataProcessingHandlerFactory handlerFactory)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _handlerFactory = handlerFactory ?? throw new ArgumentNullException(nameof(handlerFactory));
        }

        /// <summary>
        /// Executes the data processing use case with the provided request
        /// </summary>
        /// <param name="request">The processing request</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>A result containing the processing response</returns>
        public override async Task<Result<DataProcessingResponse>> ExecuteAsync(
            DataProcessingRequest request, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Validate the request first
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DataProcessingResponse>.Failure(string.Join(", ", validationResult.Errors));
                }

                _logger.Information($"Processing data message: {request.Id}, Type: {request.Type}, RetailerId: {request.RetailerId}");

                // Convert request to message for the handler
                var message = new DataProcessingMessage
                {
                    Id = request.Id,
                    Type = request.Type,
                    RetailerId = request.RetailerId,
                    RequestId = request.RequestId,
                    BranchId = request.BranchId,
                    FilterCondition = request.FilterCondition
                };

                // Get appropriate handler
                var handler = _handlerFactory.GetHandler(message.Type);
                if (handler == null)
                {
                    _logger.Error($"No handler found for message type: {message.Type}");
                    return Result<DataProcessingResponse>.Failure($"Unsupported message type: {message.Type}");
                }

                // Process the message
                var processingResult = await handler.ProcessDeleteAsync(message, cancellationToken);
                
                if (!processingResult.IsSuccess)
                {
                    return Result<DataProcessingResponse>.Failure(processingResult.ErrorMessage ?? string.Empty);
                }

                // Map result to response
                var response = new DataProcessingResponse
                {
                    Status = processingResult.Value?.Status ?? "Success",
                    JobId = processingResult.Value?.JobId ?? 0,
                    TotalRecordsProcessed = processingResult.Value?.TotalRecordsProcessed ?? 0,
                    AlreadyProcessed = processingResult.Value?.AlreadyProcessed ?? false
                };

                return Result<DataProcessingResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing data message: {request.Id}", ex);
                return Result<DataProcessingResponse>.Failure("Internal server error");
            }
        }
    }
} 