namespace KvFnB.Modules.DataManagement.Application.Abstractions
{
    /// <summary>
    /// Factory interface for creating data processing handlers
    /// </summary>
    public interface IDataProcessingHandlerFactory
    {
        /// <summary>
        /// Gets the appropriate handler for the specified message type
        /// </summary>
        /// <param name="type">The message type</param>
        /// <returns>The handler for the specified type, or null if no handler is available</returns>
        IDataProcessingHandler? GetHandler(string type);
    }
} 