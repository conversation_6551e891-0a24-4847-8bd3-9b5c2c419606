using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.ActiveProductPriceBook;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.PriceBookUseCase;

public class ActiveProductPriceBookUseCaseTests
{
    private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
    private readonly Mock<IValidator<ActiveProductPriceBookRequest>> _validatorMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly ActiveProductPriceBookUseCase _useCase;

    public ActiveProductPriceBookUseCaseTests()
    {
        _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
        _validatorMock = new Mock<IValidator<ActiveProductPriceBookRequest>>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _useCase = new ActiveProductPriceBookUseCase(
            _validatorMock.Object,
            _priceBookRepositoryMock.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WhenRequestIsValid_ShouldActivatePriceBook()
    {
        // Arrange
        var request = new ActiveProductPriceBookRequest
        {
            PriceBookId = 1
        };

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        
        // Ensure the price book is deactivated initially
        priceBook.Deactivate();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(priceBook.IsActive); // Verify the price book was activated
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(priceBook, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Arrange
        var request = new ActiveProductPriceBookRequest
        {
            PriceBookId = 0 // Invalid ID
        };

        var validationErrors = new List<string> { "Price book ID must be greater than zero." };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(false, validationErrors));

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(validationErrors, result.ValidationErrors);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenPriceBookNotFound_ShouldReturnFailure()
    {
        // Arrange
        var request = new ActiveProductPriceBookRequest
        {
            PriceBookId = 999 // Non-existent ID
        };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PriceBook?)null);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal($"Price book with ID {request.PriceBookId} not found.", result.ErrorMessage);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()), Times.Once);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenAlreadyActivated_ShouldStillSucceed()
    {
        // Arrange
        var request = new ActiveProductPriceBookRequest
        {
            PriceBookId = 1
        };

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        
        // Price book is active by default after creation
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(priceBook.IsActive); // Verify the price book is still activated
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(priceBook, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
} 