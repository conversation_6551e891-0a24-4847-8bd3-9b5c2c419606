namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for permission service that handles user permission checks and retrieval
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// Gets all permissions for the current user
        /// </summary>
        /// <returns>Dictionary of permission names and associated branch IDs</returns>
        Task<Dictionary<string, List<long>>> GetUserPermissionsAsync();

        /// <summary>
        /// Asynchronously checks if the current user has permission for the specified permission name
        /// </summary>
        /// <param name="permissionName">Permission name to check access for</param>
        /// <returns>True if user has permission, otherwise false</returns>
        Task<bool> CheckPermissionAsync(string permissionName);

        /// <summary>
        /// Asynchronously checks if the current user has any of the specified permissions
        /// </summary>
        /// <param name="permissionNames">Array of permission names to check against</param>
        /// <returns>True if the user has at least one of the specified permissions, otherwise false</returns>
        Task<bool> CheckAnyPermissionsAsync(params string[] permissionNames);
        
        /// <summary>
        /// Gets the current user ID
        /// </summary>
        string UserId { get; }

        /// <summary>
        /// Gets whether the current user is an admin
        /// </summary>
        bool IsAdmin { get; }
    }
} 