using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Modules.Menu.Application.Enums;
using KvFnB.Shared.MultiTenancy;

namespace KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateScopeOfApplication
{
    /// <summary>
    /// Implements the UpdateScopeOfApplication use case.
    /// This class handles updating the scope of application (branches, customer groups, etc.) of a price book.
    /// </summary>
    public class UpdateScopeOfApplicationUseCase
    {
        private readonly IPriceBookRepository _priceBookRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IValidator<UpdateScopeOfApplicationRequest> _validator;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly TenantInfo _tenantInfo;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateScopeOfApplicationUseCase"/> class.
        /// </summary>
        /// <param name="priceBookRepository">The repository for price books.</param>
        /// <param name="unitOfWork">The unit of work for transaction management.</param>
        /// <param name="validator">The validator for the request.</param>
        /// <param name="mapper">The mapper for mapping between entities and DTOs.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="tenantProvider">The tenant provider.</param>
        public UpdateScopeOfApplicationUseCase(
            IPriceBookRepository priceBookRepository,
            IUnitOfWork unitOfWork,
            IValidator<UpdateScopeOfApplicationRequest> validator,
            IMapper mapper,
            ILogger logger,
            TenantInfo tenantInfo)
        {
            _priceBookRepository = priceBookRepository ?? throw new ArgumentNullException(nameof(priceBookRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tenantInfo = tenantInfo ?? throw new ArgumentNullException(nameof(tenantInfo));
        }

        /// <summary>
        /// Executes the use case to update the scope of application settings for a price book.
        /// </summary>
        /// <param name="request">The request containing the updated scope settings.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the response or error messages.</returns>
        public async Task<Result<UpdateScopeOfApplicationResponse>> ExecuteAsync(UpdateScopeOfApplicationRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                // 1. Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<UpdateScopeOfApplicationResponse>.Failure(validationResult.Errors);
                }

                // 2. Get the price book from repository
                var priceBook = await _priceBookRepository.GetAsync(request.Id, cancellationToken);
                if (priceBook == null)
                {
                    return Result<UpdateScopeOfApplicationResponse>.Failure($"Price book with ID {request.Id} not found");
                }

                // 3. Update scope settings
                UpdateScopeSettings(priceBook, request);

                // 4. Update child entities
                UpdateChildEntities(priceBook, request);

                // 5. Save the updated price book
                await _priceBookRepository.UpdateAsync(priceBook, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                // 6. Map and return response
                var response = _mapper.Map<UpdateScopeOfApplicationResponse>(priceBook);
                return Result<UpdateScopeOfApplicationResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error updating scope of application for price book with ID {request.Id}", ex);
                return Result<UpdateScopeOfApplicationResponse>.Failure($"Error updating price book: {ex.Message}");
            }
        }

        private static void UpdateScopeSettings(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Update scope settings using the existing Update method
            priceBook.Update(new UpdatePriceBookModel
            {
                Name = priceBook.Name,
                StartDate = priceBook.StartDate,
                EndDate = priceBook.EndDate,
                IsGlobal = request.IsGlobal,
                ForAllUser = request.ForAllUser,
                ForAllCustomerGroup = request.ForAllCustomerGroup,
                ForAllTableAndRoom = request.ForAllTableAndRoom,
                ForTakeAwayTable = request.ForTakeAwayTable,
                Type = request.Type
            });
        }

        private void UpdateChildEntities(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            UpdatePriceBookBranches(priceBook, request);
            UpdatePriceBookCustomerGroups(priceBook, request);
            UpdatePriceBookTableAndRooms(priceBook, request);
            UpdatePriceBookGroups(priceBook, request);
            UpdatePriceBookDiningOptions(priceBook, request);
            UpdatePriceBookUsers(priceBook, request);
        }

        private static void UpdatePriceBookBranches(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book is global
            if (request.IsGlobal)
            {
                // Clear all existing branches since they're not needed for global price books
                ((List<PriceBookBranch>)priceBook.PriceBookBranches).Clear();
                return;
            }

            // If no branch IDs provided, return without making changes
            if (request.PriceBookBranchIds == null || request.PriceBookBranchIds.Count == 0)
            {
                return;
            }

            // Get existing branch IDs
            var existingBranchIds = priceBook.PriceBookBranches.Select(b => b.BranchId).ToList();
            
            // Add new branches that don't exist yet
            foreach (var branchId in request.PriceBookBranchIds)
            {
                if (!existingBranchIds.Contains(branchId))
                {
                    priceBook.AddPriceBookBranch(branchId);
                }
            }

            // Remove branches that are no longer in the request
            var branchesToRemove = priceBook.PriceBookBranches
                .Where(b => !request.PriceBookBranchIds.Contains(b.BranchId))
                .ToList();

            foreach (var branch in branchesToRemove)
            {
                ((List<PriceBookBranch>)priceBook.PriceBookBranches).Remove(branch);
            }
        }

        private static void UpdatePriceBookCustomerGroups(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book is for all customer groups
            if (request.ForAllCustomerGroup)
            {
                // Clear all existing customer groups since they're not needed
                ((List<PriceBookCustomerGroup>)priceBook.PriceBookCustomerGroups).Clear();
                return;
            }

            // If no customer group IDs provided, return without making changes
            if (request.PriceBookCustomerGroupIds == null || request.PriceBookCustomerGroupIds.Count == 0)
            {
                return;
            }

            // Get existing customer group IDs
            var existingCustomerGroupIds = priceBook.PriceBookCustomerGroups.Select(cg => cg.CustomerGroupId).ToList();
            
            // Add new customer groups that don't exist yet
            foreach (var customerGroupId in request.PriceBookCustomerGroupIds)
            {
                if (!existingCustomerGroupIds.Contains(customerGroupId))
                {
                    priceBook.AddPriceBookCustomerGroup(customerGroupId);
                }
            }

            // Remove customer groups that are no longer in the request
            var customerGroupsToRemove = priceBook.PriceBookCustomerGroups
                .Where(cg => !request.PriceBookCustomerGroupIds.Contains(cg.CustomerGroupId))
                .ToList();

            foreach (var customerGroup in customerGroupsToRemove)
            {
                ((List<PriceBookCustomerGroup>)priceBook.PriceBookCustomerGroups).Remove(customerGroup);
            }
        }

        private static void UpdatePriceBookTableAndRooms(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book is for all tables and rooms
            if (request.ForAllTableAndRoom)
            {
                // Clear all existing tables and rooms since they're not needed
                ((List<PriceBookTableAndRoom>)priceBook.PriceBookTableAndRooms).Clear();
                return;
            }

            // If no table and room IDs provided, return without making changes
            if (request.PriceBookTableAndRoomIds == null || request.PriceBookTableAndRoomIds.Count == 0)
            {
                return;
            }

            // Get existing table and room IDs
            var existingTableAndRoomIds = priceBook.PriceBookTableAndRooms.Select(tr => tr.TableAndRoomId).ToList();
            
            // Add new tables and rooms that don't exist yet
            foreach (var tableAndRoomId in request.PriceBookTableAndRoomIds)
            {
                if (!existingTableAndRoomIds.Contains(tableAndRoomId))
                {
                    priceBook.AddPriceBookTableAndRoom(tableAndRoomId);
                }
            }

            // Remove tables and rooms that are no longer in the request
            var tableAndRoomsToRemove = priceBook.PriceBookTableAndRooms
                .Where(tr => !request.PriceBookTableAndRoomIds.Contains(tr.TableAndRoomId))
                .ToList();

            foreach (var tableAndRoom in tableAndRoomsToRemove)
            {
                ((List<PriceBookTableAndRoom>)priceBook.PriceBookTableAndRooms).Remove(tableAndRoom);
            }
        }

        private static void UpdatePriceBookGroups(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book type is SC
            if (request.Type != null && request.Type.Equals("SC"))
            {
                // Clear all existing groups since they're not needed for SC price books
                ((List<PriceBookGroup>)priceBook.PriceBookGroups).Clear();
                return;
            }

            // If no price book groups provided, return without making changes
            if (request.PriceBookGroups == null || request.PriceBookGroups.Count == 0)
            {
                return;
            }

            // Create a list to track existing group IDs and their settings
            var existingGroups = priceBook.PriceBookGroups
                .Select(g => new { g.GroupId, g.Status, g.Type })
                .ToList();
            
            // Add new groups or update existing ones
            foreach (var group in request.PriceBookGroups)
            {
                var existingGroup = existingGroups.FirstOrDefault(g => g.GroupId == group.GroupId);
                
                if (existingGroup == null)
                {
                    // Group doesn't exist, add it
                    priceBook.AddPriceBookGroup(group.GroupId, group.Status, group.Type);
                }
                else
                {
                    // Group exists but settings might have changed
                    // If settings are different, remove the old one and add the new one
                    if (existingGroup.Status != group.Status || existingGroup.Type != group.Type)
                    {
                        var groupToRemove = priceBook.PriceBookGroups
                            .First(g => g.GroupId == group.GroupId);
                        
                        ((List<PriceBookGroup>)priceBook.PriceBookGroups).Remove(groupToRemove);
                        priceBook.AddPriceBookGroup(group.GroupId, group.Status, group.Type);
                    }
                }
            }

            // Remove groups that are no longer in the request
            var requestGroupIds = request.PriceBookGroups.Select(g => g.GroupId).ToList();
            var groupsToRemove = priceBook.PriceBookGroups
                .Where(g => !requestGroupIds.Contains(g.GroupId))
                .ToList();

            foreach (var group in groupsToRemove)
            {
                ((List<PriceBookGroup>)priceBook.PriceBookGroups).Remove(group);
            }
        }

        private void UpdatePriceBookDiningOptions(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book is for all tables and rooms
            if (request.ForAllTableAndRoom)
            {
                // Clear all existing dining options since they're not needed
                ((List<PriceBookDiningOption>)priceBook.PriceBookDiningOptions).Clear();
                return;
            }

            // If no dining options provided, return without making changes
            if (request.PriceBookDiningOptions == null || request.PriceBookDiningOptions.Count == 0)
            {
                return;
            }

            // Get the current tenant ID
            var tenantId = _tenantInfo.Id;

            // Filter dining options to include only delivery and takeaway
            var validDiningOptions = request.PriceBookDiningOptions
                .Where(d => d.DiningOption == (byte)DiningOptions.Delivery || d.DiningOption == (byte)DiningOptions.TakeAway)
                .ToList();

            // Create a list to track existing dining options
            var existingDiningOptions = priceBook.PriceBookDiningOptions
                .Select(d => new { d.DiningOption, d.BranchId, d.TenantId })
                .ToList();
            
            // Add new dining options
            foreach (var diningOption in validDiningOptions)
            {
                var exists = existingDiningOptions.Any(d => 
                    d.DiningOption == diningOption.DiningOption && 
                    d.BranchId == diningOption.BranchId && 
                    d.TenantId == tenantId);
                
                if (!exists)
                {
                    priceBook.AddPriceBookDiningOption(diningOption.DiningOption, diningOption.BranchId, tenantId);
                }
            }

            // Remove dining options that are no longer in the request
            var diningOptionsToRemove = priceBook.PriceBookDiningOptions
                .Where(d => !validDiningOptions.Any(vd => 
                    vd.DiningOption == d.DiningOption && 
                    vd.BranchId == d.BranchId) || 
                    d.TenantId != tenantId)
                .ToList();

            foreach (var diningOption in diningOptionsToRemove)
            {
                ((List<PriceBookDiningOption>)priceBook.PriceBookDiningOptions).Remove(diningOption);
            }
        }

        private static void UpdatePriceBookUsers(PriceBook priceBook, UpdateScopeOfApplicationRequest request)
        {
            // Skip if price book is for all users
            if (request.ForAllUser)
            {
                // Clear all existing users since they're not needed
                ((List<PriceBookUser>)priceBook.PriceBookUsers).Clear();
                return;
            }

            // If no user IDs provided, return without making changes
            if (request.PriceBookUserIds == null || request.PriceBookUserIds.Count == 0)
            {
                return;
            }

            // Get existing user IDs
            var existingUserIds = priceBook.PriceBookUsers.Select(u => u.UserId).ToList();
            
            // Add new users that don't exist yet
            foreach (var userId in request.PriceBookUserIds)
            {
                if (!existingUserIds.Contains(userId))
                {
                    priceBook.AddPriceBookUser(userId);
                }
            }

            // Remove users that are no longer in the request
            var usersToRemove = priceBook.PriceBookUsers
                .Where(u => !request.PriceBookUserIds.Contains(u.UserId))
                .ToList();

            foreach (var user in usersToRemove)
            {
                ((List<PriceBookUser>)priceBook.PriceBookUsers).Remove(user);
            }
        }
    }
} 