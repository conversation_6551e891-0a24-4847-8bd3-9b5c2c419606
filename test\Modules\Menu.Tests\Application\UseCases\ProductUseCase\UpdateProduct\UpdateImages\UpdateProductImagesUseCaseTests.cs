using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateImages;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Localization;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.UpdateProduct.UpdateImages
{
    public class UpdateProductImagesUseCaseTests
    {
        private readonly Mock<IValidator<UpdateProductImagesRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<ILocalizationProvider> _multiLang;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly UpdateProductImagesUseCase _useCase;
        private readonly Mock<ILogger> _logger;

        public UpdateProductImagesUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<UpdateProductImagesRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _multiLang = new Mock<ILocalizationProvider>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _logger = new Mock<ILogger>();

            _useCase = new UpdateProductImagesUseCase(
                _multiLang.Object,
                _validatorMock.Object,
                _productRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _tenantProviderMock.Object,
                _logger.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductImagesRequest
            {
                ProductId = 1,
                Images = new List<ProductImageDto>
                {
                    new ProductImageDto { Url = "https://example.com/image.jpg" }
                }
            };
            
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductImagesRequest
            {
                ProductId = 1,
                Images = new List<ProductImageDto>
                {
                    new ProductImageDto { Url = "https://example.com/image.jpg" }
                }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldUpdateProductImages()
        {
            // Arrange
            var request = new UpdateProductImagesRequest
            {
                ProductId = 1,
                Images = new List<ProductImageDto> { new ProductImageDto { Url = "image1.jpg" }, new ProductImageDto { Url = "image2.jpg" } }
            };
            
            // Create a real Product instance instead of mocking it
            var product = Product.CreateProduct(
                code: "PROD-001",
                name: "Test Product",
                categoryId: 1,
                productType: (byte)ProductTypes.Normal.Id,
                unit: "pcs",
                conversionValue: 1.0f,
                masterCode: "PROD-001"
            );
            
            // Set ModifiedAt to the current time
            typeof(Product).GetProperty("ModifiedAt")!.SetValue(product, DateTime.UtcNow);
            
            var response = new UpdateProductImagesResponse
            {
                Id = 1,
                Images = request.Images
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _productRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>())).ReturnsAsync(product);
            
            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _productRepositoryMock.Verify(r => r.UpdateAsync(product, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new UpdateProductImagesRequest
            {
                ProductId = 1,
                Images = new List<ProductImageDto>
                {
                    new ProductImageDto { Url = "https://example.com/image.jpg" }
                }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.ProductId, It.IsAny<CancellationToken>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var exception = await Assert.ThrowsAsync<Exception>(() => _useCase.ExecuteAsync(request, CancellationToken.None));
        }
    }
} 