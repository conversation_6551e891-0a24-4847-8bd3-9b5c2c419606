using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.DeActiveProductPriceBook;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.PriceBookUseCase;

public class DeActiveProductPriceBookUseCaseTests
{
    private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
    private readonly Mock<IValidator<DeActiveProductPriceBookRequest>> _validatorMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly DeActiveProductPriceBookUseCase _useCase;

    public DeActiveProductPriceBookUseCaseTests()
    {
        _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
        _validatorMock = new Mock<IValidator<DeActiveProductPriceBookRequest>>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _useCase = new DeActiveProductPriceBookUseCase(
            _validatorMock.Object,
            _priceBookRepositoryMock.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WhenRequestIsValid_ShouldDeactivatePriceBook()
    {
        // Arrange
        var request = new DeActiveProductPriceBookRequest
        {
            PriceBookId = 1
        };

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        
        // Ensure the price book is active initially
        priceBook.Activate();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(priceBook.IsActive); // Verify the price book was deactivated
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(priceBook, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Arrange
        var request = new DeActiveProductPriceBookRequest
        {
            PriceBookId = 0 // Invalid ID
        };

        var validationErrors = new List<string> { "Price book ID must be greater than zero." };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(false, validationErrors));

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(validationErrors, result.ValidationErrors);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenPriceBookNotFound_ShouldReturnFailure()
    {
        // Arrange
        var request = new DeActiveProductPriceBookRequest
        {
            PriceBookId = 999 // Non-existent ID
        };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PriceBook?)null);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal($"Price book with ID {request.PriceBookId} not found.", result.ErrorMessage);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()), Times.Once);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenAlreadyDeactivated_ShouldStillSucceed()
    {
        // Arrange
        var request = new DeActiveProductPriceBookRequest
        {
            PriceBookId = 1
        };

        var priceBook = PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
        
        // Ensure the price book is already deactivated
        priceBook.Deactivate();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.PriceBookId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(priceBook.IsActive); // Verify the price book is still deactivated
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(priceBook, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
} 