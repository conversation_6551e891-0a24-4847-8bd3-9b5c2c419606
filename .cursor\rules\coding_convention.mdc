---
description: 
globs: 
alwaysApply: true
---
---
description: Clean code guidelines for code generation
globs: 
alwaysApply: true
---

# Clean Code Guidelines

When generating code, ensure:

1. **Parameter Limits**: 
   - Do not pass more than 7 parameters to a constructor or function
   - Ideally, keep parameter count to 5 or fewer when possible

2. **Function Complexity**:
   - Keep function complexity below 15 (measured like SonarQube)
   - Break complex functions into smaller, more manageable functions
   - Each function should have a single responsibility
   

   # Clean Code Guidelines

When generating code, ensure:

1. **Parameter Limits**: 
   - Do not pass more than 7 parameters to a constructor or function
   - Ideally, keep parameter count to 5 or fewer when possible

2. **Function Complexity**:
   - Keep function complexity below 15 (measured like SonarQube)
   - Break complex functions into smaller, more manageable functions
   - Each function should have a single responsibility

3. **Documentation for API Models**:
   - Always add description attributes to properties in Request and Response models
   - For Swagger documentation, use `[Description("Description of the property.")]` attribute
   - For JSON serialization, use `[JsonPropertyName("property_name"), Description("Description of the property.")]`
   - Descriptions should be clear, concise, and explain the purpose of each property
   - Include units of measurement, valid ranges, or other constraints where applicable
   - Example:
     ```csharp
     [Description("The unique identifier of the product.")]
     public long Id { get; set; }
     
     [JsonPropertyName("country_id"), Description("The ID of the country where the branch is located.")]
     public int CountryId { get; set; }
     ```
4. **Sonar qube**
- Prefer comparing 'Count' to 0 rather than using 'Any()', both for clarity and for performance
