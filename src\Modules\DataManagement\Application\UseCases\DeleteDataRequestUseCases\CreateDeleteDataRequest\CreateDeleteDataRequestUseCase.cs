﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using System.Text.Json;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.CreateDeleteDataRequest
{
    /// <summary>
    /// Use case for creating a new data deletion request
    /// </summary>
    public class CreateDeleteDataRequestUseCase : UseCaseBase<CreateDeleteDataRequestRequest, DeleteDataRequestResponse>
    {
        private readonly IValidator<CreateDeleteDataRequestRequest> _validator;
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IOtpService _otpService;
        private readonly IMapper _mapper;
        private readonly IBranchService _branchService;

        public CreateDeleteDataRequestUseCase(
            IValidator<CreateDeleteDataRequestRequest> validator,
            IDeleteDataRequestRepository deleteDataRequestRepository,
            IUnitOfWork unitOfWork,
            IOtpService otpService,
            IMapper mapper,
            IBranchService branchService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _otpService = otpService ?? throw new ArgumentNullException(nameof(otpService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _branchService = branchService ?? throw new ArgumentNullException(nameof(branchService));
        }

        public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
            CreateDeleteDataRequestRequest request,
            CancellationToken cancellationToken = default)
        {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeleteDataRequestResponse>.Failure(validationResult.Errors);
                }

                // Validate branch IDs
                if (request.BranchIds != null && request.BranchIds.Count != 0)
                {
                    bool branchesValid = await _branchService.ValidateBranchAsync(request.BranchIds, cancellationToken);
                    if (!branchesValid)
                    {
                        return Result<DeleteDataRequestResponse>.Failure("Một hoặc nhiều chi nhánh ngừng hoạt động hoặc đã bị xóa");
                    }
                }

                // Check for duplicate execution date
                DateTime? executeDate = request.ExecuteDate != default ? request.ExecuteDate : null;
                if (executeDate.HasValue)
                {
                    // Use just the date part for comparison
                    var existingRequest = await _deleteDataRequestRepository.CheckDuplicateExecuteDateAsync(
                        executeDate.Value.Date,
                        cancellationToken);
                    
                    if (existingRequest != null)
                    {
                        var message = LocalizationProvider.GetLocalizedMessage("man.delete_data.message_error.duplicate_date", executeDate);
                        return Result<DeleteDataRequestResponse>.Failure(message);
                    }
                }

                // Verify OTP
                var otpVerified = await _otpService.VerifyOtpAsync(request.Otp, request.FingerPrintKey, cancellationToken);
                if (!otpVerified)
                {
                    return Result<DeleteDataRequestResponse>.Failure("Invalid or expired OTP");
                }
                if(request.FilterCondition != null && request.ScheduleType == DeleteDataScheduleType.Scheduled)
                {
                    request.FilterCondition.ToDate = request.ExecuteDate;
                }
                // Serialize ScheduleConfig to JSON
                var scheduleConfigJson = request.ScheduleConfig != null ? JsonSerializer.Serialize(request.ScheduleConfig) : null;
                
                // Serialize FilterCondition to JSON
                var filterConditionJson = request.FilterCondition != null ?  JsonSerializer.Serialize(request.FilterCondition) : null;
                // Serialize FilterCondition to JSON
                string branchIds = string.Join(",", request.BranchIds ?? []);

                // Create the DeleteDataRequest domain entity
                var deleteDataRequest = DeleteDataRequest.Create(
                    request.Email,
                    request.Type,
                    request.ScheduleType,
                    branchIds,
                    DeleteDataRequestStatus.Approved,
                    executeDate,
                    scheduleConfigJson,
                    filterConditionJson);
                
                // Save to repository
                await _deleteDataRequestRepository.AddAsync(deleteDataRequest, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                // Map to response and return
                var response = _mapper.Map<DeleteDataRequestResponse>(deleteDataRequest);
                return Result<DeleteDataRequestResponse>.Success(response);
        }
    }
}