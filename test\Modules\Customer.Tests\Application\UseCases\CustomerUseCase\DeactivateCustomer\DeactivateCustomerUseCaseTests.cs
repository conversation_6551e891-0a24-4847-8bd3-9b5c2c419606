using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer;
using KvFnB.Modules.Customer.Domain.Repositories;
using Microsoft.Extensions.Logging;
using Moq;

namespace KvFnB.Modules.Customer.Tests.Application.UseCases.CustomerUseCase.DeactivateCustomer
{
    public class DeactivateCustomerUseCaseTests
    {
        private readonly Mock<IValidator<DeactivateCustomerRequest>> _validatorMock;
        private readonly Mock<ICustomerRepository> _repositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger<DeactivateCustomerUseCase>> _loggerMock;
        private readonly DeactivateCustomerUseCase _useCase;

        public DeactivateCustomerUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<DeactivateCustomerRequest>>();
            _repositoryMock = new Mock<ICustomerRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<DeactivateCustomerUseCase>>();
            
            _useCase = new DeactivateCustomerUseCase(
                _validatorMock.Object,
                _repositoryMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new DeactivateCustomerRequest { CustomerId = 1 };
            var validationResult = new ValidationResult(false, new List<string> { "Error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
            // Verify that repository is not called when validation fails
            _repositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
            // Verify that transaction is not committed
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenEntityNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new DeactivateCustomerRequest { CustomerId = 1 };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _repositoryMock.Setup(r => r.GetAsync(request.CustomerId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Domain.Models.Customer)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("not found", result.ErrorMessage);
            // Verify that transaction is not committed
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldReturnSuccess()
        {
            // Arrange
            var request = new DeactivateCustomerRequest { CustomerId = 1 };
            var customer = new Domain.Models.Customer { Id = 1, IsActive = true };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _repositoryMock.Setup(r => r.GetAsync(request.CustomerId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(customer);
            _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(customer);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(customer.Id, result.Value?.CustomerId);
            Assert.True(result.Value?.IsDeactivated);
            
            // Verify customer is updated with IsActive = false
            _repositoryMock.Verify(r => r.UpdateAsync(It.Is<Domain.Models.Customer>(c => 
                c.Id == customer.Id && c.IsActive == false), It.IsAny<CancellationToken>()), Times.Once);
            
            // Verify transaction is committed
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailureWithStandardizedErrorMessage()
        {
            // Arrange
            var request = new DeactivateCustomerRequest { CustomerId = 1 };
            var validationResult = ValidationResult.Success();
            var exception = new Exception("Test exception");
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _repositoryMock.Setup(r => r.GetAsync(request.CustomerId, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(ErrorMessages.InternalServerError, result.ErrorMessage);
            
            // Verify exception is logged
            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => true),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
} 