using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.DeleteProduct;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.ProductUseCase.DeleteProduct
{
    public class DeleteProductUseCaseTests
    {
        private readonly Mock<IValidator<DeleteProductRequest>> _validatorMock;
        private readonly Mock<IProductRepository> _productRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<Core.Abstractions.IMapper> _mapperMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<ICodeGenerationService> _codeGenerationServiceMock;
        private readonly DeleteProductUseCase _useCase;

        public DeleteProductUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<DeleteProductRequest>>();
            _productRepositoryMock = new Mock<IProductRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _mapperMock = new Mock<Core.Abstractions.IMapper>();
            _loggerMock = new Mock<ILogger>();
            _codeGenerationServiceMock = new Mock<ICodeGenerationService>();

            _useCase = new DeleteProductUseCase(
                _validatorMock.Object,
                _productRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _mapperMock.Object,
                _loggerMock.Object,
                _codeGenerationServiceMock.Object
            );
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new DeleteProductRequest { Id = 1 };
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
            _productRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new DeleteProductRequest { Id = 1 };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync((Product)null!);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("not found", result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_WhenProductFound_ShouldDeleteAndReturnSuccess()
        {
            // Arrange
            var request = new DeleteProductRequest { Id = 1 };
            var validationResult = ValidationResult.Success();
            
            // Use CreateProduct static method to create a valid Product
            var product = Product.CreateProduct(
                "P001",
                "Test Product",
                1,
                (byte)ProductTypes.Normal.Id,
                "pcs",
                1.0f,
                "P001"
            );

            // Use reflection to set Id (as it's a protected setter)
            typeof(Product).GetProperty("Id")!.SetValue(product, 1L);
            
            var response = new DeleteProductResponse
            {
                Id = product.Id,
                Code = product.Code,
                Name = product.Name
            };
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync(product);
            _mapperMock.Setup(m => m.Map<DeleteProductResponse>(product)).Returns(response);
            _productRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>())).ReturnsAsync(product);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(response, result.Value);
            Assert.True(product.IsDeleted.GetValueOrDefault());
            _productRepositoryMock.Verify(r => r.UpdateAsync(It.Is<Product>(p => p.IsDeleted.GetValueOrDefault()), It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new DeleteProductRequest { Id = 1 };
            var validationResult = ValidationResult.Success();
            var exception = new Exception("Internal server error.");
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _productRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ThrowsAsync(exception);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains(exception.Message, result.ErrorMessage);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }
    }
} 