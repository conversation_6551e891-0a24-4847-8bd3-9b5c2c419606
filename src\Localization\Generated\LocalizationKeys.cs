namespace KvFnB.Localization
{
    public static class LocalizationKeys
    {
        public const string menu_product_category_name_can_not_be_null = "menu.product.category.name_can_not_be_null";
        public const string Test_Hello = "Test_Hello";
        public const string Test_Greeting = "Test_Greeting";
        public const string man_cashflow_message_bank_account_exists = "man.cashflow.message.bank_account_exists";
        public const string man_cashflow_message_branch_ids_invalid = "man.cashflow.message.branch_ids_invalid";
        public const string man_cashflow_message_bank_account_create_failed = "man.cashflow.message.bank_account_create_failed";
        public const string man_cashflow_message_account_name_too_long = "man.cashflow.message.account_name_too_long";
        public const string man_cashflow_message_account_required = "man.cashflow.message.account_required";
        public const string man_cashflow_message_bank_name_required = "man.cashflow.message.bank_name_required";
        public const string man_cashflow_message_bank_name_too_long = "man.cashflow.message.bank_name_too_long";
        public const string man_cashflow_message_bank_code_required = "man.cashflow.message.bank_code_required";
        public const string man_cashflow_message_bank_code_too_long = "man.cashflow.message.bank_code_too_long";
        public const string man_cashflow_message_branch_name_too_long = "man.cashflow.message.branch_name_too_long";
        public const string man_cashflow_message_description_too_long = "man.cashflow.message.description_too_long";
        public const string man_cashflow_message_bank_account_not_found = "man.cashflow.message.bank_account_not_found";
        public const string man_cashflow_message_bank_account_deleted = "man.cashflow.message.bank_account_deleted";
        public const string man_cashflow_message_bank_account_delete_failed = "man.cashflow.message.bank_account_delete_failed";
        public const string man_cashflow_message_bank_account_list_failed = "man.cashflow.message.bank_account_list_failed";
        public const string man_cashflow_message_bank_account_update_failed = "man.cashflow.message.bank_account_update_failed";
        public const string man_cashflow_message_account_type_required = "man.cashflow.message.account_type_required";
        public const string man_cashflow_message_wallet_delete_failed = "man.cashflow.message.wallet_delete_failed";
        public const string man_cashflow_message_wallet_not_found = "man.cashflow.message.wallet_not_found";
        public const string man_cashflow_message_wallet_list_failed = "man.cashflow.message.wallet_list_failed";
        public const string man_cashflow_message_wallet_account_exists = "man.cashflow.message.wallet_account_exists";
        public const string man_cashflow_message_wallet_update_failed = "man.cashflow.message.wallet_update_failed";
        public const string man_product_validate_create_code_max_length_50 = "man.product.validate.create.code.max_length_50";
        public const string man_product_validate_create_name_required = "man.product.validate.create.name.required";
        public const string man_product_validate_create_name_max_length_255 = "man.product.validate.create.name.max_length_255";
        public const string man_product_validate_create_category_id__more_than_zero = "man.product.validate.create.category_id__more_than_zero";
        public const string man_product_validate_create_description_max_length_2000 = "man.product.validate.create.description.max_length_2000";
        public const string man_product_validate_create_base_price_min = "man.product.validate.create.base_price.min";
        public const string man_product_validate_create_tax_rate_range = "man.product.validate.create.tax_rate.range";
        public const string man_product_validate_create_unit_max_length = "man.product.validate.create.unit.max_length";
        public const string man_product_validate_create_conversion_value_min = "man.product.validate.create.conversion_value.min";
        public const string man_product_validate_create_min_quantity_min = "man.product.validate.create.min_quantity.min";
        public const string man_product_validate_create_max_quantity_min = "man.product.validate.create.max_quantity.min";
        public const string man_product_validate_create_max_quantity_gte_min_quantity = "man.product.validate.create.max_quantity.gte_min_quantity";
        public const string man_product_validate_create_cost_min = "man.product.validate.create.cost.min";
        public const string man_product_validate_create_on_hand_quantity_min = "man.product.validate.create.on_hand_quantity.min";
        public const string man_product_validate_create_pricebook_ids_more_than_zero = "man.product.validate.create.pricebook_ids_more_than_zero";
        public const string man_product_validate_create_pricebook_prices_min = "man.product.validate.create.pricebook_prices.min";
        public const string man_product_validate_create_image_urls_not_empty = "man.product.validate.create.image_urls.not_empty";
        public const string man_product_validate_create_product_type_id_positive_optional = "man.product.validate.create.product_type_id.positive_optional";
        public const string man_product_validate_create_product_group_id_positive_optional = "man.product.validate.create.product_group_id.positive_optional";
        public const string man_product_validate_create_product_group_id_invalid = "man.product.validate.create.product_group_id.invalid";
        public const string man_product_validate_create_tax_id_positive_optional = "man.product.validate.create.tax_id.positive_optional";
        public const string man_product_validate_delete_product_id_required = "man.product.validate.delete.product_id.required";
        public const string man_product_validate_delete_product_id__more_than_zero = "man.product.validate.delete.product_id__more_than_zero";
        public const string man_product_validate_general_search_key_required = "man.product.validate.general.search_key_required";
        public const string man_product_validate_general_search_key_not_empty = "man.product.validate.general.search_key_not_empty";
        public const string man_product_validate_general_limit_more_than_zero = "man.product.validate.general.limit_more_than_zero";
        public const string man_product_validate_delete_product_id_more_than_zero = "man.product.validate.delete.product_id_more_than_zero";
        public const string man_product_validate_update_product_id_required = "man.product.validate.update.product_id.required";
        public const string man_product_validate_update_product_id_more_than_zero = "man.product.validate.update.product_id_more_than_zero";
        public const string man_product_validate_update_code_required = "man.product.validate.update.code.required";
        public const string man_product_validate_update_code_max_length = "man.product.validate.update.code.max_length";
        public const string man_product_validate_update_name_required = "man.product.validate.update.name.required";
        public const string man_product_validate_update_name_max_length = "man.product.validate.update.name.max_length";
        public const string man_product_validate_update_category_id_more_than_zero = "man.product.validate.update.category_id_more_than_zero";
        public const string man_product_validate_update_base_price_min = "man.product.validate.update.base_price.min";
        public const string man_product_validate_update_weight_min = "man.product.validate.update.weight.min";
        public const string man_product_validate_update_product_group_id_positive_optional = "man.product.validate.update.product_group_id.positive_optional";
        public const string man_product_validate_update_product_group_id_invalid = "man.product.validate.update.product_group_id.invalid";
        public const string man_product_validate_update_tax_id_more_than_zero = "man.product.validate.update.tax_id_more_than_zero";
        public const string man_product_validate_update_order_template_max_length = "man.product.validate.update.order_template.max_length";
        public const string man_product_validate_update_description_max_length = "man.product.validate.update.description.max_length";
        public const string man_product_validate_update_images_max_count = "man.product.validate.update.images.max_count";
        public const string man_product_validate_update_on_hand_quantity_min = "man.product.validate.update.on_hand_quantity.min";
        public const string man_product_validate_update_min_quantity_min = "man.product.validate.update.min_quantity.min";
        public const string man_product_validate_update_max_quantity_min = "man.product.validate.update.max_quantity.min";
        public const string man_product_validate_update_cost_min_on_hand = "man.product.validate.update.cost.min_on_hand";
        public const string man_product_validate_update_pricebooks_required = "man.product.validate.update.pricebooks.required";
        public const string man_product_validate_update_pricebooks_min_items = "man.product.validate.update.pricebooks.min_items";
        public const string man_product_validate_update_combo_group_name_required = "man.product.validate.update.combo_group.name.required";
        public const string man_product_validate_update_combo_group_name_max_length = "man.product.validate.update.combo_group.name.max_length";
        public const string man_product_validate_update_combo_group_items_min_items = "man.product.validate.update.combo_group.items.min_items";
        public const string man_product_validate_update_sale_branch_product_id_positive = "man.product.validate.update.sale_branch.product_id.positive";
        public const string man_product_validate_update_toppings_required = "man.product.validate.update.toppings.required";
        public const string man_product_validate_update_toppings_min_items = "man.product.validate.update.toppings.min_items";
        public const string man_product_validate_update_request_not_empty = "man.product.validate.update.request.not_empty";
        public const string man_product_validate_update_request_has_update_item = "man.product.validate.update.request.has_update_item";
        public const string man_product_msg_create_validation_failed = "man.product.msg.create.validation_failed";
        public const string man_product_msg_create_category_not_found = "man.product.msg.create.category_not_found";
        public const string man_product_msg_create_create_failed = "man.product.msg.create.create_failed";
        public const string man_product_msg_create_combo_groups_required = "man.product.msg.create.combo_groups_required";
        public const string man_product_msg_create_combo_validation_failed = "man.product.msg.create.combo_validation_failed";
        public const string man_product_msg_create_missing_product_ids = "man.product.msg.create.missing_product_ids";
        public const string man_product_msg_create_product_missing = "man.product.msg.create.product_missing";
        public const string man_product_msg_create_product_deleted = "man.product.msg.create.product_deleted";
        public const string man_product_msg_create_combo_mark_missing = "man.product.msg.create.combo_mark_missing";
        public const string man_product_msg_create_service_invalid_combo = "man.product.msg.create.service_invalid_combo";
        public const string man_product_msg_create_invalid_product_type = "man.product.msg.create.invalid_product_type";
        public const string man_product_msg_delete_product_not_found = "man.product.msg.delete.product_not_found";
        public const string man_product_msg_delete_product_deleted = "man.product.msg.delete.product_deleted";
        public const string man_product_msg_general_product_detail_not_found = "man.product.msg.general.product_detail_not_found";
        public const string man_product_msg_general_internal_server_error = "man.product.msg.general.internal_server_error";
        public const string man_product_msg_update_product_not_found = "man.product.msg.update.product_not_found";
        public const string man_product_msg_update_code_exists = "man.product.msg.update.code_exists";
        public const string man_product_msg_update_category_not_found = "man.product.msg.update.category_not_found";
        public const string man_product_msg_update_tax_not_found = "man.product.msg.update.tax_not_found";
        public const string man_product_msg_update_product_not_found_images = "man.product.msg.update.product_not_found_images";
        public const string man_product_msg_update_branch_not_found = "man.product.msg.update.branch_not_found";
        public const string man_product_msg_update_product_not_found_quantity = "man.product.msg.update.product_not_found_quantity";
        public const string man_product_msg_update_deleted_product_no_update = "man.product.msg.update.deleted_product_no_update";
        public const string man_product_msg_update_service_combo_no_inventory_update = "man.product.msg.update.service_combo_no_inventory_update";
        public const string man_product_msg_update_no_tracking_no_inventory_update = "man.product.msg.update.no_tracking_no_inventory_update";
        public const string man_product_msg_update_avg_cost_no_update = "man.product.msg.update.avg_cost_no_update";
        public const string man_product_msg_update_product_not_found_pricebooks = "man.product.msg.update.product_not_found_pricebooks";
        public const string man_product_msg_update_product_deleted_pricebooks = "man.product.msg.update.product_deleted_pricebooks";
        public const string man_product_msg_update_pricebooks_not_found = "man.product.msg.update.pricebooks_not_found";
        public const string man_product_msg_update_pricebooks_deleted = "man.product.msg.update.pricebooks_deleted";
        public const string man_product_msg_update_pricebooks_inactive = "man.product.msg.update.pricebooks_inactive";
        public const string man_product_msg_update_product_not_found_sale_branches = "man.product.msg.update.product_not_found_sale_branches";
        public const string man_product_msg_update_product_not_found_combo = "man.product.msg.update.product_not_found_combo";
        public const string man_product_msg_update_product_not_combo = "man.product.msg.update.product_not_combo";
        public const string man_product_msg_update_product_not_in_tenant = "man.product.msg.update.product_not_in_tenant";
        public const string man_product_msg_update_combo_groups_required_update = "man.product.msg.update.combo_groups_required_update";
        public const string man_product_msg_update_combo_group_not_found = "man.product.msg.update.combo_group_not_found";
        public const string man_product_msg_update_combo_item_not_found = "man.product.msg.update.combo_item_not_found";
        public const string man_product_msg_update_product_not_found_toppings = "man.product.msg.update.product_not_found_toppings";
        public const string man_product_msg_update_topping_product_not_found = "man.product.msg.update.topping_product_not_found";
        public const string man_product_msg_update_invalid_topping = "man.product.msg.update.invalid_topping";
        public const string man_product_validate_update_combo_groups_min_items = "man.product.validate.update.combo_groups.min_items";
    }
}
