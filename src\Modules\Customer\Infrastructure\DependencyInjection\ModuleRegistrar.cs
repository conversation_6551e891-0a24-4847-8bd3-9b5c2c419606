using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeleteCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetCustomerDetail;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Modules.Customer.Infrastructure.Mapping;
using KvFnB.Modules.Customer.Infrastructure.Persistence;
using KvFnB.Shared.AuditTrailMessagePublisher;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace KvFnB.Modules.Customer.Infrastructure.DependencyInjection
{
    public static class CustomerModule
    {
        public static IServiceCollection AddCustomerModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register user cases Customer
            services.AddScoped<GetListCustomerUseCase>();
            services.AddScoped<GetCustomerDetailUseCase>();
            services.AddScoped<DeleteCustomerUseCase>();
            services.AddScoped<DeactivateCustomerUseCase>();
            services.AddScoped<ActivateCustomerUseCase>();
            services.AddScoped<CreateCustomerUseCase>();
            services.AddScoped<IValidator<GetListCustomerRequest>, GetListCustomerValidator>();
            services.AddScoped<IValidator<DeleteCustomerRequest>, DeleteCustomerValidator>();
            services.AddScoped<IValidator<DeactivateCustomerRequest>, DeactivateCustomerValidator>();
            services.AddScoped<IValidator<ActivateCustomerRequest>, ActivateCustomerValidator>();
			services.AddScoped<IValidator<CreateCustomerRequest>, CreateCustomerValidator>();

            // Register repositories
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ICustomerGroupRepository, CustomerGroupRepository>();

            // Ensure AuditTrailService is registered
            services.TryAddScoped<IAuditTrailService, AuditTrailService>();

            // Register Domain Services
            services.AddAutoMapper(typeof(CustomerMappingProfile));

            return services;
        }
    }
}