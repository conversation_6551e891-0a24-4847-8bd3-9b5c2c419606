# Get List Product API - Technical Specification

## Overview
The Get List Product API provides a flexible and comprehensive way to retrieve product data with filtering, sorting, and pagination capabilities. It is designed to support complex queries including attribute-based filtering, product type filtering, and multi-branch scenarios.

## Endpoint
- **URL**: `/api/Product`
- **Method**: `GET`
- **Required Permissions**: `Product_Read`

## Request Parameters
The API accepts the following query parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| Skip | int | No | 0 | Number of records to skip for pagination |
| Take | int | No | 10 | Number of records to return per page |
| ListAttributeFilter | Array of AttributeDto | No | null | List of product attributes to filter by |
| MasterProductId | long | No | null | Filter by master product ID |
| CategoryIds | string | No | null | Comma-separated list of category IDs |
| ProductGroups | int[] | No | null | Array of product group IDs |
| ProductTypes | int[] | No | null | Array of product type IDs for filtering |
| IsActive | bool | No | null | Filter by active status |
| IsRewardPoint | bool | No | null | Filter by reward point status |
| OnhandFilter | int | No | null | Filter by onhand quantity rules |
| OrderBy | string | No | "CreatedDate" | Field to order results by |
| Keyword | string | No | null | Search keyword for product name or code |
| AllowsSale | bool | No | null | Filter by whether product allows sales |
| FilterTaxId | long | No | null | Filter by tax ID |
| ShelvesIds | string | No | null | Comma-separated list of shelf IDs |
| BranchIds | long[] | No | null | Array of branch IDs to filter products by |
| Reverse | bool | No | true | Whether to reverse the sort order |

### Special Note for Attribute Filtering
Attribute filters can be sent in the format:
```
attribute[0].id=123&attribute[0].name=Color&attribute[0].value=Red
attribute[1].id=456&attribute[1].name=Size&attribute[1].value=Large
```

## Processing Flow
1. **Controller Layer**:
   - Extracts attribute parameters from query string if not bound automatically
   - Retrieves GetListProductUseCase from service container
   - Executes the use case and returns appropriate HTTP response

2. **Use Case Layer**:
   - Creates SQL parameters from the request object
   - Executes stored procedure to retrieve product data
   - Maps database result to DTOs
   - Enriches product data with additional information:
     - Product attributes
     - Same type products
     - Product unit information
   - Constructs and returns final response

## Key Implementation Details

### Product Type Filtering
The API handles complex product type filtering logic:
- Separates standard product types from special types (Processed Goods, Manufacturing)
- Handles Customizable Combo (ID: 6) as a special case
- Supports multiple product type filtering in a single request

### Multi-Tenant Support
- Uses tenant provider to automatically filter by retailer ID
- Includes branch ID filtering for multi-branch retailers

### Data Enrichment
After retrieving the base product data, the API:
1. **Retrieves Product Attributes**: Both direct attributes and attributes of related products
2. **Identifies Same-Type Products**: Calculates and includes count of products with same master product
3. **Gathers Unit Information**: Retrieves and includes unit list data for each product

### Error Handling
- Implements standardized error handling with proper logging
- Returns appropriate HTTP status codes
- Returns general error messages rather than exposing detailed exception information

## Response Structure

### Success Response (200 OK)
```json
{
  "data": [
    {
      "id": 123,
      "name": "Product Name",
      "code": "PROD-123",
      "basePrice": 10.99,
      "onHandQuantity": 100,
      "isActive": true,
      "productType": 1,
      "groupProductType": 1,
      "masterProductId": null,
      "productAttributes": [
        {
          "id": 456,
          "attributeId": 789,
          "attributeName": "Color",
          "productId": 123,
          "masterCode": "MASTER-001",
          "attributeValue": "Red",
          "createdDate": "2023-01-01T00:00:00Z"
        }
      ],
      "sameTypeProductCount": 5,
      "unitList": [
        {
          "productId": 123,
          "unitName": "Each",
          "masterUnitId": null,
          "basePrice": 10.99
        }
      ]
    }
  ],
  "total": 100,
  "totalOnHand": 500,
  "totalReserved": 50,
  "totalProduct": 100
}
```

### Error Response (400 Bad Request)
```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "Bad Request",
  "status": 400,
  "detail": "Error message"
}
```

### Error Response (500 Internal Server Error)
```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.6.1",
  "title": "Internal Server Error",
  "status": 500,
  "detail": "An unexpected error occurred"
}
```

## Database Interaction
The API primarily interacts with the database through a stored procedure:
- **Stored Procedure**: `pr_Product_Group`
- **Input Parameters**: Filter conditions, pagination options, sort options
- **Output Parameters**:
  - `@totalRow`: Total number of rows matching the query
  - `@totalOnHand`: Total on-hand quantity across all matching products
  - `@totalReserved`: Total reserved quantity across all matching products
  - `@totalProduct`: Total count of all products (may differ from totalRow)

## Performance Considerations
- Uses `NOLOCK` hints for read operations to prevent blocking
- Limits batch sizes with pagination
- Uses parameterized queries to prevent SQL injection
- Optimizes SQL queries with appropriate indexes
- Uses CTE (Common Table Expression) for complex queries

## Security Considerations
- Implements permission-based access control
- Properly sanitizes and validates all input parameters
- Uses tenant isolation to prevent cross-tenant data access
- Does not expose sensitive internal error details to clients

## Special Notes for Customizable Combo Products
When filtering for CustomizableCombo product type (ID: 6):
- The system includes this in the product type filtering logic
- Products of CustomizableCombo type can be retrieved alongside other product types

## Integration with Other Systems
- Uses `ITenantProvider` for multi-tenant data isolation
- Uses `ICurrencyProvider` for proper currency rounding
- Integrates with the permission system for access control 