using System.Text.Json;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Application.Dtos;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Models;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog.Context;

namespace KvFnB.Modules.DataManagement.Application.UseCases.ScanScheduledRequestsUseCase
{
    /// <summary>
    /// Use case for scanning scheduled data deletion requests and preparing them for execution
    /// </summary>
    public class ScanScheduledRequestsUseCase : UseCaseBase<ScanScheduledRequestsRequest, ScanScheduledRequestsResponse>
    {

        private readonly IDataManagementQueryService _queryService;
        private readonly IMessageQueueService _messageQueueService;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IConfiguration _configuration;
        private readonly List<string> adjustTypeIgnoreMultiBranchs = ["AdjustmentCustomer", "AdjustmentSupplier", "AdjustmentPartnerDelivery"];
        /// <summary>
        /// Initializes a new instance of the <see cref="ScanScheduledRequestsUseCase"/> class.
        /// </summary>
        /// <param name="validator">The validator for the request</param>
        /// <param name="messageQueueService">The message queue service</param>
        /// <param name="logger">The logger</param>
        public ScanScheduledRequestsUseCase(
            IDataManagementQueryService queryService,
            IMessageQueueService messageQueueService,
            IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration)
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _messageQueueService = messageQueueService ?? throw new ArgumentNullException(nameof(messageQueueService));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Executes the use case to scan for scheduled data deletion requests that are due for execution
        /// </summary>
        /// <param name="request">The request containing the current time</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A result containing information about the processed requests</returns>
        public override async Task<Result<ScanScheduledRequestsResponse>> ExecuteAsync(
            ScanScheduledRequestsRequest request,
            CancellationToken cancellationToken = default)
        {

            // Get scheduled requests due for execution
            var dueRequests = await GetScheduledRequestsDueForExecutionAsync(
                request.CurrentTime,
                cancellationToken);

            if (dueRequests.ToList().Count == 0) 
            {
                return Result<ScanScheduledRequestsResponse>.Success(new ScanScheduledRequestsResponse());
            }

            int processedCount = 0;
            var errors = new List<string>();

            foreach (var deleteRequest in dueRequests)
            {
                await InitScope(deleteRequest, request, cancellationToken);
                processedCount++;
            }
            return Result<ScanScheduledRequestsResponse>.Success(new ScanScheduledRequestsResponse
            {
                ProcessedCount = processedCount,
                Errors = errors
            });
        }

        private async Task InitScope(DeleteDataRequest deleteRequest, ScanScheduledRequestsRequest request, CancellationToken cancellationToken)
        {

            using (LogContext.PushProperty("RetailerId", deleteRequest.TenantId))
            using (LogContext.PushProperty("RequestId", deleteRequest.Id))
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var tenantProvider = scope.ServiceProvider.GetRequiredService<ITenantProvider>();
                var authUser = scope.ServiceProvider.GetRequiredService<IInitUserInfo>();
                authUser.InitUserInfo(deleteRequest.CreatedBy, deleteRequest.TenantId);

                tenantProvider.InitTenantInfo(request.GroupId, deleteRequest.TenantId);
                await SaveDeleteDataRequest(scope, deleteRequest, request, tenantProvider, cancellationToken);  
            }
            
        }

        private async Task SaveDeleteDataRequest(IServiceScope scope, DeleteDataRequest deleteRequest, ScanScheduledRequestsRequest request, ITenantProvider tenantProvider, CancellationToken cancellationToken)
        {
            if(string.IsNullOrEmpty(deleteRequest.BranchIds)) return;
            var deleteDataRequestRepository = scope.ServiceProvider.GetRequiredService<IDeleteDataRequestRepository>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            var createdJob = await AddProcessingDataJobAsync(scope, unitOfWork, deleteRequest, cancellationToken);

            // Add DeleteDataDetail entries for transaction tables
            AddDeleteDataDetailsAsync(deleteRequest, createdJob);
            AddAdjustmentDataDetailsAsync(deleteRequest, createdJob);
            // Calculate next execution date
            if(!string.IsNullOrEmpty(deleteRequest.ScheduleConfig))
            {
                var scheduleConfig = JsonSerializer.Deserialize<ScheduleConfig>(deleteRequest.ScheduleConfig ?? "{}") ?? new ScheduleConfig();
                var nextExecutionDate = deleteRequest.CalculateNextExecutionDate(scheduleConfig, deleteRequest.ExecuteDate ?? request.CurrentTime);
                var filterCondition = JsonSerializer.Deserialize<FilterCondition>(deleteRequest.FilterCondition ?? "{}") ?? new FilterCondition();
                filterCondition.UpdateToDate(nextExecutionDate);
                var filterConditionJson = JsonSerializer.Serialize(filterCondition);
                deleteRequest.UpdateFilterCondition(filterConditionJson);
                // Update the request with the new execution date
                deleteRequest.UpdateNextExecutionDate(nextExecutionDate);
            }
            else
            {
                deleteRequest.UpdateStatus(DeleteDataRequestStatus.Completed);
            }
            await deleteDataRequestRepository.UpdateAsync(deleteRequest, cancellationToken);
            await unitOfWork.CommitAsync(cancellationToken);
            
            // Enqueue the job for processing
            await ProducerAsync(deleteRequest, tenantProvider, cancellationToken);

        }

        private async Task ProducerAsync(DeleteDataRequest deleteRequest, ITenantProvider tenantProvider, CancellationToken cancellationToken)
        {
            var filterCondition = JsonSerializer.Deserialize<FilterCondition>(deleteRequest.FilterCondition ?? "{}") ?? new FilterCondition();
            if(filterCondition.ToDate == null) filterCondition.UpdateToDate(deleteRequest.ExecuteDate);
            foreach (var detail in deleteRequest.DeleteDataDetails)
            {
                var dataProcessingMessage = new DataProcessingMessage()
                {
                    Id = detail.Id,
                    RequestId = deleteRequest.Id,
                    RetailerId = deleteRequest.TenantId,
                    BranchId = detail.BranchId,
                    GroupId = tenantProvider.GetShardId() ?? 0,
                    Type = detail.Type,
                    FilterCondition = filterCondition
                };
                await _messageQueueService.ProducerAsync(dataProcessingMessage, cancellationToken);
            }
        }

        private static async Task<ProcessingDataJob> AddProcessingDataJobAsync(IServiceScope scope, IUnitOfWork unitOfWork, DeleteDataRequest deleteRequest, CancellationToken cancellationToken)
        {
            var ProcessingDataJobRepository = scope.ServiceProvider.GetRequiredService<IProcessingDataJobRepository>();
            // Create a new job
            var job = new ProcessingDataJob
            {
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                TenantId = deleteRequest.TenantId
            };

            // Save the job
            await ProcessingDataJobRepository.AddAsync(job, cancellationToken);
            await unitOfWork.CommitAsync(cancellationToken);
            
            return job;
        }

        private void AddDeleteDataDetailsAsync(DeleteDataRequest deleteRequest, ProcessingDataJob job)
        {
            // For each transaction table in the configuration, create a DeleteDataDetail entry
            var tablesToDelete = _configuration.GetSection("DataDeletion:MainTransactionTables").Get<string[]>() ?? Array.Empty<string>();
            foreach (var tableName in tablesToDelete)
            {
                // Create filter condition JSON if needed (could be extended based on tableName)
                var filterCondition = JsonSerializer.Deserialize<FilterCondition>(deleteRequest.FilterCondition ?? "{}") ?? new FilterCondition();
                if(filterCondition != null && filterCondition.ToDate == null)
                    filterCondition.ToDate = deleteRequest.ExecuteDate;
                // Create and add DeleteDataDetail for this table
                foreach (var branch in deleteRequest.BranchIds?.Split(',') ?? Array.Empty<string>())
                {
                    deleteRequest.AddDeleteDataDetail(DeleteDataDetail.Create(
                        deleteRequest.Id,
                        deleteRequest.TenantId,
                        long.Parse(branch),
                        job.Id,
                        tableName,
                        JsonSerializer.Serialize(filterCondition)
                    ));
                }
            }
        }

        private void AddAdjustmentDataDetailsAsync(DeleteDataRequest deleteRequest, ProcessingDataJob job)
        {
            // For each transaction table in the configuration, create a DeleteDataDetail entry
            var adjustmentTables = _configuration.GetSection("DataDeletion:AdjustmentTables").Get<string[]>() ?? Array.Empty<string>();
            // Create filter condition JSON if needed (could be extended based on tableName)
            var filterCondition = JsonSerializer.Deserialize<FilterCondition>(deleteRequest.FilterCondition ?? "{}") ?? new FilterCondition();
            if (filterCondition != null && filterCondition.ToDate == null)
                filterCondition.ToDate = deleteRequest.ExecuteDate;
            foreach (var tableName in adjustmentTables)
            {
                // Create and add DeleteDataDetail for this table
                var branchIds = deleteRequest.BranchIds?.Split(',').ToList() ?? [];
                if (branchIds != null && branchIds.Count != 0)
                {
                    if (adjustTypeIgnoreMultiBranchs.Contains(tableName))
                    {
                        var branchId = deleteRequest.BranchIds?.Split(',').FirstOrDefault();
                        deleteRequest.AddDeleteDataDetail(DeleteDataDetail.Create(
                                deleteRequest.Id,
                                deleteRequest.TenantId,
                                long.Parse(branchId ?? "0"),
                                job.Id,
                                tableName,
                                JsonSerializer.Serialize(filterCondition)
                            ));
                    } else
                    {
                        foreach (var branch in deleteRequest.BranchIds?.Split(',') ?? Array.Empty<string>())
                        {
                            deleteRequest.AddDeleteDataDetail(DeleteDataDetail.Create(
                                deleteRequest.Id,
                                deleteRequest.TenantId,
                                long.Parse(branch),
                                job.Id,
                                tableName,
                                JsonSerializer.Serialize(filterCondition)
                            ));
                        }
                    }
                        
                }
                
            }
        }

        private async Task<IEnumerable<DeleteDataRequest>> GetScheduledRequestsDueForExecutionAsync(DateTime currentTime, CancellationToken cancellationToken)
        {
            var query = $@"SELECT RetailerId as TenantId, CreatedDate as CreatedAt, * FROM DeleteDataRequest
                         WHERE Status = @Status AND 
                         Type = @Type AND 
                         ExecuteDate < @CurrentTime";
            var parameters = new { CurrentTime = currentTime.Date.AddDays(1), Status = DeleteDataRequestStatus.Approved, Type = DeleteDataScheduleType.Scheduled };
            return await _queryService.QueryPlainTextAsync<DeleteDataRequest>(query, parameters, cancellationToken);
        }
    }
}