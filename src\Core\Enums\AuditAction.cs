namespace KvFnB.Core.Enums
{
    /// <summary>
    /// Represents the action types for audit logging
    /// </summary>
    public enum AuditAction
    {
        /// <summary>
        /// View or read action
        /// </summary>
        View = 1,
        
        /// <summary>
        /// Create or add action
        /// </summary>
        Create = 2,
        
        /// <summary>
        /// Update or modify action
        /// </summary>
        Update = 3,
        
        /// <summary>
        /// Delete action
        /// </summary>
        Delete = 4,
        
        /// <summary>
        /// Login action
        /// </summary>
        Login = 5,
        
        /// <summary>
        /// Logout action
        /// </summary>
        Logout = 6,
        
        /// <summary>
        /// Import action
        /// </summary>
        Import = 7,
        
        /// <summary>
        /// Export action
        /// </summary>
        Export = 8,
        
        /// <summary>
        /// Print action
        /// </summary>
        Print = 9,
        
        /// <summary>
        /// Approve action
        /// </summary>
        Approve = 10,
        
        /// <summary>
        /// Reject action
        /// </summary>
        Reject = 11
    }
} 