using System.Threading.Tasks;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Service for rendering email templates
    /// </summary>
    public interface ITemplateRenderer
    {
        /// <summary>
        /// Renders the subject line for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered subject</returns>
        Task<string> RenderSubjectAsync(string templateId, object data);
        
        /// <summary>
        /// Renders the HTML body for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered HTML body</returns>
        Task<string> RenderHtmlBodyAsync(string templateId, object data);
        
        /// <summary>
        /// Renders the plain text body for an email template
        /// </summary>
        /// <param name="templateId">The template identifier</param>
        /// <param name="data">Data to populate the template</param>
        /// <returns>The rendered text body</returns>
        Task<string> RenderTextBodyAsync(string templateId, object data);
    }
} 