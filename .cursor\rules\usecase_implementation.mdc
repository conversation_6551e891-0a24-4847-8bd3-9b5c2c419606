---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
# Use Case Implementation Guidelines

## Overview
This document outlines the standardized approach for implementing use cases in the KvFnB Core project, following our hexagonal architecture pattern and Domain-Driven Design principles.

## Directory Structure
```
src/Modules/{ModuleName}/
├── Application/
│   ├── Contracts/
│   │   └── {EntityName}Dto.cs
│   └── UseCases/{EntityName}UseCase/{UseCaseName}/
│       ├── {UseCaseName}Request.cs
│       ├── {UseCaseName}Response.cs
│       ├── {UseCaseName}Validator.cs
│       └── {UseCaseName}UseCase.cs
├── Infrastructure/
│   ├── DependencyInjection/
│   │   └── ModuleRegistrar.cs
│   └── Mapping/
│       └── {ModuleName}MappingProfile.cs
└── Tests/
    └── Application/UseCases/{EntityName}UseCase/{UseCaseName}/
        └── {UseCaseName}UseCaseTests.cs
```

## IMPORTANT: Required Implementation Components

For each use case, you MUST implement ALL of the following components:

1. **DTO Models** - Define in separate files in the Contracts folder
2. **Request/Response Models** - Define in the UseCase folder
3. **Validator** - Implement validation rules for the request
4. **Use Case Implementation** - Implement the business logic
5. **Dependency Injection Registration** - Register in the ModuleRegistrar (in Infrastructure/DependencyInjection folder)
6. **Mapping Configuration** - Configure in the MappingProfile (in Infrastructure/Mapping folder)
7. **Unit Tests** - Implement tests for all scenarios

Skipping any of these components will result in incomplete functionality and potential issues.

## Component Templates

### DTO Models Template (in Contracts folder)
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.{ModuleName}.Application.Contracts
{
    /// <summary>
    /// Represents a {EntityName} data transfer object
    /// </summary>
    public record {EntityName}Dto
    {
        /// <summary>
        /// Property documentation
        /// </summary>
        [Required]
        [JsonPropertyName("property_name")]
        public string PropertyName { get; init; } = string.Empty;
        
        // Other properties with annotations
    }
}
```

### Request Model Template
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using KvFnB.Modules.{ModuleName}.Application.Contracts;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName}
{
    /// <summary>
    /// Represents the request model for the {UseCaseName} use case
    /// </summary>
    public record {UseCaseName}Request
    {
        /// <summary>
        /// Property documentation
        /// </summary>
        [Required]
        [JsonPropertyName("property_name")]
        public string PropertyName { get; init; } = string.Empty;
        
        // Other properties with annotations
    }
}
```

### Response Model Template
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.{ModuleName}.Application.Contracts;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName}
{
    /// <summary>
    /// Represents the response model for the {UseCaseName} use case
    /// </summary>
    public record {UseCaseName}Response
    {
        /// <summary>
        /// Property documentation
        /// </summary>
        [JsonPropertyName("property_name")]
        public string PropertyName { get; init; } = string.Empty;
        
        // Other properties with annotations
    }
}
```

### Validator Template
```csharp
using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using KvFnB.Modules.{ModuleName}.Application.Contracts;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName}
{
    /// <summary>
    /// Validates the {UseCaseName} request
    /// </summary>
    public class {UseCaseName}Validator : Validator<{UseCaseName}Request>
    {
        public {UseCaseName}Validator()
        {
            // Simple property validation
            RuleFor(x => x.PropertyName)
                .NotNull("Property is required")
                .NotEmpty("Property cannot be empty");
                
            // Collection validation using RuleForEach
            RuleFor(x => (IEnumerable<ItemDto>)x.Items)
                .ForEach(item =>
                {
                    item.Rule(x => x.Id)
                        .GreaterThan(0, "Item ID must be greater than zero");
                        
                    item.Rule(x => x.Name)
                        .NotEmpty("Item name is required")
                        .MaxLength(100, "Item name must be less than 100 characters");
                });
        }
    }
}
```

### Use Case Implementation Template

#### Query Use Case (Get/Read Operations)
Need using IQueryService _queryService to query data.
```csharp
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.{ModuleName}.Application.Contracts;
using KvFnB.Shared.DapperQuery;
using KvFnB.Shared.MultiTenancy;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName}
{
    /// <summary>
    /// Implements the {UseCaseName} use case for querying data
    /// </summary>
    public class {UseCaseName}UseCase : UseCaseBase<{UseCaseName}Request, {UseCaseName}Response>
    {
        private readonly IQueryService _queryService;
        private readonly IMapper _mapper;
        private readonly ITenantProvider _tenantProvider;
        // Other dependencies

        public {UseCaseName}UseCase(
            IValidator<{UseCaseName}Request> validator,
            IQueryService queryService,
            IMapper mapper,
            ITenantProvider tenantProvider
            // Other dependencies
        )
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            // Initialize other dependencies
        }

        public override async Task<Result<{UseCaseName}Response>> ExecuteAsync(
            {UseCaseName}Request request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Business logic implementation
                // Note: Request validation is handled by the base class
                
                // Create query using QueryBuilder
                var query = new QueryBuilder()
                    .SelectFrom("{TableName} t")
                    .Where("t.RetailerId = @RetailerId", new { RetailerId = _tenantProvider.GetTenantId() })
                    .Where("t.Id = @Id", new { Id = request.Id });
                    
                // Execute query using Dapper through IQueryService
                var entity = await _queryService.QueryFirstOrDefaultAsync<dynamic>(query);
                
                if (entity == null)
                {
                    return Result<{UseCaseName}Response>.Failure($"{EntityName} with ID {request.Id} not found");
                }
                
                // Map to response
                var response = _mapper.Map<{UseCaseName}Response>(entity);
                
                // Return success result
                return Result<{UseCaseName}Response>.Success(response);
            }
            catch (Exception ex)
            {
                // Handle exceptions
                return Result<{UseCaseName}Response>.Failure(ex.Message);
            }
        }
    }
}
```


#### Command Use Case (Create/Update/Delete Operations)
Need using IRepository _repository to update data.
```csharp
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.{ModuleName}.Application.Contracts;
using KvFnB.Modules.{ModuleName}.Domain.Repositories;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName}
{
    /// <summary>
    /// Implements the {UseCaseName} use case for modifying data
    /// </summary>
    public class {UseCaseName}UseCase : UseCaseBase<{UseCaseName}Request, {UseCaseName}Response>
    {
        private readonly I{EntityName}Repository _repository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        // Other dependencies

        public {UseCaseName}UseCase(
            IValidator<{UseCaseName}Request> validator,
            I{EntityName}Repository repository,
            IUnitOfWork unitOfWork,
            IMapper mapper
            // Other dependencies
        )
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            // Initialize other dependencies
        }

        public override async Task<Result<{UseCaseName}Response>> ExecuteAsync(
            {UseCaseName}Request request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Business logic implementation
                // Note: Request validation is handled by the base class
                
                // For update operations, first check if entity exists
                if (request.Id.HasValue)
                {
                    var existingEntity = await _repository.GetAsync(request.Id.Value, cancellationToken);
                    if (existingEntity == null)
                    {
                        return Result<{UseCaseName}Response>.Failure($"{EntityName} with ID {request.Id} not found");
                    }
                }
                
                // Map request to entity
                var entity = _mapper.Map<{EntityName}>(request);
                
                // Perform repository operation
                if (request.Id.HasValue)
                {
                    // Update
                    entity = await _repository.UpdateAsync(entity, cancellationToken);
                }
                else
                {
                    // Create
                    entity = await _repository.AddAsync(entity, cancellationToken);
                }
                
                // Commit changes using unit of work
                await _unitOfWork.CommitAsync(cancellationToken);
                
                // Map to response
                var response = _mapper.Map<{UseCaseName}Response>(entity);
                
                // Return success result
                return Result<{UseCaseName}Response>.Success(response);
            }
        }
    }
}

```



### Mapping Profile Template (in Infrastructure/Mapping folder)
```csharp
using KvFnB.Modules.{ModuleName}.Application.Contracts;
using KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName};
using KvFnB.Modules.{ModuleName}.Domain.Models;

namespace KvFnB.Modules.{ModuleName}.Infrastructure.Mapping
{
    public class {ModuleName}MappingProfile : Profile
    {
        public {ModuleName}MappingProfile()
        {
            // Entity to DTO mappings
            CreateMap<{EntityName}, {EntityName}Dto>();
            
            // DTO to Entity mappings
            CreateMap<{EntityName}Dto, {EntityName}>();
            
            // Request/Response mappings
            CreateMap<{UseCaseName}Request, {EntityName}>();
            CreateMap<{EntityName}, {UseCaseName}Response>();
        }
    }
}
```

### Module Registrar Template (in Infrastructure/DependencyInjection folder)
```csharp
using KvFnB.Core.Validation;
using KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName};
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.{ModuleName}.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static IServiceCollection Register{ModuleName}Module(this IServiceCollection services)
        {
            // Register use cases
            services.AddScoped<{UseCaseName}UseCase>();
            
            // Register validators
            services.AddScoped<IValidator<{UseCaseName}Request>, {UseCaseName}Validator>();
            
            // Register other dependencies
            
            return services;
        }
    }
}
```

### Unit Test Template
#### Query Use Case Tests
```csharp
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName};
using KvFnB.Shared.DapperQuery;
using KvFnB.Shared.MultiTenancy;
using Moq;
using Xunit;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase
{
    public class {UseCaseName}UseCaseTests
    {
        private readonly Mock<IValidator<{UseCaseName}Request>> _validatorMock;
        private readonly Mock<IQueryService> _queryServiceMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly {UseCaseName}UseCase _useCase;

        public {UseCaseName}UseCaseTests()
        {
            _validatorMock = new Mock<IValidator<{UseCaseName}Request>>();
            _queryServiceMock = new Mock<IQueryService>();
            _mapperMock = new Mock<IMapper>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            
            _useCase = new {UseCaseName}UseCase(
                _validatorMock.Object,
                _queryServiceMock.Object,
                _mapperMock.Object,
                _tenantProviderMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new {UseCaseName}Request { Id = 1 };
            var validationResult = new ValidationResult(false, new List<string> { "Error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.Errors);
            // Verify that query is not executed when validation fails
            _queryServiceMock.Verify(q => q.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<QueryBuilder>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenEntityNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new {UseCaseName}Request { Id = 1 };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
            _queryServiceMock.Setup(q => q.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<QueryBuilder>()))
                .ReturnsAsync(null);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("not found", result.Errors.First());
            // Verify that transaction is not committed
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);

        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldReturnSuccess()
        {
            // Arrange
            var request = new {UseCaseName}Request { Id = 1 };
            var entity = new { Id = 1, Name = "Test" };
            var updatedEntity = new {EntityName}();
            var response = new {UseCaseName}Response { Id = 1, Name = "Test" };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
           _repositoryMock.Setup(r => r.GetAsync(request.Id.Value, It.IsAny<CancellationToken>())).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<{EntityName}>(request)).Returns(entity);
            _repositoryMock.Setup(r => r.UpdateAsync(entity, It.IsAny<CancellationToken>())).ReturnsAsync(updatedEntity);
            _mapperMock.Setup(m => m.Map<{UseCaseName}Response>(updatedEntity)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(response, result.Value);
        }
    }
}
```

#### Command Use Case Tests
```csharp
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase.{UseCaseName};
using KvFnB.Modules.{ModuleName}.Domain.Repositories;
using Moq;
using Xunit;

namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase
{
    public class {UseCaseName}UseCaseTests
    {
        private readonly Mock<IValidator<{UseCaseName}Request>> _validatorMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<I{EntityName}Repository> _repositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly {UseCaseName}UseCase _useCase;

        public {UseCaseName}UseCaseTests()
        {
            _validatorMock = new Mock<IValidator<{UseCaseName}Request>>();
            _mapperMock = new Mock<IMapper>();
            _repositoryMock = new Mock<I{EntityName}Repository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            
            _useCase = new {UseCaseName}UseCase(
                _validatorMock.Object,
                _mapperMock.Object,
                _repositoryMock.Object,
                _unitOfWorkMock.Object,
                _mapperMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new {UseCaseName}Request();
            var validationResult = new ValidationResult(false, new List<string> { "Error" });
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.Errors);
            // Verify that business logic is not executed when validation fails
            _repositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);

        }

        [Fact]
        public async Task ExecuteAsync_WhenEntityNotFound_ShouldReturnFailure()
        {
            // Arrange
            var request = new {UseCaseName}Request { Id = 1 };
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _repositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync((EntityName)null);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("not found", result.Errors.First());
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldReturnSuccess()
        {
            // Arrange
            var request = new {UseCaseName}Request { Id = 1 };
            var entity = new {EntityName}();
            var response = new {UseCaseName}Response();
            var validationResult = ValidationResult.Success();
            
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _repositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>())).ReturnsAsync(entity);
            _repositoryMock.Setup(r => r.UpdateAsync(entity, It.IsAny<CancellationToken>())).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<{UseCaseName}Response>(entity)).Returns(response);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(response, result.Value);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
```

## Implementation Checklist

For each use case, ensure you have implemented:

- [ ] DTO models in separate files
- [ ] Request model with proper annotations
- [ ] Response model with proper annotations
- [ ] Validator with comprehensive rules
- [ ] Use case implementation with proper error handling
- [ ] Mapping configuration in the module's mapping profile (in Infrastructure/Mapping folder)
- [ ] Dependency registration in the module's registrar (in Infrastructure/DependencyInjection folder)
- [ ] Unit tests covering all scenarios

## Naming Conventions

1. **DTOs**
   - PascalCase
   - Suffix with "Dto"
   - Example: `ProductDto`

2. **Use Cases**
   - PascalCase
   - Suffix with "UseCase"
   - Example: `CreateProductUseCase`

3. **Requests**
   - PascalCase
   - Suffix with "Request"
   - Example: `CreateProductRequest`

4. **Responses**
   - PascalCase
   - Suffix with "Response"
   - Example: `CreateProductResponse`

5. **Validators**
   - PascalCase
   - Suffix with "Validator"
   - Example: `CreateProductValidator`

## DTO Guidelines

1. **Use Record Types**
   - All DTOs, requests, and responses should be defined as record types
   - Use init-only properties to ensure immutability
   - Example: `public record ProductDto { public string Name { get; init; } }`

2. **Separate Contract DTOs**
   - Define shared DTOs in the Contracts folder
   - Group related DTOs in a single file (e.g., `ProductDtos.cs`)
   - Use these DTOs in request and response models

3. **Add Annotations**
   - Use `[Required]` for required properties
   - Use `[Range]`, `[MaxLength]`, etc. for validation constraints
   - Use `[JsonPropertyName]` for JSON serialization

## Validation Guidelines

1. **Basic Property Validation**
   - Use `RuleFor(x => x.Property)` for simple property validation
   - Chain validation methods like `NotNull()`, `NotEmpty()`, `GreaterThan()`, etc.

2. **Collection Validation**
   - Use `RuleFor(x => (IEnumerable<T>)x.Collection)` to cast to IEnumerable
   - Use `ForEach(item => { ... })` to define rules for each item
   - Use `item.Rule(x => x.Property)` to validate item properties

3. **Custom Validation Logic**
   - Use `Must(predicate, errorMessage)` for custom validation logic
   - Create private methods for complex validation rules

## Implementation Steps

1. **Define DTOs in Contracts Folder**
   - Create or update DTO models in the Contracts folder
   - Group related DTOs in a single file
   - Add proper annotations and documentation

2. **Create Request/Response Models**
   - Define input parameters in Request
   - Define output data in Response
   - Reference DTOs from the Contracts folder
   - Add annotations and documentation

3. **Create Validator**
   - Implement validation rules for simple properties
   - Use RuleForEach for collection validation
   - Add custom validation logic as needed

4. **Determine Use Case Type**
   - For read-only operations (Get/List/Search), implement as Query Use Case
   - For data-changing operations (Create/Update/Delete), implement as Command Use Case

5. **Implement Use Case**
   - **For Query Use Cases:**
     - Inherit from UseCaseBase<TRequest, TResponse>
     - Inject IQueryService for database access through Dapper
     - Create SQL queries using QueryBuilder
     - Use _queryService.QueryAsync or similar methods for data retrieval
     - Never modify data in a Query Use Case
  
   - **For Command Use Cases:**
     - Inherit from UseCaseBase<TRequest, TResponse>
     - Inject appropriate IRepository interfaces and IUnitOfWork
     - Implement business logic using repository methods
     - Always call _unitOfWork.CommitAsync() to persist changes
     - Avoid direct database queries with Dapper in Command Use Cases

   - **For Both Types:**
     - Do not implement validation logic (handled by base class)
     - Override ExecuteAsync method from base class
     - Handle exceptions appropriately
     - Return appropriate Result<TResponse>

6. **Configure Mapping Profile**
   - Add mappings between DTOs and domain entities
   - Add mappings between request/response models and domain entities
   - Implement custom mapping logic if needed
   - Location: Infrastructure/Mapping/{ModuleName}MappingProfile.cs

7. **Register Dependencies**
   - Add use case to module's DI container
   - Register validator in the DI container
   - Configure any required options
   - Location: Infrastructure/DependencyInjection/ModuleRegistrar.cs

8. **Write Unit Tests**
   - Test validation failures
   - Test entity not found scenarios
   - Test successful execution
   - Test exception handling

## Common Dependencies


### Query Use Case Dependencies
```csharp
// Common interfaces to inject for Query use cases
IValidator<TRequest>           // For request validation
IQueryService                 // For executing raw SQL queries with Dapper
IMapper                       // For object mapping
ITenantProvider               // For tenant context data
IUserProvider                 // For user context data
ILogger<T>                    // For logging
```

### Command Use Case Dependencies
```csharp

// Common interfaces to inject for Command use cases
IValidator<TRequest>           // For request validation
I{Entity}Repository           // For repository operations
IUnitOfWork                   // For transaction management
IMapper                       // For object mapping
ITenantProvider               // For tenant context data
IUserProvider                 // For user context data
```

## Error Handling Guidelines

1. Use custom domain exceptions from Core layer
2. Return `Result<T>` with appropriate error messages
3. Log exceptions using the injected logger
4. Handle specific domain exceptions separately from general exceptions

## Testing Requirements

1. Test Directory Structure:
```
test/Modules/{ModuleName}.Tests/Application/UseCases/{EntityName}UseCase/
└── {UseCaseName}UseCaseTests.cs
```

2. Required Test Scenarios:
   - Validation failures
   - Entity not found scenarios
   - Successful execution
   - Domain rule violations
   - Exception handling
   - Repository calls verification
   - Unit of work commits
   - Cancellation token handling
   - External dependencies mocking

3. Test Class Template:
```csharp
namespace KvFnB.Modules.{ModuleName}.Application.UseCases.{EntityName}UseCase;

public class {UseCaseName}UseCaseTests
{
    private readonly Mock<IValidator<{UseCaseName}Request>> _validatorMock;
    // Other mocked dependencies
    private readonly {UseCaseName}UseCase _useCase;

    public {UseCaseName}UseCaseTests()
    {
        _validatorMock = new Mock<IValidator<{UseCaseName}Request>>();
        // Initialize other mocks
        _useCase = new {UseCaseName}UseCase(_validatorMock.Object /* other dependencies */);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Test validation failure
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidRequest_ShouldReturnSuccess()
    {
        // Test successful execution
    }
}
```

## Documentation Requirements

1. XML comments on all public members
2. Clear method and parameter descriptions
3. Example usage in complex scenarios
4. Document exceptions that may be thrown

## Dependency Injection Example

```csharp
// In Module's DependencyInjection.cs
public static IServiceCollection Add{ModuleName}Module(this IServiceCollection services)
{
    services.AddScoped<{UseCaseName}UseCase>();
    services.AddScoped<IValidator<{UseCaseName}Request>, {UseCaseName}Validator>();
    return services;
}
```

## Best Practices

1. **Immutability**
   - Use record types for DTOs, requests, and responses
   - Use init-only properties to ensure immutability
   - Avoid mutable state in use cases

2. **Validation**
   - Use RuleForEach for collection validation
   - Add proper annotations to request models
   - Return descriptive validation error messages

3. **Error Handling**
   - Use Result<T> for returning success/failure
   - Include detailed error messages
   - Log exceptions with appropriate log levels

4. **Testing**
   - Test validation rules thoroughly
   - Mock external dependencies
   - Verify repository calls and unit of work commits

5. **Documentation**
   - Add XML comments to all public members
   - Document request/response models thoroughly
   - Include examples for complex scenarios