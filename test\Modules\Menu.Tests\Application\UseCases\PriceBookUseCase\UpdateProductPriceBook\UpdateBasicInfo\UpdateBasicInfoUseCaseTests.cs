using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateBasicInfo;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using Moq;

namespace KvFnB.Modules.Menu.Tests.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateBasicInfo;

public class UpdateBasicInfoUseCaseTests
{
    private readonly Mock<IValidator<UpdateBasicInfoRequest>> _validatorMock;
    private readonly Mock<IPriceBookRepository> _priceBookRepositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly UpdateBasicInfoUseCase _useCase;

    public UpdateBasicInfoUseCaseTests()
    {
        _validatorMock = new Mock<IValidator<UpdateBasicInfoRequest>>();
        _priceBookRepositoryMock = new Mock<IPriceBookRepository>();
        _mapperMock = new Mock<IMapper>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _useCase = new UpdateBasicInfoUseCase(
            _validatorMock.Object,
            _priceBookRepositoryMock.Object,
            _mapperMock.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WhenRequestIsValid_ShouldUpdateBasicInfo()
    {
        // Arrange
        var request = CreateValidRequest();
        var priceBook = CreatePriceBook();
        var response = new UpdateBasicInfoResponse
        {
            Id = priceBook.Id,
            Name = request.Name,
            StartDate = request.StartDate,
            EndDate = request.EndDate
        };

        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _priceBookRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(priceBook);
        _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        _mapperMock.Setup(m => m.Map<UpdateBasicInfoResponse>(It.IsAny<PriceBook>()))
            .Returns(response);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(response, result.Value);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateValidRequest();
        var validationErrors = new List<string> { "Validation error" };
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(false, validationErrors));

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(validationErrors, result.ValidationErrors);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenPriceBookNotFound_ShouldReturnFailure()
    {
        // Arrange
        var request = CreateValidRequest();
        
        _validatorMock.Setup(v => v.Validate(request))
            .Returns(new ValidationResult(true, null!));
        _priceBookRepositoryMock.Setup(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PriceBook?)null);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal($"Price book with ID {request.Id} not found", result.ErrorMessage);
        _priceBookRepositoryMock.Verify(r => r.GetAsync(request.Id, It.IsAny<CancellationToken>()), Times.Once);
        _priceBookRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<PriceBook>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    private static UpdateBasicInfoRequest CreateValidRequest()
    {
        return new UpdateBasicInfoRequest
        {
            Id = 1,
            Name = "Updated Price Book",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30)
        };
    }

    private static PriceBook CreatePriceBook()
    {
        return PriceBook.Create(new CreatePriceBookModel
        {
            Name = "Test Price Book",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(30),
            IsGlobal = true,
            ForAllUser = true,
            ForAllCustomerGroup = true,
            ForAllTableAndRoom = true,
            ForTakeAwayTable = true,
            Type = "Regular"
        });
    }
} 