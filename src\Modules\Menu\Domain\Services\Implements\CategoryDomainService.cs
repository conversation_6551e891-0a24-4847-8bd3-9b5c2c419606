using KvFnB.Core.Abstractions;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;

namespace KvFnB.Modules.Menu.Domain.Services.Implements
{
    public class CategoryDomainService : ICategoryDomainService
    {
        private readonly ICategoryRepository _categoryRepository;
        private readonly IUnitOfWork _unitOfWork;

        public CategoryDomainService(ICategoryRepository categoryRepository, IUnitOfWork unitOfWork)
        {
            _categoryRepository = categoryRepository ?? throw new ArgumentNullException(nameof(categoryRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<bool> UpdateCategoryRanksAsync(Dictionary<int, int?> categoryRanks, CancellationToken cancellationToken = default)
        {
            if (categoryRanks == null || categoryRanks.Count == 0)
            {
                return false;
            }

            var categoryIds = categoryRanks.Keys.ToList();
            var categories = await _categoryRepository.GetByIdsAsync(categoryIds, cancellationToken);

            if (categories == null || categories.Count() != categoryIds.Count)
            {
                return false;
            }

            foreach (var category in categories)
            {
                if (categoryRanks.TryGetValue(category.Id, out int? rank) && rank.HasValue)
                {
                    category.UpdateRank(rank.Value);
                }
            }

            _categoryRepository.UpdateCategories(categories);
            await _unitOfWork.CommitAsync(cancellationToken);
            return true;
        }
    }
}