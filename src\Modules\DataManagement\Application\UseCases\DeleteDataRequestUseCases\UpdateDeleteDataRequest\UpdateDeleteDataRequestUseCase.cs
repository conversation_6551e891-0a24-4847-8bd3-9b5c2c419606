﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using System.Text.Json;

namespace KvFnB.Modules.DataManagement.Application.UseCases.DeleteDataRequestUseCases.UpdateDeleteDataRequest
{
    public class UpdateDeleteDataRequestUseCase : UseCaseBase<UpdateDeleteDataRequestRequest, DeleteDataRequestResponse>
    {
        private readonly IValidator<UpdateDeleteDataRequestRequest> _validator;
        private readonly IDeleteDataRequestRepository _deleteDataRequestRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ITenantProvider _tenantProvider;
        private readonly IOtpService _otpService;
        private readonly IBranchService _branchService;


        public UpdateDeleteDataRequestUseCase(
            IValidator<UpdateDeleteDataRequestRequest> validator,
            IDeleteDataRequestRepository deleteDataRequestRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ITenantProvider tenantProvider,
            IOtpService otpService,
            IBranchService branchService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _deleteDataRequestRepository = deleteDataRequestRepository ?? throw new ArgumentNullException(nameof(deleteDataRequestRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _otpService = otpService ?? throw new ArgumentNullException(nameof(otpService));
            _branchService = branchService ?? throw new ArgumentNullException(nameof(branchService));
        }

        public override async Task<Result<DeleteDataRequestResponse>> ExecuteAsync(
            UpdateDeleteDataRequestRequest request,
            CancellationToken cancellationToken = default)
        {
                //Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeleteDataRequestResponse>.Failure(validationResult.Errors);
                }

                var tenantId = _tenantProvider.GetTenantId() ?? 0;

                // Get the existing request
                var deleteDataRequest = await _deleteDataRequestRepository.GetAsync(request.Id, cancellationToken);
                if (deleteDataRequest == null || deleteDataRequest.TenantId != tenantId)
                {
                    return Result<DeleteDataRequestResponse>.Failure("Delete data request not found");
                }

                // Validate branch IDs
                if (request.BranchIds != null && request.BranchIds.Count != 0)
                {
                    bool branchesValid = await _branchService.ValidateBranchAsync(request.BranchIds.ToList(), cancellationToken);
                    if (!branchesValid)
                    {
                        return Result<DeleteDataRequestResponse>.Failure("Một hoặc nhiều chi nhánh ngừng hoạt động hoặc đã bị xóa");
                    }
                }

                // Check for duplicate execution date (excluding the current request)
                DateTime? executeDate = request.ExecuteDate != default ? request.ExecuteDate : null;
                if (executeDate.HasValue)
                {
                    // Use just the date part for comparison
                    var existingRequest = await _deleteDataRequestRepository.CheckDuplicateExecuteDateAsync(
                        executeDate.Value.Date,
                        cancellationToken,
                        deleteDataRequest.Id);

                    if (existingRequest != null)
                    {
                        var message = LocalizationProvider.GetLocalizedMessage("man.delete_data.message_error.duplicate_date", executeDate);
                        return Result<DeleteDataRequestResponse>.Failure(message);
                    }
                }

                //Verify OTP
                var otpVerified = await _otpService.VerifyOtpAsync(request.Otp, request.FingerPrintKey, cancellationToken);
                if (!otpVerified)
                {
                    return Result<DeleteDataRequestResponse>.Failure("Invalid or expired OTP");
                }

                // Serialize ScheduleConfig to JSON
                var scheduleConfigJson = request.ScheduleConfig != null ? JsonSerializer.Serialize(request.ScheduleConfig) : null;

                // Serialize FilterCondition to JSON
                var filterConditionJson = request.FilterCondition != null ? JsonSerializer.Serialize(request.FilterCondition) : null;
                string branchIds = string.Join(",", request.BranchIds ?? []);

                // Khi update sẽ tạo mới lịch, lịch cũ sẽ update Status để sau này trace bug
                // Create the DeleteDataRequest domain entity
                var deleteDataRequestCreate = DeleteDataRequest.Create(
                    request.Email,
                    request.Type,
                    request.ScheduleType,
                    branchIds,
                    DeleteDataRequestStatus.Approved,
                    executeDate,
                    scheduleConfigJson,
                    filterConditionJson);
                // Update status old deleteDataRequest
                deleteDataRequest.UpdateStatus(DeleteDataRequestStatus.Cancelled);

                await _deleteDataRequestRepository.AddAsync(deleteDataRequestCreate, cancellationToken);
                await _deleteDataRequestRepository.UpdateAsync(deleteDataRequest, cancellationToken);
                // Save changes
                await _unitOfWork.CommitAsync(cancellationToken);

                // Map to response and return
                var response = _mapper.Map<DeleteDataRequestResponse>(deleteDataRequestCreate);

                return Result<DeleteDataRequestResponse>.Success(response);
        }
    }
} 