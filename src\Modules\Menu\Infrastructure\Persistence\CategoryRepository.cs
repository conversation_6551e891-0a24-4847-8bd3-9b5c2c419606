using System.Runtime.CompilerServices;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Menu.Infrastructure.Persistence
{
    public class CategoryRepository : BaseRepository<Category, int>, ICategoryRepository
    {
        public CategoryRepository(ShardingDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Category>> GetByIdsAsync(List<int> categoryIds, CancellationToken cancellationToken = default)
        {
            return await _context.Set<Category>()
                               .Where(c => categoryIds.Contains(c.Id))
                               .ToListAsync(cancellationToken);
        }
        
        public async Task<IEnumerable<Category>> GetAllAsync(bool includeDeleted = false, CancellationToken cancellationToken = default)
        {
            var query = _context.Set<Category>().AsQueryable();

            if (!includeDeleted)
            {
                query = query.Where(c => c.IsDeleted == false || c.IsDeleted == null);
            }

            return await query.OrderBy(c => c.Rank).ToListAsync(cancellationToken);
        }

        public int? GetLevelOfCategory(int categoryId)
        {
            var sql = FormattableStringFactory.Create(@"
                    WITH CategoryHierarchy AS (
                        SELECT Id, ParentId, 1 AS Level
                        FROM Category
                        WHERE Id = {0}

                        UNION ALL

                        SELECT c.Id, c.ParentId, ch.Level + 1
                        FROM Category c
                        INNER JOIN CategoryHierarchy ch ON c.Id = ch.ParentId
                    )
                    SELECT Level
                    FROM CategoryHierarchy
                    ORDER BY Level DESC", categoryId);

            var results = _context.Database.SqlQuery<int?>(sql).AsEnumerable();
            return results.FirstOrDefault();
        }

        public void UpdateCategories(IEnumerable<Category> categories)
        {
            _context.Set<Category>().UpdateRange(categories);
        }

        public async Task<bool> IsUniqueCategoryNameAsync(string name, int? excludeCategoryId = null, CancellationToken cancellationToken = default)
        {
            var query = _context.Set<Category>()
                .Where(c => c.Name == name);

            if (excludeCategoryId.HasValue)
            {
                query = query.Where(c => c.Id != excludeCategoryId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
    }
}