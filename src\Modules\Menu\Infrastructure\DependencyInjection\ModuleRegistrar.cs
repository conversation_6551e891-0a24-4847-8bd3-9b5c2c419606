using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.CreateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.GetProductCategories;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.GetProductCategoryDetail;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.RankingProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.RemoveProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.SortingProductCategories;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.UpdateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.CreateGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.DeleteGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.GetListGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.CreateNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.DeleteNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetListNoteTemplateByGroup;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.GetProductPriceBookDetail;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.GetProductPriceBooks;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateBasicInfo;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateCustomTime;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateProductPrice;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateScopeOfApplication;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.DeleteProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetListProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductDetail;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.SearchProduct;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateImages;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateOnHandQuantity;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdatePriceBooks;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductSaleBranches;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateToppings;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services;
using KvFnB.Modules.Menu.Infrastructure.Mapping;
using KvFnB.Modules.Menu.Infrastructure.Persistence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.CreateProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.ActiveProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.DeActiveProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.DeleteProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateBasicInfo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductCombo;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProductCombo;
using KvFnB.Modules.Menu.Application.UseCases.BranchUseCase.GetBranchList;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.UpdateProduct.UpdateProduct;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.UpdateGroupNoteTemplate.UpdateGroupName;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateBasicInfoNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateProductNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetListProductByNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.UpdateNoteTemplate.UpdateNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.GetAllGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.NoteTemplateUseCase.GetAllProductByNoteTemplate;
using KvFnB.Modules.Menu.Domain.Services.Implements;
using KvFnB.Modules.Menu.Domain.Services.Interfaces;
using KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.GetProductSaleBranch;

namespace KvFnB.Modules.Menu.Infrastructure.DependencyInjection
{
    public static class MenuModule
    {
        public static void AddMenuModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register use cases
            services.AddScoped<UpdateProductCategoryUseCase>();
            services.AddScoped<CreateProductCategoryUseCase>();
            services.AddScoped<RankCategoryUseCase>();
            services.AddScoped<RemoveProductCategoryUseCase>();
            services.AddScoped<SortingProductCategoriesUseCase>();
            services.AddScoped<GetProductCategoriesUseCase>();
            services.AddScoped<GetProductCategoryDetailUseCase>();
            services.AddScoped<ActiveProductPriceBookUseCase>();
            services.AddScoped<DeActiveProductPriceBookUseCase>();
            services.AddScoped<GetProductPriceBooksUseCase>();
            services.AddScoped<GetProductPriceBookDetailUseCase>();
            services.AddScoped<UpdateProductPriceUseCase>();
            services.AddScoped<CreateProductPriceBookUseCase>();
            services.AddScoped<DeleteProductPriceBookUseCase>();
            services.AddScoped<UpdateBasicInfoUseCase>();
            services.AddScoped<UpdateCustomTimeUseCase>();
            services.AddScoped<UpdateScopeOfApplicationUseCase>();
            services.AddScoped<GetProductDetailUseCase>();
            services.AddScoped<GetListProductUseCase>();
            services.AddScoped<CreateProductUseCase>();
            services.AddScoped<UpdateProductImagesUseCase>();
            services.AddScoped<UpdateProductBasicInfoUseCase>();
            services.AddScoped<UpdateProductToppingsUseCase>();
            services.AddScoped<UpdateProductPriceBooksUseCase>();
            services.AddScoped<UpdateProductOnHandQuantityUseCase>();
            services.AddScoped<UpdateProductSaleBranchesUseCase>();
            services.AddScoped<DeleteProductUseCase>();
            services.AddScoped<GetProductComboUseCase>();
            services.AddScoped<UpdateProductComboUseCase>();
            services.AddScoped<SearchProductUseCase>();
            services.AddScoped<UpdateProductUseCase>();
            services.AddScoped<CreateGroupNoteTemplateUseCase>();
            services.AddScoped<UpdateGroupNameUseCase>();
            services.AddScoped<DeleteGroupNoteTemplateUseCase>();
            services.AddScoped<GetListGroupNoteTemplateUseCase>();
            services.AddScoped<CreateNoteTemplateUseCase>();
            services.AddScoped<UpdateBasicInfoNoteTemplateUseCase>();
            services.AddScoped<UpdateProductNoteTemplateUseCase>();
            services.AddScoped<DeleteNoteTemplateUseCase>();
            services.AddScoped<GetListNoteTemplateByGroupUseCase>();
            services.AddScoped<GetListProductByNoteTemplateUseCase>();
            services.AddScoped<UpdateNoteTemplateUseCase>();
            services.AddScoped<GetProductSaleBranchUseCase>();
            services.AddScoped<GetAllGroupNoteTemplateUseCase>();
            services.AddScoped<GetAllProductByNoteTemplateUseCase>();

            // Register validators
            services.AddScoped<IValidator<UpdateProductCategoryRequest>, UpdateProductCategoryValidator>();
            services.AddScoped<IValidator<CreateProductCategoryRequest>, CreateProductCategoryValidator>();
            services.AddScoped<IValidator<SortingProductCategoriesRequest>, SortingProductCategoriesUseCaseValidator>();
            services.AddScoped<IValidator<CreateProductRequest>, CreateProductUseCaseValidator>();
            services.AddScoped<IValidator<UpdateProductImagesRequest>, UpdateProductImagesValidator>();
            services.AddScoped<IValidator<UpdateProductBasicInfoRequest>, UpdateProductBasicInfoValidator>();
            services.AddScoped<IValidator<UpdateProductToppingsRequest>, UpdateProductToppingsValidator>();
            services.AddScoped<IValidator<UpdateProductPriceBooksRequest>, UpdateProductPriceBooksValidator>();
            services.AddScoped<IValidator<UpdateProductOnHandQuantityRequest>, UpdateProductOnHandQuantityValidator>();
            services.AddScoped<IValidator<UpdateProductSaleBranchesRequest>, UpdateProductSaleBranchesValidator>();
            services.AddScoped<IValidator<DeleteProductRequest>, DeleteProductValidator>();
            services.AddScoped<IValidator<GetProductComboRequest>, GetProductComboValidator>();
            services.AddScoped<IValidator<UpdateProductComboRequest>, UpdateProductComboRequestValidator>();
            services.AddScoped<IValidator<SearchProductRequest>, SearchProductValidator>();
            services.AddScoped<IValidator<UpdateProductPriceRequest>, UpdateProductPriceValidator>();
            services.AddScoped<IValidator<GetProductPriceBookDetailRequest>, GetProductPriceBookDetailValidator>();
            services.AddScoped<IValidator<CreateProductPriceBookRequest>, CreateProductPriceBookValidator>();
            services.AddScoped<IValidator<ActiveProductPriceBookRequest>, ActiveProductPriceBookValidator>();
            services.AddScoped<IValidator<DeActiveProductPriceBookRequest>, DeActiveProductPriceBookValidator>();
            services.AddScoped<IValidator<DeleteProductPriceBookRequest>, DeleteProductPriceBookValidator>();
            services.AddScoped<IValidator<UpdateBasicInfoRequest>, UpdateBasicInfoValidator>();
            services.AddScoped<IValidator<UpdateCustomTimeRequest>, UpdateCustomTimeValidator>();
            services.AddScoped<IValidator<UpdateScopeOfApplicationRequest>, UpdateScopeOfApplicationValidator>();
            services.AddScoped<IValidator<UpdateProductRequest>, UpdateProductValidator>();
            services.AddScoped<IValidator<CreateNoteTemplateRequest>, CreateNoteTemplateValidator>();
            services.AddScoped<IValidator<UpdateBasicInfoNoteTemplateRequest>, UpdateBasicInfoNoteTemplateValidator>();
            services.AddScoped<IValidator<UpdateProductNoteTemplateRequest>, UpdateProductNoteTemplateValidator>();
            services.AddScoped<IValidator<DeleteNoteTemplateRequest>, DeleteNoteTemplateValidator>();
            services.AddScoped<IValidator<GetListNoteTemplateByGroupRequest>, GetListNoteTemplateByGroupValidator>();
            services.AddScoped<IValidator<CreateGroupNoteTemplateRequest>, CreateGroupNoteTemplateValidator>();
            services.AddScoped<IValidator<UpdateGroupNameRequest>, UpdateGroupNameValidator>();
            services.AddScoped<IValidator<DeleteGroupNoteTemplateRequest>, DeleteGroupNoteTemplateValidator>();
            services.AddScoped<IValidator<GetListProductByNoteTemplateRequest>, GetListProductByNoteTemplateValidator>();
            services.AddScoped<IValidator<UpdateNoteTemplateRequest>, UpdateNoteTemplateValidator>();
            services.AddScoped<IValidator<GetProductSaleBranchRequest>, GetProductSaleBranchValidator>();
            services.AddScoped<IValidator<GetAllProductByNoteTemplateRequest>, GetAllProductByNoteTemplateValidator>();

            // Register repositories
            services.AddScoped<ICategoryRepository, CategoryRepository>();
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<ITaxRepository, TaxRepository>();
            services.AddScoped<IBranchRepository, BranchRepository>();
            services.AddScoped<IPriceBookRepository, PriceBookRepository>();
            services.AddScoped<IPriceBookDetailRepository, PriceBookDetailRepository>();
            services.AddScoped<ICustomizableComboGroupRepository, CustomizableComboGroupRepository>();
            services.AddScoped<ICustomizableComboGroupItemRepository, CustomizableComboGroupItemRepository>();
            services.AddScoped<INoteTemplateRepository, NoteTemplateRepository>();
            services.AddScoped<IGroupNoteTemplateRepository, GroupNoteTemplateRepository>();

            // Register Domain Services
            services.AddScoped<ICategoryDomainService, CategoryDomainService>();
            services.AddScoped<IProductPriceBookDomainService, ProductPriceBookDomainService>();
            services.AddScoped<INoteTemplateDomainService, NoteTemplateDomainService>();

            services.AddMenuBranchModule();

            services.AddAutoMapper(typeof(MenuMappingProfile));

        }

        public static void AddMenuBranchModule(this IServiceCollection services)
        {
            services.AddScoped<GetBranchListUseCase>();
            services.AddScoped<IValidator<GetBranchListRequest>, GetBranchListValidator>();
        }
    }
}