# S3 Technical Implementation Plan

## 1. Project Setup and Configuration

### 1.1. Create Project Structure

```cursor
// Create a new .NET 8 class library project for S3 service
dotnet new classlib -n AmazonS3Service -f net8.0

// Create a test project
dotnet new xunit -n AmazonS3Service.Tests -f net8.0

// Create a solution file
dotnet new sln -n AmazonS3Service

// Add projects to solution
dotnet sln add AmazonS3Service/AmazonS3Service.csproj
dotnet sln add AmazonS3Service.Tests/AmazonS3Service.Tests.csproj
```

### 1.2. Add Required NuGet Packages

```cursor
// Add required packages to main project
cd AmazonS3Service
dotnet add package AWSSDK.S3
dotnet add package AWSSDK.Extensions.NETCore.Setup
dotnet add package Microsoft.Extensions.Options
dotnet add package Microsoft.Extensions.Logging.Abstractions
dotnet add package Microsoft.Extensions.DependencyInjection.Abstractions

// Add required packages to test project
cd ../AmazonS3Service.Tests
dotnet add package Moq
dotnet add package xunit
dotnet add package xunit.runner.visualstudio
dotnet add package Microsoft.NET.Test.Sdk
dotnet add package coverlet.collector
dotnet add package AWSSDK.S3
dotnet add reference ../AmazonS3Service/AmazonS3Service.csproj
```

## 2. Implementation Tasks

### 2.1. Create Configuration Models

```cursor
// Create S3Configuration.cs
cd ../AmazonS3Service
mkdir Models
touch Models/S3Configuration.cs
```

Edit `Models/S3Configuration.cs`:

```cursor
// Add S3Configuration class implementation
using Amazon.S3;

namespace AmazonS3Service.Models;

public class S3Configuration
{
    public string BucketName { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string AccessKey { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public bool UseInstanceProfile { get; set; } = false;
    
    // S3-compatible storage settings
    public bool UseCompatibleMode { get; set; } = false;
    public string? CompatibleEndpoint { get; set; }
    public string? CompatibleAccessKey { get; set; }
    public string? CompatibleSecretKey { get; set; }
    public bool ForcePathStyle { get; set; } = false;
    
    // Proxy configuration
    public string? ProxyUrl { get; set; }
    
    public string? BaseKeyPrefix { get; set; }
    public int UploadPartSize { get; set; } = 5 * 1024 * 1024; // Default: 5MB
    public S3CannedACL DefaultACL { get; set; } = S3CannedACL.Private;
    public S3StorageClass StorageClass { get; set; } = S3StorageClass.Standard;
    public ServerSideEncryptionMethod EncryptionMethod { get; set; } = ServerSideEncryptionMethod.None;
    public string? CacheControlHeader { get; set; }
}
```

### 2.2. Create Result Models

```cursor
// Create S3UploadResult.cs
touch Models/S3UploadResult.cs
```

Edit `Models/S3UploadResult.cs`:

```cursor
// Add S3UploadResult class implementation
namespace AmazonS3Service.Models;

public class S3UploadResult
{
    public bool Success { get; set; }
    public string? Key { get; set; }
    public string? BucketName { get; set; }
    public string? ETag { get; set; }
    public string? VersionId { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
    public string? PublicUrl { get; set; }
}
```

### 2.3. Create Service Interface

```cursor
// Create IS3Service.cs interface
mkdir Interfaces
touch Interfaces/IS3Service.cs
```

Edit `Interfaces/IS3Service.cs`:

```cursor
// Add IS3Service interface implementation
using Amazon.S3;
using AmazonS3Service.Models;

namespace AmazonS3Service.Interfaces;

public interface IS3Service
{
    Task<S3UploadResult> UploadFileAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
    
    Task<S3UploadResult> UploadFileAsync(string filePath, string key, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
    
    Task<bool> DeleteFileAsync(string key, CancellationToken cancellationToken = default);
    
    Task<Stream> DownloadFileAsync(string key, CancellationToken cancellationToken = default);

    Task<S3UploadResult> UploadLargeFileAsync(Stream fileStream, string key, string contentType, Dictionary<string, string>? metadata = null, IProgress<int>? progress = null, CancellationToken cancellationToken = default);
}
```

### 2.4. Implement S3 Service

```cursor
// Create S3Service.cs implementation
mkdir Services
touch Services/S3Service.cs
```

Edit `Services/S3Service.cs`:

```cursor
// Add S3Service implementation
using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using AmazonS3Service.Interfaces;
using AmazonS3Service.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;

namespace AmazonS3Service.Services;

public class S3Service : IS3Service
{
    private readonly S3Configuration _config;
    private readonly ILogger<S3Service> _logger;
    private IAmazonS3? _s3Client;

    public S3Service(
        IOptions<S3Configuration> config,
        ILogger<S3Service> logger)
    {
        _config = config.Value;
        _logger = logger;
        
        if (string.IsNullOrEmpty(_config.BucketName))
        {
            _logger.LogError("S3 bucket name is not configured");
            return;
        }

        try
        {
            _s3Client = GetS3Client();
            _logger.LogDebug("S3 client initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize S3 client");
            // We allow the S3Client to be null and handle it in each method
        }
    }
    
    protected virtual IAmazonS3 GetS3Client()
    {
        // Initialize S3 client based on configuration
        var s3Config = new AmazonS3Config
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(_config.Region)
        };
        
        // Configure for S3-compatible storage
        if (_config.UseCompatibleMode)
        {
            if (string.IsNullOrEmpty(_config.CompatibleEndpoint))
            {
                throw new InvalidOperationException("CompatibleEndpoint must be set when UseCompatibleMode is true");
            }

            // Configure S3 client for compatibility mode
            s3Config.ServiceURL = _config.CompatibleEndpoint;
            s3Config.ForcePathStyle = _config.ForcePathStyle;
        }

        // Configure proxy if specified
        if (!string.IsNullOrEmpty(_config.ProxyUrl))
        {
            try
            {
                // Parse the proxy URL to extract host and port
                var proxyUri = new Uri(_config.ProxyUrl);
                s3Config.ProxyHost = proxyUri.Host;
                s3Config.ProxyPort = proxyUri.Port > 0 ? proxyUri.Port : 80;
            }
            catch (UriFormatException ex)
            {
                throw new InvalidOperationException($"Invalid proxy URL format: {_config.ProxyUrl}", ex);
            }
        }
        
        // Create the S3 client with the proper credentials
        if (_config.UseInstanceProfile)
        {
            return new AmazonS3Client(s3Config);
        }
        else if (_config.UseCompatibleMode)
        {
            if (string.IsNullOrEmpty(_config.CompatibleAccessKey) || string.IsNullOrEmpty(_config.CompatibleSecretKey))
            {
                throw new InvalidOperationException("CompatibleAccessKey and CompatibleSecretKey must be set when UseCompatibleMode is true");
            }

            return new AmazonS3Client(
                _config.CompatibleAccessKey,
                _config.CompatibleSecretKey,
                s3Config);
        }
        else
        {
            return new AmazonS3Client(
                _config.AccessKey,
                _config.SecretKey,
                s3Config);
        }
    }

    public async Task<S3UploadResult> UploadFileAsync(
        Stream fileStream, 
        string key, 
        string contentType, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting file upload to S3. Key: {Key}", key);
            
            // Append base prefix if configured
            if (!string.IsNullOrEmpty(_config.BaseKeyPrefix))
            {
                key = $"{_config.BaseKeyPrefix.TrimEnd('/')}/{key.TrimStart('/')}";
            }
            
            // Prepare request
            var putRequest = new PutObjectRequest
            {
                BucketName = _config.BucketName,
                Key = key,
                InputStream = fileStream,
                ContentType = contentType,
                CannedACL = _config.DefaultACL,
                StorageClass = _config.StorageClass
            };
            
            // Add server-side encryption if configured
            if (_config.EncryptionMethod != ServerSideEncryptionMethod.None)
            {
                putRequest.ServerSideEncryptionMethod = _config.EncryptionMethod;
            }
            
            // Add cache control header if configured
            if (!string.IsNullOrEmpty(_config.CacheControlHeader))
            {
                putRequest.CacheControl = _config.CacheControlHeader;
            }
            
            // Add metadata if provided
            if (metadata != null && metadata.Count > 0)
            {
                foreach (var item in metadata)
                {
                    putRequest.Metadata.Add(item.Key, item.Value);
                }
            }
            
            // Execute request
            var response = await _s3Client.PutObjectAsync(putRequest, cancellationToken);
            
            // Build result
            var result = new S3UploadResult
            {
                Success = true,
                Key = key,
                BucketName = _config.BucketName,
                ETag = response.ETag,
                VersionId = response.VersionId,
                Metadata = metadata
            };
            
            _logger.LogInformation("File uploaded successfully to S3. Key: {Key}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file to S3. Key: {Key}", key);
            return new S3UploadResult
            {
                Success = false,
                Key = key,
                BucketName = _config.BucketName,
                ErrorMessage = ex.Message,
                Exception = ex
            };
        }
    }

    public async Task<S3UploadResult> UploadFileAsync(
        string filePath, 
        string key, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting file upload to S3 from path: {FilePath}, Key: {Key}", filePath, key);
            
            // Verify file exists
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }
            
            // Determine content type from file extension
            var contentType = GetContentType(Path.GetExtension(filePath));
            
            // Upload using file stream
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                return await UploadFileAsync(fileStream, key, contentType, metadata, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file to S3 from path: {FilePath}, Key: {Key}", filePath, key);
            return new S3UploadResult
            {
                Success = false,
                Key = key,
                BucketName = _config.BucketName,
                ErrorMessage = ex.Message,
                Exception = ex
            };
        }
    }

    public async Task<bool> DeleteFileAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting file from S3. Key: {Key}", key);
            
            // Append base prefix if configured
            if (!string.IsNullOrEmpty(_config.BaseKeyPrefix))
            {
                key = $"{_config.BaseKeyPrefix.TrimEnd('/')}/{key.TrimStart('/')}";
            }
            
            // Create delete request
            var deleteRequest = new DeleteObjectRequest
            {
                BucketName = _config.BucketName,
                Key = key
            };
            
            // Execute request
            var response = await _s3Client.DeleteObjectAsync(deleteRequest, cancellationToken);
            
            _logger.LogInformation("File deleted successfully from S3. Key: {Key}", key);
            return response.HttpStatusCode == HttpStatusCode.NoContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file from S3. Key: {Key}", key);
            return false;
        }
    }

    public async Task<Stream> DownloadFileAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Downloading file from S3. Key: {Key}", key);
            
            // Append base prefix if configured
            if (!string.IsNullOrEmpty(_config.BaseKeyPrefix))
            {
                key = $"{_config.BaseKeyPrefix.TrimEnd('/')}/{key.TrimStart('/')}";
            }
            
            // Create get request
            var getRequest = new GetObjectRequest
            {
                BucketName = _config.BucketName,
                Key = key
            };
            
            // Execute request
            var response = await _s3Client.GetObjectAsync(getRequest, cancellationToken);
            
            // Create memory stream to hold the file data
            var memoryStream = new MemoryStream();
            await response.ResponseStream.CopyToAsync(memoryStream, cancellationToken);
            memoryStream.Position = 0;
            
            _logger.LogInformation("File downloaded successfully from S3. Key: {Key}", key);
            return memoryStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file from S3. Key: {Key}", key);
            throw;
        }
    }

    // Helper method to determine content type from file extension
    private string GetContentType(string fileExtension)
    {
        return fileExtension.ToLower() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".pdf" => "application/pdf",
            ".doc" or ".docx" => "application/msword",
            ".xls" or ".xlsx" => "application/vnd.ms-excel",
            ".zip" => "application/zip",
            ".txt" => "text/plain",
            ".csv" => "text/csv",
            ".html" or ".htm" => "text/html",
            ".json" => "application/json",
            ".xml" => "application/xml",
            _ => "application/octet-stream"
        };
    }

    public async Task<S3UploadResult> UploadLargeFileAsync(
        Stream fileStream,
        string key,
        string contentType,
        Dictionary<string, string>? metadata = null,
        IProgress<int>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting multi-part upload to S3. Key: {Key}", key);
            
            // Append base prefix if configured
            if (!string.IsNullOrEmpty(_config.BaseKeyPrefix))
            {
                key = $"{_config.BaseKeyPrefix.TrimEnd('/')}/{key.TrimStart('/')}";
            }
            
            // Create transfer utility for multi-part upload
            var transferUtility = new TransferUtility(_s3Client);
            
            // Configure upload request
            var uploadRequest = new TransferUtilityUploadRequest
            {
                BucketName = _config.BucketName,
                Key = key,
                InputStream = fileStream,
                ContentType = contentType,
                CannedACL = _config.DefaultACL,
                StorageClass = _config.StorageClass,
                PartSize = _config.UploadPartSize
            };
            
            // Add cache control header if configured
            if (!string.IsNullOrEmpty(_config.CacheControlHeader))
            {
                uploadRequest.CacheControl = _config.CacheControlHeader;
            }
            
            // Add metadata if provided
            if (metadata != null && metadata.Count > 0)
            {
                foreach (var item in metadata)
                {
                    uploadRequest.Metadata.Add(item.Key, item.Value);
                }
            }
            
            // Add progress tracking if requested
            if (progress != null)
            {
                uploadRequest.UploadProgressEvent += (sender, e) =>
                {
                    var percentDone = (int)((double)e.TransferredBytes / e.TotalBytes * 100);
                    progress.Report(percentDone);
                };
            }
            
            // Execute upload
            await transferUtility.UploadAsync(uploadRequest, cancellationToken);
            
            // Build result
            var result = new S3UploadResult
            {
                Success = true,
                Key = key,
                BucketName = _config.BucketName,
                Metadata = metadata
            };
            
            _logger.LogInformation("Large file uploaded successfully to S3. Key: {Key}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading large file to S3. Key: {Key}", key);
            return new S3UploadResult
            {
                Success = false,
                Key = key,
                BucketName = _config.BucketName,
                ErrorMessage = ex.Message,
                Exception = ex
            };
        }
    }
}
```

### 2.5. Create Extension Methods for DI Registration

```cursor
// Create S3ServiceExtensions.cs
mkdir Extensions
touch Extensions/S3ServiceExtensions.cs
```

Edit `Extensions/S3ServiceExtensions.cs`:

```cursor
// Add S3ServiceExtensions implementation
using AmazonS3Service.Interfaces;
using AmazonS3Service.Models;
using AmazonS3Service.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AmazonS3Service.Extensions;

public static class S3ServiceExtensions
{
    public static IServiceCollection AddS3Service(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<S3Configuration>(configuration.GetSection("S3"));
        services.AddSingleton<IS3Service, S3Service>();
        return services;
    }
    
    public static IServiceCollection AddS3Service(this IServiceCollection services, Action<S3Configuration> configureOptions)
    {
        services.Configure(configureOptions);
        services.AddSingleton<IS3Service, S3Service>();
        return services;
    }
}
```

## 3. Unit Testing

### 3.1. Create Basic Test Setup

```cursor
// Create test classes
cd ../AmazonS3Service.Tests
mkdir Services
touch Services/S3ServiceTests.cs
```

Edit `Services/S3ServiceTests.cs`:

```cursor
// Add S3ServiceTests implementation
using Amazon.S3;
using Amazon.S3.Model;
using AmazonS3Service.Models;
using AmazonS3Service.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace AmazonS3Service.Tests.Services;

public class S3ServiceTests
{
    private readonly Mock<IAmazonS3> _mockS3Client;
    private readonly Mock<ILogger<S3Service>> _mockLogger;
    private readonly S3Configuration _s3Config;
    
    public S3ServiceTests()
    {
        _mockS3Client = new Mock<IAmazonS3>();
        _mockLogger = new Mock<ILogger<S3Service>>();
        
        _s3Config = new S3Configuration
        {
            BucketName = "test-bucket",
            Region = "us-east-1",
            AccessKey = "test-access-key",
            SecretKey = "test-secret-key",
            UseInstanceProfile = false,
            BaseKeyPrefix = "uploads/",
            UploadPartSize = 5 * 1024 * 1024,
            DefaultACL = S3CannedACL.Private,
            StorageClass = S3StorageClass.Standard,
            EncryptionMethod = ServerSideEncryptionMethod.None
        };
    }
    
    [Fact]
    public async Task UploadFileAsync_ValidStream_ReturnsSuccessResult()
    {
        // Arrange
        var mockOptions = Options.Create(_s3Config);
        
        // Mock the S3 client response
        var putObjectResponse = new PutObjectResponse
        {
            ETag = "\"testETag\"",
            VersionId = "testVersionId"
        };
        
        _mockS3Client
            .Setup(x => x.PutObjectAsync(It.IsAny<PutObjectRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(putObjectResponse);
        
        // Create a custom S3Service that uses our mocked S3 client
        var s3Service = new TestableS3Service(_mockS3Client.Object, mockOptions, _mockLogger.Object);
        
        // Create test data
        var testStream = new MemoryStream(new byte[] { 1, 2, 3, 4, 5 });
        var testKey = "test-file.txt";
        var testContentType = "text/plain";
        var testMetadata = new Dictionary<string, string> { { "test", "value" } };
        
        // Act
        var result = await s3Service.UploadFileAsync(testStream, testKey, testContentType, testMetadata);
        
        // Assert
        Assert.True(result.Success);
        Assert.Equal($"{_s3Config.BaseKeyPrefix}{testKey}", result.Key);
        Assert.Equal(_s3Config.BucketName, result.BucketName);
        Assert.Equal("\"testETag\"", result.ETag);
        Assert.Equal("testVersionId", result.VersionId);
        Assert.Equal(testMetadata, result.Metadata);
        
        // Verify that PutObjectAsync was called with expected parameters
        _mockS3Client.Verify(
            x => x.PutObjectAsync(
                It.Is<PutObjectRequest>(req => 
                    req.BucketName == _s3Config.BucketName &&
                    req.Key == $"{_s3Config.BaseKeyPrefix}{testKey}" &&
                    req.ContentType == testContentType &&
                    req.CannedACL == _s3Config.DefaultACL &&
                    req.StorageClass == _s3Config.StorageClass),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
    
    [Fact]
    public async Task UploadFileAsync_WithCacheControlHeader_SetsCacheControlOnRequest()
    {
        // Arrange
        var configWithCacheControl = new S3Configuration
        {
            BucketName = "test-bucket",
            Region = "us-east-1",
            AccessKey = "test-access-key",
            SecretKey = "test-secret-key",
            CacheControlHeader = "max-age=86400"
        };
        
        var mockOptions = Options.Create(configWithCacheControl);
        
        // Mock the S3 client response
        var putObjectResponse = new PutObjectResponse
        {
            ETag = "\"testETag\"",
            VersionId = "testVersionId"
        };
        
        _mockS3Client
            .Setup(x => x.PutObjectAsync(It.IsAny<PutObjectRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(putObjectResponse);
        
        // Create a custom S3Service that uses our mocked S3 client
        var s3Service = new TestableS3Service(_mockS3Client.Object, mockOptions, _mockLogger.Object);
        
        // Create test data
        var testStream = new MemoryStream(new byte[] { 1, 2, 3, 4, 5 });
        var testKey = "test-file.txt";
        var testContentType = "text/plain";
        
        // Act
        var result = await s3Service.UploadFileAsync(testStream, testKey, testContentType);
        
        // Assert
        Assert.True(result.Success);
        
        // Verify that the cache control header was set in the request
        _mockS3Client.Verify(
            x => x.PutObjectAsync(
                It.Is<PutObjectRequest>(req => 
                    req.CacheControl == configWithCacheControl.CacheControlHeader),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
    
    // More tests...
    
    // Helper class to allow for injecting mocked S3 client
    private class TestableS3Service : S3Service
    {
        public TestableS3Service(
            IAmazonS3 s3Client,
            IOptions<S3Configuration> config,
            ILogger<S3Service> logger)
            : base(config, logger)
        {
            // We need a way to inject our mock S3 client, so we use this constructor
            // in unit tests only. It will call the base constructor but replace the
            // S3 client with our mock.
            SetS3Client(s3Client);
        }
        
        // Method to allow setting the S3 client from unit tests
        private void SetS3Client(IAmazonS3 s3Client)
        {
            // Use reflection to set the private field
            var field = typeof(S3Service).GetField("_s3Client", 
                System.Reflection.BindingFlags.Instance | 
                System.Reflection.BindingFlags.NonPublic);
            
            field?.SetValue(this, s3Client);
        }
    }

    Add tests for compatibility mode and proxy configuration:

    [Fact]
    public void Constructor_WithCompatibleModeEnabled_SetsUpClientCorrectly()
    {
        // Arrange
        var mockOptions = Options.Create(new S3Configuration
        {
            BucketName = "test-bucket",
            Region = "us-east-1",
            UseInstanceProfile = false,
            UseCompatibleMode = true,
            CompatibleEndpoint = "https://custom-s3-endpoint.com",
            CompatibleAccessKey = "compatible-access-key",
            CompatibleSecretKey = "compatible-secret-key",
            ForcePathStyle = true
        });
        
        var mockLogger = new Mock<ILogger<S3Service>>();
        
        // Create a testing service that allows us to inspect the configuration
        var s3Service = new TestableS3Service(mockOptions, mockLogger.Object);
        
        // Assert
        Assert.Equal("https://custom-s3-endpoint.com", s3Service.GetServiceUrl());
        Assert.True(s3Service.GetForcePathStyle());
        Assert.Equal("compatible-access-key", s3Service.GetAccessKey());
        Assert.Equal("compatible-secret-key", s3Service.GetSecretKey());
    }

    [Fact]
    public void Constructor_WithProxyConfiguration_SetsUpProxyCorrectly()
    {
        // Arrange
        var mockOptions = Options.Create(new S3Configuration
        {
            BucketName = "test-bucket",
            Region = "us-east-1",
            AccessKey = "test-access-key",
            SecretKey = "test-secret-key",
            ProxyUrl = "http://proxy.example.com:8080"
        });
        
        var mockLogger = new Mock<ILogger<S3Service>>();
        
        // Create a testing service that allows us to inspect the configuration
        var s3Service = new TestableS3Service(mockOptions, mockLogger.Object);
        
        // Assert
        Assert.Equal("proxy.example.com", s3Service.GetProxyHost());
        Assert.Equal(8080, s3Service.GetProxyPort());
    }

    Add a testable service class to help with testing configuration:

    private class TestableS3Service : S3Service
    {
        public TestableS3Service(
            IOptions<S3Configuration> config,
            ILogger<S3Service> logger)
            : base(config, logger)
        {
        }
        
        // Properties to capture configuration settings
        private string? _serviceUrl;
        private bool _forcePathStyle;
        private string? _accessKey;
        private string? _secretKey;
        private string? _proxyHost;
        private int? _proxyPort;
        private string? _cacheControlHeader;
        
        // Getters for testing
        public string? GetServiceUrl() => _serviceUrl;
        public bool GetForcePathStyle() => _forcePathStyle;
        public string? GetAccessKey() => _accessKey;
        public string? GetSecretKey() => _secretKey;
        public string? GetProxyHost() => _proxyHost;
        public int? GetProxyPort() => _proxyPort;
        public string? GetCacheControlHeader() => _cacheControlHeader;
        
        protected override IAmazonS3 GetS3Client()
        {
            // Save configuration settings for testing
            var config = GetConfig();
            
            if (config.UseCompatibleMode)
            {
                _serviceUrl = config.CompatibleEndpoint;
                _forcePathStyle = config.ForcePathStyle;
                _accessKey = config.CompatibleAccessKey;
                _secretKey = config.CompatibleSecretKey;
            }
            else
            {
                _accessKey = config.AccessKey;
                _secretKey = config.SecretKey;
            }
            
            _cacheControlHeader = config.CacheControlHeader;
            
            if (!string.IsNullOrEmpty(config.ProxyUrl))
            {
                try
                {
                    var proxyUri = new Uri(config.ProxyUrl);
                    _proxyHost = proxyUri.Host;
                    _proxyPort = proxyUri.Port > 0 ? proxyUri.Port : 80;
                }
                catch (UriFormatException)
                {
                    // Let base class handle the exception
                }
            }
            
            // Return mock
            return new Mock<IAmazonS3>().Object;
        }
        
        private S3Configuration GetConfig()
        {
            // Use reflection to access the protected _config field
            var field = typeof(S3Service).GetField("_config", 
                System.Reflection.BindingFlags.Instance | 
                System.Reflection.BindingFlags.NonPublic);
            
            return (S3Configuration)field.GetValue(this);
        }
    }
}
```

### 3.2. Create Integration Test Setup (Optional)

```cursor
// Create integration test class
mkdir IntegrationTests
touch IntegrationTests/S3ServiceIntegrationTests.cs
```

Edit `IntegrationTests/S3ServiceIntegrationTests.cs`:

```cursor
// Add integration test implementation
using AmazonS3Service.Interfaces;
using AmazonS3Service.Models;
using AmazonS3Service.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace AmazonS3Service.Tests.IntegrationTests;

[Trait("Category", "Integration")]
public class S3ServiceIntegrationTests
{
    private readonly IS3Service _s3Service;
    private readonly S3Configuration _config;
    
    public S3ServiceIntegrationTests()
    {
        // Set up real AWS integration for testing
        // Important: These tests will only run if proper credentials are configured
        // and should be excluded from CI/CD pipelines
        
        _config = new S3Configuration
        {
            BucketName = "your-test-bucket", // Use a dedicated test bucket
            Region = "us-east-1",
            UseInstanceProfile = true, // Use IAM role
            BaseKeyPrefix = "integration-tests/"
        };
        
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<S3Service>();
        
        _s3Service = new S3Service(Options.Create(_config), logger);
    }
    
    [Fact(Skip = "Integration test - only run manually")]
    public async Task UploadAndDownloadFile_ShouldSucceed()
    {
        // Arrange
        var testFileName = $"test-{Guid.NewGuid()}.txt";
        var testContent = "This is a test file for S3 integration testing.";
        var memoryStream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(testContent));
        var metadata = new Dictionary<string, string> { { "test", "integration" } };
        
        try
        {
            // Act - Upload
            var uploadResult = await _s3Service.UploadFileAsync(
                memoryStream, 
                testFileName, 
                "text/plain", 
                metadata);
            
            // Assert - Upload
            Assert.True(uploadResult.Success);
            Assert.Equal(_config.BucketName, uploadResult.BucketName);
            Assert.NotNull(uploadResult.Key);
            
            // Act - Download
            var downloadStream = await _s3Service.DownloadFileAsync(testFileName);
            
            // Assert - Download
            Assert.NotNull(downloadStream);
            
            using var streamReader = new StreamReader(downloadStream);
            var downloadedContent = await streamReader.ReadToEndAsync();
            Assert.Equal(testContent, downloadedContent);
        }
        finally
        {
            // Cleanup - always delete the test file
            await _s3Service.DeleteFileAsync(testFileName);
        }
    }
}
```

## 4. Deployment and Configuration

### 4.1. Configure AWS Resources

```cursor
// AWS CLI commands to create S3 bucket with appropriate permissions
aws s3api create-bucket --bucket your-app-bucket-name --region us-east-1

// Set up CORS configuration for the bucket
aws s3api put-bucket-cors --bucket your-app-bucket-name --cors-configuration file://cors-config.json

// Create an IAM user with appropriate permissions for the S3 bucket
aws iam create-user --user-name your-app-s3-user

// Create and attach policy to the IAM user
aws iam create-policy --policy-name YourAppS3Policy --policy-document file://s3-policy.json
aws iam attach-user-policy --user-name your-app-s3-user --policy-arn arn:aws:iam::YOUR_ACCOUNT_ID:policy/YourAppS3Policy

// Create access keys for the IAM user
aws iam create-access-key --user-name your-app-s3-user
```

Create `cors-config.json`:

```cursor
{
  "CORSRules": [
    {
      "AllowedHeaders": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedOrigins": ["https://your-app-domain.com"],
      "ExposeHeaders": ["ETag"]
    }
  ]
}
```

Create `s3-policy.json`:

```cursor
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-app-bucket-name",
        "arn:aws:s3:::your-app-bucket-name/*"
      ]
    }
  ]
}
```

### 4.2. Configure Application Settings

Add to `appsettings.json`:

```cursor
{
  "S3": {
    "BucketName": "your-app-bucket-name",
    "Region": "us-east-1",
    "AccessKey": "YOUR_ACCESS_KEY", 
    "SecretKey": "YOUR_SECRET_KEY",
    "UseInstanceProfile": false,
    "UseCompatibleMode": false,
    "CompatibleEndpoint": null,
    "CompatibleAccessKey": null,
    "CompatibleSecretKey": null,
    "ForcePathStyle": false,
    "ProxyUrl": null,
    "BaseKeyPrefix": "uploads/",
    "UploadPartSize": 5242880,
    "DefaultACL": "Private",
    "StorageClass": "Standard",
    "EncryptionMethod": "AES256",
    "CacheControlHeader": "max-age=86400"
  }
}
```

For S3-compatible storage services:

```cursor
{
  "S3": {
    "BucketName": "my-application-bucket",
    "Region": "us-east-1",
    "UseInstanceProfile": false,
    "UseCompatibleMode": true,
    "CompatibleEndpoint": "https://minio.example.com",
    "CompatibleAccessKey": "minio-access-key",
    "CompatibleSecretKey": "minio-secret-key",
    "ForcePathStyle": true,
    "BaseKeyPrefix": "uploads/",
    "CacheControlHeader": "no-cache"
  }
}
```

For environments requiring proxy:

```cursor
{
  "S3": {
    "BucketName": "my-application-bucket",
    "Region": "us-east-1",
    "AccessKey": "aws-access-key", 
    "SecretKey": "aws-secret-key",
    "ProxyUrl": "http://proxy.example.com:8080",
    "BaseKeyPrefix": "uploads/",
    "CacheControlHeader": "public, max-age=31536000"
  }
}
```

### 4.3. Register the S3 Service in Startup

In `Program.cs` or `Startup.cs`:

```cursor
// Register S3 Service
builder.Services.AddS3Service(builder.Configuration);

// OR with direct configuration
builder.Services.AddS3Service(options => {
    options.BucketName = "your-app-bucket-name";
    options.Region = "us-east-1";
    options.UseInstanceProfile = true; // For EC2/Lambda with IAM roles
});
```

## 5. Multi-part Upload Implementation for Large Files

For large file uploads (>100MB), implement multi-part upload functionality:

```cursor
// Create new method in S3Service.cs to handle multi-part uploads
public async Task<S3UploadResult> UploadLargeFileAsync(
    Stream fileStream,
    string key,
    string contentType,
    Dictionary<string, string>? metadata = null,
    IProgress<int>? progress = null,
    CancellationToken cancellationToken = default)
{
    try
    {
        _logger.LogInformation("Starting multi-part upload to S3. Key: {Key}", key);
        
        // Append base prefix if configured
        if (!string.IsNullOrEmpty(_config.BaseKeyPrefix))
        {
            key = $"{_config.BaseKeyPrefix.TrimEnd('/')}/{key.TrimStart('/')}";
        }
        
        // Create transfer utility for multi-part upload
        var transferUtility = new TransferUtility(_s3Client);
        
        // Configure upload request
        var uploadRequest = new TransferUtilityUploadRequest
        {
            BucketName = _config.BucketName,
            Key = key,
            InputStream = fileStream,
            ContentType = contentType,
            CannedACL = _config.DefaultACL,
            StorageClass = _config.StorageClass,
            PartSize = _config.UploadPartSize
        };
        
        // Add cache control header if configured
        if (!string.IsNullOrEmpty(_config.CacheControlHeader))
        {
            uploadRequest.CacheControl = _config.CacheControlHeader;
        }
        
        // Add metadata if provided
        if (metadata != null && metadata.Count > 0)
        {
            foreach (var item in metadata)
            {
                uploadRequest.Metadata.Add(item.Key, item.Value);
            }
        }
        
        // Add progress tracking if requested
        if (progress != null)
        {
            uploadRequest.UploadProgressEvent += (sender, e) =>
            {
                var percentDone = (int)((double)e.TransferredBytes / e.TotalBytes * 100);
                progress.Report(percentDone);
            };
        }
        
        // Execute upload
        await transferUtility.UploadAsync(uploadRequest, cancellationToken);
        
        // Build result
        var result = new S3UploadResult
        {
            Success = true,
            Key = key,
            BucketName = _config.BucketName,
            Metadata = metadata
        };
        
        _logger.LogInformation("Large file uploaded successfully to S3. Key: {Key}", key);
        return result;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error uploading large file to S3. Key: {Key}", key);
        return new S3UploadResult
        {
            Success = false,
            Key = key,
            BucketName = _config.BucketName,
            ErrorMessage = ex.Message,
            Exception = ex
        };
    }
}
```

## 6. Integration Examples

### 6.1 ASP.NET Core Application Integration

1. Add the S3 service to a web application
2. Create file upload controllers and endpoints
3. Implement client-side file upload with progress tracking
4. Add validation middleware for file uploads

### 6.2 Console Application Integration

```cursor
// Example of using S3 service in a console application
static async Task Main(string[] args)
{
    // Setup DI
    var services = new ServiceCollection();
    
    // Add configuration
    var configBuilder = new ConfigurationBuilder()
        .AddJsonFile("appsettings.json")
        .AddEnvironmentVariables();
    var config = configBuilder.Build();
    
    // Add services
    services.AddLogging(builder => builder.AddConsole());
    services.AddS3Service(config);
    
    // Build service provider
    var serviceProvider = services.BuildServiceProvider();
    
    // Get S3 service
    var s3Service = serviceProvider.GetRequiredService<IS3Service>();
    
    // Upload a file
    var filePath = "path/to/your/file.txt";
    var result = await s3Service.UploadFileAsync(
        filePath,
        Path.GetFileName(filePath));
        
    Console.WriteLine($"Upload success: {result.Success}");
    Console.WriteLine($"File key: {result.Key}");
}
```

## 7. Implementation Summary and Timeline

### 7.1 Implementation Phases

| Phase | Task | Timeline | Dependencies |
|-------|------|----------|--------------|
| 1 | Project Setup & Configuration | Week 1, Day 1-2 | None |
| 2 | Core Implementation (Models, Interface, Service) | Week 1, Day 3-5 | Phase 1 |
| 3 | S3-Compatible Storage Support | Week 2, Day 1-2 | Phase 2 |
| 4 | Proxy Configuration Support | Week 2, Day 2-3 | Phase 2 |
| 5 | Unit Testing | Week 2, Day 3-5 | Phase 2-4 |
| 6 | AWS Resource Configuration | Week 3, Day 1 | None |
| 7 | Multi-part Upload Implementation | Week 3, Day 2-3 | Phase 2-4 |
| 8 | Integration Examples | Week 3, Day 3-4 | Phase 2-7 |
| 9 | Documentation and Deployment Guide | Week 3, Day 4-5 | All previous phases |

### 7.2 Risk Assessment

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| AWS credential exposure | High | Medium | Use IAM roles, secret management, rotate keys |
| Performance issues with large files | Medium | Medium | Implement multi-part uploads, chunking, and progress tracking |
| Network connectivity issues | Medium | High | Implement retry policies, connection resilience |
| S3 costs exceeding budget | Medium | Low | Set up budget alerts, lifecycle policies |
| Security vulnerabilities | High | Low | Regular security audits, limit permissions |

### 7.3 Success Criteria

1. Successful upload of files up to 5GB
2. File upload success rate > 99.9%
3. Response time < 200ms for file operations (excluding transfer time)
4. Comprehensive test coverage > 85%
5. Proper error handling and logging
6. Secure implementation with no credential exposure

## 8. Next Steps

1. Implement the service following this technical plan
2. Set up CI/CD pipeline for automated testing and deployment
3. Create monitoring dashboards for S3 usage and performance
4. Develop user documentation for the S3 service
5. Consider expanding functionality for additional S3 features (lifecycle policies, replication, etc.)

## 13. Implementation Plan

1. Set up S3 bucket and IAM roles
2. Implement core S3 service
3. Add file validation
4. Implement multi-part upload support
5. Implement proper error handling and logging
6. Create unit and integration tests
7. Document usage examples
