using System.Globalization;
using System.Resources;

namespace KvFnB.Localization
{
    public static class LocalizationProvider 
    {
        private static readonly ResourceManager _resourceManager =  new("KvFnB.Localization.Resources.Localization", typeof(LocalizationProvider).Assembly);
        public static string GetLocalizedMessage(string key, params object[] parameters)
        {
            try {
                var culture = CultureInfo.CurrentCulture;
                var message = _resourceManager.GetString(key, culture);
                if (string.IsNullOrEmpty(message))
                {
                    return $"[{key}]"; // Key not found
                }
                return string.Format(message, parameters);
            } 
            catch
            {
                return key;
            }
          
        }

        public static IEnumerable<string> GetSupportedLanguages()
        {
            return ["vi", "en"];
        }
    }
}