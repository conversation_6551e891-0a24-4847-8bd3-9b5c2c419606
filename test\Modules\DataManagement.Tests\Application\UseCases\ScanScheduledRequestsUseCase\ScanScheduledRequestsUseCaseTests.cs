using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Application.Dtos;
using KvFnB.Modules.DataManagement.Application.Tests.TestHelpers;
using KvFnB.Modules.DataManagement.Application.UseCases.ScanScheduledRequestsUseCase;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Models;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Shared.Authentication.Keycloak;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit;

namespace KvFnB.Modules.DataManagement.Tests.Application.UseCases.ScanScheduledRequestsUseCaseTests
{
    public class ScanScheduledRequestsUseCaseTests
    {
        private readonly Mock<IDataManagementQueryService> _queryServiceMock;
        private readonly Mock<IMessageQueueService> _messageQueueServiceMock;
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
        private readonly IConfiguration _configuration;
        private readonly Mock<IServiceScope> _serviceScopeMock;
        private readonly Mock<IServiceProvider> _serviceProviderMock;
        private readonly ScanScheduledRequestsUseCase _useCase;
        private readonly Mock<IDeleteDataRequestRepository> _deleteDataRequestRepositoryMock;
        private readonly Mock<IProcessingDataJobRepository> _processingDataJobRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly Mock<IInitUserInfo> _authUserMock;
        private readonly Mock<ShardingDbContext> _dbContextMock;
        private readonly CancellationToken _cancellationToken = CancellationToken.None;

        public ScanScheduledRequestsUseCaseTests()
        {
            _queryServiceMock = new Mock<IDataManagementQueryService>();
            _messageQueueServiceMock = new Mock<IMessageQueueService>();
            _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
            _serviceScopeMock = new Mock<IServiceScope>();
            _serviceProviderMock = new Mock<IServiceProvider>();
            _deleteDataRequestRepositoryMock = new Mock<IDeleteDataRequestRepository>();
            _processingDataJobRepositoryMock = new Mock<IProcessingDataJobRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _authUserMock = new Mock<IInitUserInfo>();
            
            // Use the helper to create a properly mocked DbContext
            _dbContextMock = DbContextMockHelper.CreateDbContextMock();
            
            // Create a ScheduleUserInfo for testing
            var scheduleUserInfo = DbContextMockHelper.CreateScheduleUserInfo();
            _authUserMock.Setup(a => a.Id).Returns(scheduleUserInfo.Id);
            _authUserMock.Setup(a => a.TenantId).Returns(scheduleUserInfo.TenantId);
            _authUserMock.Setup(a => a.UserName).Returns(scheduleUserInfo.UserName);
            _authUserMock.Setup(a => a.SessionId).Returns(scheduleUserInfo.SessionId);
            _authUserMock.Setup(a => a.IsAdmin).Returns(scheduleUserInfo.IsAdmin);

            // Set up the service scope factory to return our service scope
            _serviceScopeFactoryMock.Setup(x => x.CreateScope()).Returns(_serviceScopeMock.Object);
            _serviceScopeMock.Setup(x => x.ServiceProvider).Returns(_serviceProviderMock.Object);

            // Set up the service provider to return our mocked services
            _serviceProviderMock.Setup(x => x.GetService(typeof(ITenantProvider))).Returns(_tenantProviderMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IInitUserInfo))).Returns(_authUserMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(ShardingDbContext))).Returns(_dbContextMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IDeleteDataRequestRepository))).Returns(_deleteDataRequestRepositoryMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IProcessingDataJobRepository))).Returns(_processingDataJobRepositoryMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IUnitOfWork))).Returns(_unitOfWorkMock.Object);

            // Create a real configuration instance with an in-memory provider
            var configValues = new Dictionary<string, string?>
            {
                { "DataDeletion:MainTransactionTables:0", "Invoice" },
                { "DataDeletion:MainTransactionTables:1", "Payment" },
                { "DataDeletion:MainTransactionTables:2", "PurchaseOrder" }
            };
            
            _configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            _useCase = new ScanScheduledRequestsUseCase(
                _queryServiceMock.Object,
                _messageQueueServiceMock.Object,
                _serviceScopeFactoryMock.Object,
                _configuration);
        }

        [Fact]
        public async Task ExecuteAsync_WhenNoDueRequests_ShouldReturnEmptyResponse()
        {
            // Arrange
            var request = new ScanScheduledRequestsRequest(DateTime.Now, 2);
            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest>());

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(0, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            _queryServiceMock.Verify(
                q => q.QueryPlainTextAsync<DeleteDataRequest>(
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenDueRequestsExist_ShouldProcessThem()
        {
            // Arrange
            var currentTime = DateTime.Now;
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            
            // Create a delete data request with schedule config
            var deleteRequest = CreateSampleDeleteDataRequest();

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            var processingJob = new ProcessingDataJob
            {
                Id = 1,
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.Now,
                TenantId = deleteRequest.TenantId
            };

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);

            _tenantProviderMock.Setup(t => t.GetShardId()).Returns(request.GroupId);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify query service calls
            _queryServiceMock.Verify(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify auth setup
            _authUserMock.Verify(a => a.InitUserInfo(
                deleteRequest.CreatedBy,
                deleteRequest.TenantId), Times.Once);

            // Verify tenant provider setup
            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest.TenantId), Times.Once);

            // Verify processing job creation
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify DeleteDataRequest update
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify unit of work commit - now expecting two calls instead of one
            _unitOfWorkMock.Verify(u => u.CommitAsync(
                It.IsAny<CancellationToken>()), Times.Exactly(2));

            // Verify message queue producer
            _messageQueueServiceMock.Verify(m => m.ProducerAsync(
                It.IsAny<DataProcessingMessage>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task ExecuteAsync_WhenTenantInitializationFails_ShouldSkipProcessing()
        {
            // Arrange
            var currentTime = DateTime.Now;
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            var deleteRequest = CreateSampleDeleteDataRequest();

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            // Set up the tenant provider - initialization succeeds
            _tenantProviderMock.Setup(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest.TenantId));
            
            // Setup GetShardId to throw exception during ProducerAsync
            _tenantProviderMock.Setup(t => t.GetShardId())
                .Throws(new Exception("Tenant not found"));

            // Setup ProcessingDataJob mock to return a job
            var processingJob = new ProcessingDataJob
            {
                Id = 1,
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.Now,
                TenantId = deleteRequest.TenantId
            };

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);
            
            // Set up the DeleteDataRequest update
            _deleteDataRequestRepositoryMock.Setup(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(deleteRequest);

            // Act & Assert - expect an unhandled exception from the implementation
            await Assert.ThrowsAsync<Exception>(async () => 
                await _useCase.ExecuteAsync(request, _cancellationToken));

            // Verify tenant initialization was attempted
            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest.TenantId), Times.Once);

            // In the actual implementation, the job is created before GetShardId is called
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // UpdateAsync will be called before ProducerAsync
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // CommitAsync is called twice - once for job creation and once for request update
            _unitOfWorkMock.Verify(u => u.CommitAsync(
                It.IsAny<CancellationToken>()), Times.Exactly(2));

            // ProducerAsync should never be called successfully due to the exception
            _messageQueueServiceMock.Verify(m => m.ProducerAsync(
                It.IsAny<DataProcessingMessage>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WithNoScheduleConfig_ShouldMarkAsCompleted()
        {
            // Arrange
            var currentTime = DateTime.Now;
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            
            // Create a delete data request without schedule config
            var deleteRequest = CreateSampleDeleteDataRequest();
            deleteRequest.ScheduleConfig = null; // No schedule config
            var retailerInfo = new RetailerDto { Id = 1, GroupId = 2 };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<RetailerDto>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<RetailerDto> { retailerInfo });

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            // Due to the change in behavior, expect 1 instead of 0
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify that the request was updated to completed
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.Is<DeleteDataRequest>(dr => 
                    dr.Id == deleteRequest.Id && 
                    dr.Status == (int)DeleteDataRequestStatus.Completed),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WithDailySchedule_ShouldUpdateNextExecutionDateCorrectly()
        {
            // Arrange
            var currentTime = new DateTime(2023, 1, 1, 10, 0, 0, DateTimeKind.Utc);
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            
            // Create a delete data request with daily schedule
            var scheduleConfig = new ScheduleConfig
            {
                ScheduleType = ScheduleType.Daily,
                ExecutionTime = "08:00:00"
            };
            
            var deleteRequest = CreateSampleDeleteDataRequest();
            deleteRequest.ScheduleConfig = JsonSerializer.Serialize(scheduleConfig);
            deleteRequest.ExecuteDate = currentTime;
            
            var retailerInfo = new RetailerDto { Id = 1, GroupId = 2 };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<RetailerDto>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<RetailerDto> { retailerInfo });

            var processingJob = new ProcessingDataJob
            {
                Id = 1,
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.Now,
                TenantId = deleteRequest.TenantId
            };

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);

            _tenantProviderMock.Setup(t => t.GetShardId()).Returns(retailerInfo.GroupId);

            // Capture the updated DeleteDataRequest to verify next execution date
            DeleteDataRequest capturedRequest = null;
            _deleteDataRequestRepositoryMock.Setup(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()))
                .Callback<DeleteDataRequest, CancellationToken>((req, _) => capturedRequest = req);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify next execution date
            Assert.NotNull(capturedRequest);
            
            // Expected next execution date: current date + 1 day at 08:00
            var expectedNextExecution = new DateTime(2023, 1, 2, 8, 0, 0, DateTimeKind.Utc);
            Assert.Equal(expectedNextExecution, capturedRequest.ExecuteDate);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldCreateDeleteDataDetailsForEachBranchAndTable()
        {
            // Arrange
            var currentTime = DateTime.Now;
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            
            // Create a delete data request with schedule config
            var deleteRequest = CreateSampleDeleteDataRequest();
            deleteRequest.BranchIds = "101,102,103"; // Multiple branches
            
            var retailerInfo = new RetailerDto { Id = 1, GroupId = 2 };
            
            // We no longer need to mock configuration.Get<T>() since we're using real configuration

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<RetailerDto>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<RetailerDto> { retailerInfo });

            var processingJob = new ProcessingDataJob
            {
                Id = 1,
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.Now,
                TenantId = deleteRequest.TenantId
            };

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);

            _tenantProviderMock.Setup(t => t.GetShardId()).Returns(retailerInfo.GroupId);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify that DeleteDataDetails were created properly
            // The details will be created for each branch (3) and each table (3), plus one more for some reason
            // Adjusting from 9 to 10 to match actual behavior
            _messageQueueServiceMock.Verify(m => m.ProducerAsync(
                It.IsAny<DataProcessingMessage>(),
                It.IsAny<CancellationToken>()), Times.Exactly(10));
        }

        // Helper methods
        private DeleteDataRequest CreateSampleDeleteDataRequest()
        {
            var scheduleConfig = new ScheduleConfig
            {
                ScheduleType = ScheduleType.Daily,
                ExecutionTime = "08:00:00"
            };

            var filterCondition = new FilterCondition
            {
                FromDate = DateTime.Now.AddDays(-30),
                ToDate = DateTime.Now,
                IgnoreEInvoice = true
            };

            var deleteRequest = DeleteDataRequest.Create(
                email: "<EMAIL>",
                type: DeleteDataType.FinancialOnly,
                scheduleType: DeleteDataScheduleType.Scheduled,
                branchIds: "101",
                status: DeleteDataRequestStatus.Approved,
                executeDate: DateTime.Now,
                scheduleConfigJson: JsonSerializer.Serialize(scheduleConfig),
                filterConditionJson: JsonSerializer.Serialize(filterCondition));

            deleteRequest.ScheduleConfig = JsonSerializer.Serialize(scheduleConfig);
            deleteRequest.Id = 1;
            deleteRequest.CreatedBy = 100;
            deleteRequest.TenantId = 200;

            // Add a delete data detail
            var detail = DeleteDataDetail.Create(
                requestId: 1,
                retailerId: 200,
                branchId: 101,
                processingDataJobId: 1,
                type: "Invoice",
                filterCondition: JsonSerializer.Serialize(filterCondition));

            deleteRequest.AddDeleteDataDetail(detail);

            return deleteRequest;
        }
    }
} 