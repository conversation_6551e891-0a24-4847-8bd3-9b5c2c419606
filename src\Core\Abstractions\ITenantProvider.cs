namespace KvFnB.Core.Abstractions
{
    public interface ITenantProvider
    {
        void InitTenantInfo(int shard, int tenantId);
        int? GetTenantId();

        int? GetShardId();

        long? GetBranchId();
        
        /// <summary>
        /// Gets the current retailer code
        /// </summary>
        /// <returns>The retailer code, or null if not available</returns>
        string? GetRetailerCode();
    }
}